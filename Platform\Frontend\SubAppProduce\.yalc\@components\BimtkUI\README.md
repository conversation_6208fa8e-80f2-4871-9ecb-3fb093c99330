# @components/BimtkUI

一个基于 Vue 2.x 的 UI 组件库，集成了 vxe-table 和 element-ui，提供高度可定制的表格组件和上传组件。

!!!使用前，请确保你的项目安装了portal-vue@2.1.6 (不推荐使用更高版本，可能会报错)

## 安装

### 1. 配置私有仓库

在项目根目录创建或编辑 `.npmrc` 文件，添加以下配置：

```bash
# Set URL for your scoped packages.
# For example package with name `@foo/bar` will use this URL for download
@components:registry=https://git.jgsteel.cn/api/v4/packages/npm/

# Add the token for the scoped packages URL. Replace <your_project_id>
# with the project where your package is located.
//git.jgsteel.cn/api/v4/packages/npm/:_authToken=YZu-pXWokgm29N4VgS-H

```

### 2. 安装依赖

```bash
npm install @components/BimtkUI --save
```

## 使用

### 全局注册

```javascript
import Vue from 'vue'
import BimtkUI from '@components/BimtkUI'

Vue.use(BimtkUI, {
  baseURL: 'https://your-api-url/SYS' // 设置表格组件的API基础URL(注意，这里实际要写到baseURL的下一级，有些系统里面用的是SYS，有些用的是Platform)
  tablePageSizes: [10, 20, 50, 100],  // 设置表格组件的分页大小选项（默认[10, 20, 50, 100]）
  tablePageSize: 20 // 设置表格组件的默认分页大小（默认20）
})
```

### 可配置全局参数
| 参数名称           | 说明                         |
|----------------|----------------------------|
| baseURL       | table配置的baseURL            |
| tablePageSizes | table分页列表，默认[10,20,50,100] |
| tablePageSize  | table默认分页大小，默认20           |


## 组件说明

### Table 组件

基于 vxe-table 封装的高性能表格组件


## 依赖说明

本组件库依赖以下核心库：

- Vue ^2.6.14
- vxe-table ^3.0.0
- element-ui ^2.15.9
- vuedraggable ^2.24.3
- moment ^2.29.1
- axios ^1.6.7
- js-cookie 2.2.0
- decimal.js ^10.5.0
- numeral ^2.0.6
- portal-vue: "2.1.6",

## 开发说明

### 构建

```bash
npm run build
```

### 发布

```bash
npm run publish
```

## 注意事项

- 组件仅支持 Vue 2.x 版本
- 发布前请确保已更新版本号
- 确保有仓库的发布权限

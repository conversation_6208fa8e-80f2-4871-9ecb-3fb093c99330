{"name": "@components/BimtkUI", "version": "1.1.14", "scripts": {"dev": "rollup -c -w", "build": "rollup -c", "test": "jest", "publish": "npm publish --registry=https://git.jgsteel.cn/api/v4/projects/231/packages/npm/", "watch:yalc": "nodemon --watch src -e vue,js,scss --exec \"npm run build && yalc push\""}, "publishConfig": {"@components:registry": "https://git.jgsteel.cn/api/v4/projects/231/packages/npm/"}, "main": "dist/bimtk-ui.umd.js", "module": "dist/bimtk-ui.esm.js", "files": ["dist", "src", "static"], "peerDependencies": {"vue": "^2.6.14", "vxe-table": "^3.0.0", "vuedraggable": "^2.24.3", "moment": "^2.29.1", "axios": "^1.6.7", "js-cookie": "2.2.0", "decimal.js": "^10.5.0", "numeral": "^2.0.6", "ali-oss": "^6.16.0", "portal-vue": "2.1.6"}, "dependencies": {"element-ui": "^2.15.9", "global": "^4.4.0", "xlsx": "^0.18.5"}, "yalcSig": "e24e6e3907d29b99249f43b16a41f951"}
import table from './table/index.vue'
import upload from './upload/index.vue'
import uploadTy from './upload-ty/index.vue'
import tree from './tree/index.vue'
import noData from './no-data/index.vue'

const components = {
  table,
  upload,
  tree,
  uploadTy,
  noData
}

const install = function(Vue, options = {}) {
  // 全局配置
  Vue.prototype.$BimtkUIConfig = {
    baseURL: options.baseURL,
    tablePageSizes: options.tablePageSizes,
    tablePageSize: options.tablePageSize,
  }

  Object.values(components).forEach(component => {
    Vue.component(component.name, component)
  })
}

export default {
  install,
  ...components
}

if (typeof window !== 'undefined' && window.Vue) {
  window.Vue.use(install)
}

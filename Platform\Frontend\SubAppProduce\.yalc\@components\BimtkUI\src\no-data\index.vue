<template>
  <div class="bt-no-data" :style="containerStyle">
    <div class="no-data-content">
      <img :src="currentImageSrc" :style="imageStyle" alt="no-data">
      <div v-if="showText" class="no-data-text">
        <template v-if="$scopedSlots.default">
          <slot name="default" />
        </template>
        <template v-else>
          {{ text }}
        </template>
      </div>
    </div>
  </div>
</template>

<script>
// 定义可用的图片类型
const imageTypes = [
  '404',
  'address',
  'auth',
  'bim',
  'cad',
  'contact',
  'default',
  'favor',
  'message',
  'network',
  'notice',
  'phone',
  'picture',
  'project',
  'publish',
  'search',
  'surveillance'
]

export default {
  name: 'BtNoData',
  props: {
    // 占位图类型
    type: {
      type: String,
      default: 'default',
      validator: function(value) {
        // 验证是否为预设类型之一或自定义图片路径
        return imageTypes.includes(value) || value.includes('/')
      }
    },
    // 自定义图片路径
    image: {
      type: String,
      default: ''
    },
    // 图片宽度
    width: {
      type: [Number, String],
      default: 400
    },
    // 图片高度
    height: {
      type: [Number, String],
      default: 266
    },
    // 提示文本
    text: {
      type: String,
      default: '暂无数据'
    },
    // 是否显示文本
    showText: {
      type: Boolean,
      default: true
    },
    // 容器样式
    containerStyle: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 计算当前应该显示的图片路径
    currentImageSrc() {
      // 如果提供了自定义图片，优先使用自定义图片
      if (this.image) {
        return this.image
      }
      
      // 如果type是完整路径，直接返回
      if (this.type.includes('/')) {
        return this.type
      }
      
      // 否则构造预设图片路径
      const imageName = imageTypes.includes(this.type) ? this.type : 'default'

      return `https://001-app.oss-cn-hangzhou.aliyuncs.com/images/${imageName}.svg`
    },
    // 计算图片样式
    imageStyle() {
      // 处理宽度：如果是数字或纯数字字符串，添加px单位
      let width = this.width
      if (typeof width === 'number' || (typeof width === 'string' && /^\d+$/.test(width))) {
        width = `${width}px`
      }
      
      // 处理高度：如果是数字或纯数字字符串，添加px单位
      let height = this.height
      if (typeof height === 'number' || (typeof height === 'string' && /^\d+$/.test(height))) {
        height = `${height}px`
      }
      
      return {
        width,
        height
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bt-no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  
  .no-data-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    img {
      display: block;
      margin-bottom: 16px;
    }
    
    .no-data-text {
      font-size: 14px;
      color: #909399;
      text-align: center;
    }
  }
}
</style>
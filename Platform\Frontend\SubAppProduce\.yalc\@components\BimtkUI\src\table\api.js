import request from '../utils/request'

// 获取系统表格样式(code)
export function GetGridByCode(data) {
    return request({
        url: '/Grid/GetGridByCode',
        method: 'post',
        data
    })
}

// 更用用户表格样式(code)
export function UpdateColumnSetting(data) {
    return request({
        url: '/Grid/UpdateColumnSeting',
        method: 'post',
        data
    })
}

// 获取用户表格样式(code)
export function GetFullDisplayColumnGridByCode(data) {
    return request({
        url: '/Grid/GetFullDisplayColumnGridByCode',
        method: 'post',
        data
    })
}



<template>
  <div class="CustomTable">
    <portal v-if="customTableObj.btnName" :to="customTableObj.btnName">
      <el-popover v-model="popoverVisible" placement="bottom-end" width="300" trigger="click" @hide="getTableFUllColumns" >
        <div style="text-align: right; margin: 0">
          <el-button size="mini" type="text" @click="handleCancelTbSet">取消</el-button>
          <el-button type="primary" size="mini" @click="handleSaveTbSet">确定</el-button>
        </div>
        <div class="popover-container">
          <div class="title">左侧固定</div>
          <draggable v-model="freezeColumnLeft" handle=".drag-btn" class="popover-content" group="column">
            <template v-for="item in freezeColumnLeft">
              <div :key="item.Id" class="item">
                <el-checkbox
                  v-model="item.Is_Display"
                  :disabled="item.Is_Edit && item.Is_Must_Input"
                >{{ item.Display_Name }}</el-checkbox>
                <el-button class="drag-btn" style="cursor: move" size="mini" round>拖动</el-button>
              </div>
            </template>
          </draggable>
          <div class="title">不固定</div>
          <draggable v-model="commonColumn" handle=".drag-btn" class="popover-content" group="column">
            <template v-for="item in commonColumn">
              <div :key="item.Id" class="item">
                <el-checkbox
                  v-model="item.Is_Display"
                  :disabled="item.Is_Edit && item.Is_Must_Input"
                >{{ item.Display_Name }}</el-checkbox>
                <el-button class="drag-btn" style="cursor: move" size="mini" round>拖动</el-button>
              </div>
            </template>
          </draggable>
          <div class="title">右侧固定</div>
          <draggable v-model="freezeColumnRight" handle=".drag-btn" class="popover-content" group="column">
            <template v-for="item in freezeColumnRight">
              <div :key="item.Id" class="item">
                <el-checkbox
                  v-model="item.Is_Display"
                  :disabled="item.Is_Edit && item.Is_Must_Input"
                >{{ item.Display_Name }}</el-checkbox>
                <el-button class="drag-btn" style="cursor: move" size="mini" round>拖动</el-button>
              </div>
            </template>
          </draggable>
        </div>
        <el-button slot="reference" style="margin-left: 10px;display: inline-block;">表格配置</el-button>
      </el-popover>
    </portal>
    <div v-loading="customTableObj && (gridLoading || loading || customTableObj.loading)" class="table" :style="customTableObj.height ? {height:customTableObj.height || '100%'} : {flex:1}">
      <vxe-table
        v-if="customTableObj && !gridLoading"
        ref="table"
        keep-source
        resizable
        auto-resize
        :class="customTableObj.mergeCells?'cs-vxe-table merge-table ':'cs-vxe-table column-table'"
        :data="tableData"
        :fit="true"
        :tree-config="customTableObj.treeConfig?customTableObj.treeConfig:{children: 'Children', iconOpen: 'vxe-icon-square-minus',
                                                                           iconClose: 'vxe-icon-square-plus'}"
        :stripe="customTableObj.stripe===false?false:true"
        :column-config="{ useKey: true }"
        :row-config="{ useKey: true,isCurrent: true,isHover: true, keyField: customTableObj.keyField }"
        :row-style="customTableObj.rowStyle"
        :edit-rules="customTableObj.validRules"
        :show-footer="customTableObj.showFooter"
        :footer-method="footerMethod || handleFooterMethod"
        style="width: 100%;height:0"
        height="100%"
        :sort-config="{remote:customTableObj.sortRemote!==false}"
        :merge-cells="customTableObj.mergeCells"
        :row-class-name="rowClassName"
        :checkbox-config="checkboxConfig"
        :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
        @sort-change="changeTableSort"
        @cell-dblclick="cellClickEvent"
        @cell-click="(e)=>$emit('cell-click',e)"
        @checkbox-change="select"
        @checkbox-all="selectAll"
        :scroll-x="{enabled: false}"
        :scroll-y="{enabled: true}"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
      >
        <template>
          <template v-for="(item, index) in customTableObj.tableColumns">
            <!-- <render-group :column="item" :index="index" :key="index" /> -->
            {{ $scopedSlots }}
            <template v-if="item.children && item.children.length > 0">
              <render-group :key="index" :column="item" :index="index" />
            </template>
            <template v-else>
              <render-column
                :key="index"
                :column="item"
                :index="index"
                v-on="$listeners"
              >
                <!-- 透传所有具名插槽 -->
                <template v-for="(_, slotName) in $scopedSlots" #[slotName]="slotProps">
                  <slot :name="slotName" v-bind="slotProps" />
                </template>
              </render-column>
            </template>
          </template>
          <vxe-column
            v-if="(customTableObj.tableActions && customTableObj.tableActions.length > 0) || (customTableObj && customTableObj.operateOptions && customTableObj.operateOptions.isShow)"
            :width="customTableObj.operateOptions?customTableObj.operateOptions.width:150"
            title="操作"
            v-bind="customTableObj.operateOptions"
            fixed="right"
          >
            <template #default="{row,rowIndex}">
              <el-button
                v-for="(item, index) in customTableObj.tableActions"
                v-show="getenavl(item,row)"
                :key="index"
                v-bind="item.otherOptions"
                style="font-size:14px"
                @click="item.onclick(rowIndex, row)"
              >{{ item.actionLabel }}</el-button>
            </template>
          </vxe-column>
          <vxe-column v-if="$scopedSlots.actions" title="操作" fixed="right" :width="customTableObj.operateOptions?customTableObj.operateOptions.width:150">
            <template #default="{row,rowIndex}">
              <slot v-bind="{ row, rowIndex }" name="actions" />
            </template>
          </vxe-column>

        </template>
        <template #empty>
            <div style="text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center;">
              <div style="width: 100%; height: 70%; max-width: 400px; max-height: 266px; background-image: url(&quot;data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='400' height='266' viewBox='0 0 400 266'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:none%7D.f%7Bfill:url(%23e)%7D.g%7Bfill:url(%23f)%7D.l%7Bfill:url(%23m)%7D%3C/style%3E%3CclipPath id='a'%3E%3Cpath class='a' transform='translate(-.424 -.313)' d='M0 0h400v133H0z'/%3E%3C/clipPath%3E%3ClinearGradient id='b' x1='.5' y1='1.351' x2='.5' y2='-.755' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.39' stop-color='%23fff' stop-opacity='0'/%3E%3Cstop offset='.91' stop-color='%239bcaff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='.499' y1='1.028' x2='.5' y2='.039' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23bfd4f3'/%3E%3Cstop offset='1' stop-color='%23e7edf8'/%3E%3C/linearGradient%3E%3ClinearGradient id='d' x1='.507' y1='1.397' x2='.486' y2='-.106' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23c9daf4'/%3E%3C/linearGradient%3E%3ClinearGradient id='e' x1='.5' y1='1.033' x2='.5' y2='.035' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23fefefe'/%3E%3C/linearGradient%3E%3ClinearGradient id='f' x1='.5' y1='1.039' x2='.5' y2='.029' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23cbd8ee'/%3E%3Cstop offset='1' stop-color='%23cfdcef'/%3E%3C/linearGradient%3E%3ClinearGradient id='i' y1='1.032' y2='.038' xlink:href='%23e'/%3E%3ClinearGradient id='j' y1='1.036' y2='.036' xlink:href='%23f'/%3E%3ClinearGradient id='k' x1='.507' y1='2.589' x2='.496' y2='-.667' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.52' stop-color='%23c9daf4'/%3E%3Cstop offset='.92' stop-color='%23fff'/%3E%3C/linearGradient%3E%3ClinearGradient id='l' y1='.5' x2='1' y2='.5' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e0e9f7'/%3E%3Cstop offset='1' stop-color='%23e9eef9'/%3E%3C/linearGradient%3E%3ClinearGradient id='m' y1='.5' x2='1' y2='.5' xlink:href='%23c'/%3E%3ClinearGradient id='n' x2='.999' xlink:href='%23l'/%3E%3ClinearGradient id='o' x1='-.001' y1='.502' x2='1' y2='.502' xlink:href='%23c'/%3E%3ClinearGradient id='p' x2='1.001' xlink:href='%23l'/%3E%3ClinearGradient id='r' x1='.5' y1='1.351' y2='-.755' xlink:href='%23c'/%3E%3C/defs%3E%3Cpath class='a' d='M0 0h400v266H0z'/%3E%3Cg transform='translate(.424 133.313)' clip-path='url(%23a)'%3E%3Cpath d='M182.733 0c.807 0 1.614 0 2.543.013 99.682.94 180.191 60.074 180.191 132.887 0 73.4-81.813 132.9-182.733 132.9S0 206.294 0 132.9 81.813 0 182.733 0z' transform='translate(17.01 -.434)' fill='url(%23b)'/%3E%3C/g%3E%3Cg transform='translate(136.21 67.996)'%3E%3Cpath d='M386.493 135.75h-91.046a9.811 9.811 0 0 0-9.787 9.787v1.963h19.575v80.264a16.891 16.891 0 0 0 16.843 16.836h63.648a7.625 7.625 0 0 0 7.607-7.6v-94.4a6.876 6.876 0 0 0-6.84-6.85z' transform='translate(-285.648 -135.75)' fill='url(%23c)'/%3E%3Cpath d='M295.391 135.75h.048a9.751 9.751 0 0 1 9.775 9.733v2H285.64v-2a9.751 9.751 0 0 1 9.751-9.733z' transform='translate(-285.64 -135.75)' fill='url(%23d)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 18.586)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 23.725)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 34.705)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 39.844)'/%3E%3Crect width='32.099' height='5.091' rx='2.546' transform='translate(37.394 51.77)' fill='url(%23i)'/%3E%3Crect width='30.68' height='1.845' rx='.922' transform='translate(38.107 56.909)' fill='url(%23j)'/%3E%3Cpath d='M353.011 294.44s1.605 14.118-9.751 13.807h82.959s9.871-.1 9.757-13.807z' transform='translate(-308.747 -199.387)' fill='url(%23k)'/%3E%3C/g%3E%3Cg transform='translate(284.941 175.523)'%3E%3Cellipse cx='6.199' cy='1.875' rx='6.199' ry='1.875' transform='translate(2.713 16.199)' fill='url(%23l)'/%3E%3Cpath class='l' d='M12.099 6.081a6.05 6.05 0 1 0-10.38 4.193 2.4 2.4 0 0 0-.377 1.288 2.438 2.438 0 0 0 2.438 2.432h1.743v3.528a.533.533 0 1 0 1.06 0v-3.528h1.749a2.438 2.438 0 0 0 2.432-2.432 2.433 2.433 0 0 0-.371-1.288 6.032 6.032 0 0 0 1.706-4.193z'/%3E%3C/g%3E%3Cg transform='translate(123.952 163.037)'%3E%3Cellipse cx='4.283' cy='1.294' rx='4.283' ry='1.294' transform='translate(1.893 11.237)' fill='url(%23n)'/%3E%3Cpath d='M280.266 298.631a4.193 4.193 0 1 0-7.188 2.917 1.689 1.689 0 0 0-.257.892 1.7 1.7 0 0 0 1.689 1.689h1.2v2.45a.371.371 0 0 0 .737 0v-2.45h1.2a1.7 1.7 0 0 0 1.689-1.689 1.69 1.69 0 0 0-.258-.893 4.193 4.193 0 0 0 1.192-2.917z' transform='translate(-271.88 -294.421)' fill='url(%23o)'/%3E%3C/g%3E%3Cg transform='translate(100.28 180.825)'%3E%3Cellipse cx='5.205' cy='1.575' rx='5.205' ry='1.575' transform='translate(2.276 13.586)' fill='url(%23p)'/%3E%3Cpath class='l' d='M10.159 5.082a5.079 5.079 0 1 0-8.721 3.534A2.048 2.048 0 0 0 1.121 9.7a2.055 2.055 0 0 0 2.049 2.049h1.47v2.959a.444.444 0 0 0 .887 0v-2.959h1.45A2.055 2.055 0 0 0 9.02 9.7a2.044 2.044 0 0 0-.311-1.084 5.056 5.056 0 0 0 1.456-3.534z'/%3E%3C/g%3E%3Cpath d='M524.958 133.034c0-5.744-7.966-10.4-17.8-10.4s-17.8 4.654-17.8 10.4c0 4.744 5.511 8.518 12.854 10a3.139 3.139 0 0 1 2.21 4.6v.036a.18.18 0 0 0 .228.246c7.739-3.282 11.944-5.553 14.232-7.044 3.718-1.914 6.076-4.717 6.076-7.838zm-9.41-2.174a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.386 0a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.3 4.355a2.186 2.186 0 1 1 2.4-2.18 2.294 2.294 0 0 1-2.4 2.174z' transform='translate(-230.157 -67.285)' fill='url(%23r)'/%3E%3C/svg%3E&quot;); background-size: contain; background-repeat: no-repeat; background-position: center center; margin: 0px auto;"></div>
            <p>暂无内容</p>
          </div>
        </template>
      </vxe-table>
    </div>

    <div v-if="!customTableObj.disablidPagination" class="pagination">
      <div class="box">
        <template v-if="$scopedSlots.leftPagination">
          <slot name="leftPagination" v-bind="{selections}" />
        </template>
        <template v-else>
          <div>
            <el-tag
              v-if="customTableObj.checkbox"
              size="medium"
              class="info-x"
            >已选{{ selections.length }}条数据
            </el-tag>
          </div>
        </template>
        <el-pagination
          v-if="customTableObj.total"
          :total="customTableObj.total"
          :page-sizes="customTableObj.pageSizeOptions || pageSizes"
          :current-page.sync="customTableObj.currentPage"
          :page-size.sync="customTableObj.pageSize || pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { formatCurrency } from '../utils/index'
import RenderColumn from './renderColumn.vue'
import RenderGroup from './renderGroup.vue'
import { GetFullDisplayColumnGridByCode, GetGridByCode, UpdateColumnSetting } from './api'
import draggable from 'vuedraggable'
import { Portal } from 'portal-vue'

export default {
  name:'BtTable',
  components: {
    draggable,
    RenderGroup,
    renderDom: {
      functional: true,
      props: {
        render: Function
      },
      render(createElement, renDom) {
        return <div>{renDom.props.render()}</div>
      }
    },
    'render-column': RenderColumn,
    Portal
  },
  props: {
    config:{
      type: Object,
      default: () => {
        return {}
      }
    },
    gridDataHandler: {
      type: Function,
      default: null
    },
    configDataHandler: {
      type: Function,
      default: null
    },
    customTableConfig: {
      type: Object,
      default: () => {
        return {
          currentPage:1,
          pageSize:20,
          tableColumns: [],
          tableActions: [],
          operateOptions: {
            width: 120, // 操作栏宽度
            align: 'center',
            isShow: false // 是否显示,当tableActions有值时，此值无效，强制限制操作列
          },
          checkbox: false,
          treeConfig:{
            transform:true //自动将列表转为树结构（支持虚拟滚动）.这里默认设置为true，不然不支持虚拟滚动
          },
          keyField:'Id'
        }
      }
    },
    // gridCode
    code: {
      type: String,
      default: ''
    },
    // 自定义表格底部合计方法
    footerMethod: {
      type: Function,
      default: null
    },
    // 加载中
    loading: {
      type: Boolean,
      default: false
    },
    headerCellStyle: {
      type: Function,
      default: () => { return '' }   // 表头单元格样式
    },
    cellStyle: {
      type: Function,
      default: () => { return '' }   // 单元格样式
    },
  },
  data() {
    return {
      tableHeight: null, // 页面高度
      selections: [],
      gridConfig: null,
      checkboxConfig: {
        checkMethod: (row) => {
          return row.row.IscheckMethod !== undefined ? row.row.IscheckMethod : true
        }
      },
      gridLoading: false,
      popoverVisible: false,
      freezeColumnLeft: [],
      freezeColumnRight: [],
      commonColumn: [],
      pageSizes: this.$BimtkUIConfig?.tablePageSizes || [20, 50, 100, 200, 500, 1000, 2000, 5000],
      pageSize: this.$BimtkUIConfig?.tablePageSize || 20
    }
  },

  computed: {
    customTableObj() {
      return this.code && this.gridConfig ? {
        ...this.customTableConfig,
        ...this.config,
        ...this.gridConfig
      } : {
        ...this.customTableConfig,
        ...this.config
      }
    },
    tableData() {
      const data = []
      return [].concat(data, this.customTableObj?.tableData)
    }
  },
  watch: {
    code: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getGridByCode()
          this.getTableFUllColumns()
        } else {
          this.gridConfig = null
        }
      }
    },
    tableData:{
      deep: true,
      handler(newVal) {
        console.log({tableData:this.tableData})
        this.selections = []
        this.$emit('select-change', [])
      }
    }
  },

  methods: {
    async getTableFUllColumns() {
      if(!this.customTableObj.btnName){
        return
      }
      const res = await GetFullDisplayColumnGridByCode({code: this.code})
      let data = res.Data
      if (this.configDataHandler) {
        data = await this.configDataHandler(data)
      }
      const tableAllData = data
      const tableAllColumns = tableAllData.ColumnList
      let tableAllColumnsRes = []
      if (this.manualHideColumns) {
        tableAllColumnsRes = tableAllColumns.filter((item) => {
          return !this.manualHideColumns.some((d) => d.Code === item.Code)
        })
      } else {
        tableAllColumnsRes = tableAllColumns
      }
      // 替换列

      this.freezeColumnLeft = tableAllColumnsRes.filter((item) => item.Is_Frozen && (item.Frozen_Dirction === 'left' || !item.Frozen_Dirction))
      this.freezeColumnRight = tableAllColumnsRes.filter((item) => item.Is_Frozen && item.Frozen_Dirction === 'right')
      this.commonColumn = tableAllColumnsRes.filter((item) => !item.Is_Frozen)
      this.$emit('fetched')
    },
    handleCancelTbSet() {
      this.popoverVisible = false
      this.getTableFUllColumns()
    },
    /**
     * 保存表格设置
     */
    handleSaveTbSet() {
      const columnList = [
        ...this.freezeColumnLeft.map((item) => {
          return {
            ...item,
            Is_Frozen: true,
            Frozen_Dirction: 'left'
          }
        }),
        ...this.commonColumn.map((item) => {
          return {
            ...item,
            Is_Frozen: false
          }
        }),
        ...this.freezeColumnRight.map((item) => {
          return {
            ...item,
            Is_Frozen: true,
            Frozen_Dirction: 'right'
          }
        })
      ].map((item, index) => {
        item.Sort = (index + 1) * 10
        item.Business_Type = this.type
        return item
      })
      UpdateColumnSetting({Model: columnList}).then((res) => {
        if (res.IsSucceed) {
          // this.handleCancelTbSet()
          this.getGridByCode()
          this.popoverVisible = false
        }
        this.$emit('changeColumn')
      })
    },
    async getGridByCode() {
      try {
        this.gridConfig = this.gridConfig || {}
        this.gridLoading = true
        this.index++
        const res = await GetGridByCode({code: this.code})
        if (res.IsSucceed) {
          let data = res.Data
          if (this.gridDataHandler) {
            data = await this.gridDataHandler(data)
          }
          const g = data.Grid
          this.gridLoading = false
          this.$set(this.gridConfig, 'height', g.Height)
          this.$set(this.gridConfig, 'showFooter', g.Is_Summary)
          this.$set(this.gridConfig, 'disablidPagination', !g.Is_Page)
          let columnList = []
          // 可选择
          if (g.Is_Select) {
            columnList.push({
              width: 55,
              label: '',
              otherOptions: {type: 'select', align: 'center', fixed: 'left'}
            })
            this.$set(this.gridConfig, 'checkbox', true)
          }
          // 带序号
          if (g.Is_Row_Number) {
            columnList.push({
              width: 70,
              label: '序号',
              key: 'index',
              otherOptions: {type: 'index', align: 'center', fixed: 'left'}
            })
          }
          columnList = [...columnList, ...data.ColumnList.map(item => {
            item.label = item.Display_Name
            item.key = item.Code
            item.width = item.Width || '150'
            item.hide = !item.Is_Display
            item.sortable = item.Is_Sort
            item.editable = item.Is_Edit
            item.otherOptions = {
              type: item.Type,
              style: item.Style || '',
              fixed: item.Is_Frozen ? item.Frozen_Dirction : '',
              align: item.Align
            }
            item.decimal = item.Digit_Number || 0
            return item
          })]

          this.$set(this.gridConfig, 'tableColumns', columnList)
          let mustArr = columnList.filter(item => item.Is_Must_Input)
          this.$set(this.gridConfig, 'validRules', mustArr.reduce((acc,value)=>{
            acc[value.Code] = [{required: true, message: `${value.Display_Name}不能为空`, trigger: 'blur'}]
            return acc
          },{}))
        } else {
          this.$message.error(res.Message || '获取表格配置失败')
        }
      } catch (error) {
        this.$message.error('获取表格配置失败')
      }
    },

    rowClassName({row}) {
      if (row[this.customTableObj.rowClassName] === true) {
        return 'row-yellow'
      }
    },
    clearCurrentRow() {
      this.$refs.table.clearCheckboxRow()
    },
    handleCellChange() {
      // 更新表格的合计数据
      this.$refs.table.updateFooter()
    },

    handleFooterMethod({columns, data}) {
      if (this.customTableObj.footerList) {
        return this.customTableObj.footerList
      } else if (this.customTableObj.summaryColumns) {
        const columnsToSum = [...(this.customTableObj.summaryColumns || [])]
        const footer = []
        columns.forEach((column, index) => {
          if (index === 0) {
            footer.push('合计')
          } else if (column.property) {
            if (columnsToSum.includes(column.property)) {
              let values = []
              if (this.customTableObj.footerMethodType === 'list') {
                values = data.map(item => Number(item[column.property] || 0))
              } else if (this.customTableObj.footerMethodType === 'listParent') {
                values = data.map(item => {
                  if (!item.ParentId) {
                    return Number(item[column.property])
                  } else {
                    return 0
                  }
                })
              } else {
                const getSum = (data) => {
                  data.forEach(item => {
                    if (item.children && item.children.length > 0) {
                      values.push(Number(item[column.property] || 0))
                      getSum(item.children)
                    } else {
                      values.push(Number(item[column.property] || 0))
                    }
                  })
                }
                getSum(data)
              }
              const total = formatCurrency(values.reduce((acc, cur) => acc + cur, 0))
              footer.push(total)
            } else {
              footer.push('-')
            }
          } else {
            footer.push('-')
          }
        })
        return [footer]
      }
      return []
    },
    handleSizeChange(val) {
      this.customTableObj.tableData = []
      this.$emit('handleSizeChange', val)
    },
    handleCurrentChange(val) {
      this.customTableObj.tableData = []
      this.$emit('handleCurrentChange', val)
    },
    select(selection) {
      this.$emit('select', selection.records, selection.row)
      this.$emit('checkbox-change', selection)
      this.$emit('select-change', selection.records)
      this.selections = selection.records
    },
    selectAll(selection) {
      this.$emit('selectall', selection.records, this.tableData)
      this.$emit('checkbox-all', selection)
      this.$emit('select-change', selection.records)
      this.selections = selection.records
    },
    getSelections() {
      return this.selections || []
    },
    treeNode(item, index) {
      if (index === 0 && item.treeNode) {
        return true
      }
    },
    cellClickEvent(e) {
      this.$emit('cellClickEvent', e.row)
      this.$emit('cell-dblclick', e)
    },
    changeTableSort(e) {
      this.$emit('currentNodeSort', e.column)
      this.$emit('sort-change', e)
    },
    getenavl(item, row) {
      if (item.noAuthor) {
        if ((eval(item.condition) || item.condition === undefined || item.condition === '')) {
          return true
        } else {
          return false
        }
      }
      if ((item.isShow || item.isShow === undefined) && (eval(item.condition) || item.condition === undefined || item.condition === '')) {
        return true
      } else {
        return false
      }
    },
    /**
     * 导出Excel
     * @param {string} filename - 文件名，默认为'表格数据'
     * @param {Object} valueFormatter - 值格式化器，用于自定义字段值的转换
     * @param {Array} customColumns - 自定义导出列，如果不传则使用当前显示的列
     * @param {Array} customData - 自定义导出数据，如果不传则使用当前表格数据
     */
    exportToExcel(filename = '表格数据', valueFormatter = null, customColumns = null, customData = null) {
      try {
        // 获取要导出的数据
        const exportData = customData || this.tableData || []
        if (!exportData || exportData.length === 0) {
          this.$message.warning('暂无数据可导出')
          return
        }

        // 获取要导出的列
        const exportColumns = customColumns || this.customTableObj.tableColumns?.filter(col =>
          !col.hide &&
          col.key &&
          col.key !== 'index' &&
          col.otherOptions?.type !== 'select'
        ) || []

        if (exportColumns.length === 0) {
          this.$message.warning('暂无可导出的列')
          return
        }

        // 创建工作簿
        const XLSX = require('xlsx')
        const wb = XLSX.utils.book_new()

        // 准备表头
        const headers = exportColumns.map(col => col.label || col.Display_Name || col.key)

        // 准备数据行
        const rows = exportData.map(row => {
          return exportColumns.map(col => {
            let value = row[col.key]

            // 优先使用自定义值格式化器
            if (valueFormatter && valueFormatter[col.key]) {
              if (typeof valueFormatter[col.key] === 'function') {
                value = valueFormatter[col.key](value, row, col)
              } else if (typeof valueFormatter[col.key] === 'object') {
                // 支持映射对象格式，如 { true: '是', false: '否' }
                value = valueFormatter[col.key][value] !== undefined ? valueFormatter[col.key][value] : value
              }
            } else {
              // 处理不同类型的数据格式
              if (col.otherOptions?.type === 'date' && value) {
                // 日期格式化
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                  value = date.toLocaleDateString('zh-CN')
                }
              } else if (col.otherOptions?.type === 'number' && value !== null && value !== undefined) {
                // 数字格式化
                const num = parseFloat(value)
                if (!isNaN(num)) {
                  value = col.decimal ? num.toFixed(col.decimal) : num
                }
              } else if (col.otherOptions?.type === 'switch') {
                // 开关类型转换
                value = value ? '是' : '否'
              }
            }

            return value || ''
          })
        })

        // 合并表头和数据
        const wsData = [headers, ...rows]

        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(wsData)

        // 设置列宽
        const colWidths = exportColumns.map(col => ({
          wch: Math.max(col.width ? parseInt(col.width) / 8 : 15, 10)
        }))
        ws['!cols'] = colWidths

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '数据')

        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
        const finalFilename = `${filename}_${timestamp}.xlsx`

        // 导出文件
        XLSX.writeFile(wb, finalFilename)

        this.$message.success('导出成功')

        // 触发导出事件
        this.$emit('excel-exported', {
          filename: finalFilename,
          data: exportData,
          columns: exportColumns
        })

      } catch (error) {
        console.error('Excel导出失败:', error)
        this.$message.error('导出失败，请检查是否已安装xlsx依赖')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@use './table.scss';
</style>

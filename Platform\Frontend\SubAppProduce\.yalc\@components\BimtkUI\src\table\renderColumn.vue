<template>
  <vxe-column
    v-if="!column.hide"
    :key="index"
    :show-overflow="getType(column) === 'checkbox' ? false : true"
    :tree-node="treeNode(column,index)"
    v-bind="column.otherOptions"
    :field="column.key"
    :title="(column.otherOptions.title||'')+(column.label||'')"
    :sortable="column.sortable"
    :min-width="getType(column) === 'seq' ? '58px': column.width"
    :type="getType(column)"
    :show-header-overflow="!column.hideOverflowTooltip&&!column.otherOptions.fixed"
    :show-footer-overflow="!column.hideOverflowTooltip"
    :fixed="column.otherOptions.fixed!==undefined?column.otherOptions.fixed:null"
    auto-resize
    :edit-render="{enabled:column.editable ===true ? true : false }"
    :align="column.otherOptions.align"
  >
    <template #default="{row,rowIndex}">
      <!-- 优先使用具名插槽 -->
      <template v-if="$scopedSlots[column.key]">
        <slot :name="column.key" v-bind="{ row,rowIndex, column: column }" />
      </template>
      <template v-else-if="column.render && typeof column.render === 'function'">
        <render-dom :render="() => column.render?column.render(row,rowIndex):()=>{}" />
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'index'">
        <span class="no-wrap-cell">{{ rowIndex+1 }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'year'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] ? moment(row[column.key]).format('YYYY') : '-' }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'month'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] ? moment(row[column.key]).format('YYYY-MM') : '-' }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'date'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] ? moment(row[column.key]).format('YYYY-MM-DD','') : '-' }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'time'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] ? moment(row[column.key]).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'switch'">
        <el-switch
          v-model="row[column.key]"
          :disabled="!column.editable"
        />
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'progress'">
        <el-progress v-model="row[column.key]" :percentage="100" />
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'number'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] === null ? '-' : formatCurrency(Number(row[column.key]),column.decimal) }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'percent'">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] === null ? '-' : (Number(row[column.key]) * 100).toFixed(column.decimal) + '%' }}</span>
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.color">
        <span class="no-wrap-cell" :style="column.otherOptions.color ? 'color:' + column.otherOptions.color : ''" @click="handleColumnClick(column.key, row)">{{ row[column.key] || '-' }}</span>
      </template>
      <template v-else-if="column.key">
        <span class="no-wrap-cell" :style="column.otherOptions.style">{{ row[column.key] || '-' }}</span>
      </template>
    </template>

    <template v-if="column.editable" #edit="{ row }">
      <template v-if="$scopedSlots[column.key + '_Edit'] ">
        <slot :name="column.key+'_Edit'" v-bind="{ row,rowIndex, column: column }" />
      </template>
      <template v-else-if="column.otherOptions && ['year','month','date','datetime'].includes(column.otherOptions.type)">
        <vxe-input v-model="row[column.key]" :type="column.otherOptions.type" transfer @change="rowChange(column.key,row)" />
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'number'">
        <el-input v-model="row[column.key]" @change="rowChange(column.key,row)" />
      </template>
      <template v-else-if="column.otherOptions && column.otherOptions.type === 'switch'">
        <el-switch
          v-model="row[column.key]"
          @change="rowChange(column.key,row)"
        />
      </template>
      <template v-else>
        <el-input v-model="row[column.key]" @change="rowChange(column.key,row)" />
      </template>
    </template>

    <!-- 递归渲染子列 -->
    <template v-if="column.children && column.children.length">
      <template v-for="(child, childIndex) in column.children">
        <render-column :key="childIndex" :column="child" :index="childIndex" />
      </template>
    </template>
  </vxe-column>
</template>

<script>

import moment from 'moment'
import {formatCurrency} from "../utils/index";

export default {
  name: 'RenderColumn',
  components: {
    renderDom: {
      functional: true,
      props: {
        render: Function
      },
      render(createElement, renDom) {
        return <div>{renDom.props.render()}</div>
      }
    }
  },
  props: {
    column: {
      type: Object,
      required: true,
      default: () => ({
        slot: ''
      })
    },
    slotConfig: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      required: true
    }
  },
  data() {
    return {

    }
  },
  methods: {
    formatCurrency,
    moment,
    rowChange(key, row) {
      // this.$set(row, key, row[key])
      this.$emit('rowChange',key, row[key], row)
    },
    handleColumnClick(key, row) {
      this.$emit('column-click', key, row)
    },

    getType(column) {
      if (column.otherOptions.type === 'index') {
        return 'seq'
      } else if (column.otherOptions.type === 'select') {
        return 'checkbox'
      } else if (column.otherOptions.type === 'html') {
        return 'html'
      } else {
        return ''
      }
    },
    treeNode(item, index) {
      if (item.treeNode) {
        return true
      }
    }
  }
}
</script>


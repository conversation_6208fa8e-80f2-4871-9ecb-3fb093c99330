<template>
  <vxe-colgroup
      v-if="!column.hide"
      show-overflow="title"
      :tree-node="treeNode(column,index)"
      :key="index"
      v-bind="column.otherOptions"
      :field="column.key"
      :title="column.label"
      :sortable="column.sortable"
      :width="column.width"
      min-width="90"
      :render-header="addRedStar"
      :type="column.otherOptions.type === 'index' ? 'seq' : column.otherOptions.type === 'select' ? 'checkbox' : ''"
      :show-header-overflow="!column.hideOverflowTooltip&&!column.otherOptions.fixed"
      :show-footer-overflow="!column.hideOverflowTooltip"
      :fixed="column.otherOptions.fixed!==undefined?column.otherOptions.fixed:null">

        <template v-if="column.children && column.children.length">
          <template v-for="(child, childIndex) in column.children">
            <template v-if="child.children && column.children.length">
              <render-group :column="child" :index="childIndex" :key="childIndex" ></render-group>
            </template>
            <template v-else>
              <render-column :column="child" :index="childIndex" :key="childIndex" ></render-column>
            </template>
          </template>
        </template>

    </vxe-colgroup>
</template>

<script>
import RenderColumn from './renderColumn.vue';
export default {
  name: 'render-group',
  props: {
    column: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      required: true,
    },
  },
  mounted() {
  },
  components: {
    renderDom: {
      functional: true,
      props: {
        render: Function
      },
      render(createElement, renDom) {
        return <div>{renDom.props.render()}</div>
      }
    },
    RenderColumn
  },
  data() {
    return {

    }
  },
  methods: {
    addRedStar(h, { column }) { // 给表头加必选标识
      console.log(column,'dddddddd')
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)]
    },
    treeNode(item,index){
      if( item.treeNode){
        return true
      }
    },
  },
};
</script>
<style scoped>

</style>

::v-deep .el-pagination__sizes .el-input--small .el-input__inner {
  height: 28px !important;
}

.CustomTable {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
  height: 100%;
}

.pagination {
  .box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

}

// 滚动条css
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #e5e5e5;
}

::-webkit-scrollbar-thumb {
  background-color: #bbbbbb;
  border-radius: 5px;
}

// 表格配置css
.popover-container {
  height: 300px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 12px 0;

  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;

    .drag-btn {
      margin-right: 12px;
    }
  }
}

.popover-content {
  min-height: 30px;
}

.title {
  margin-bottom: 5px;
  font-size: 14px;
}

::v-deep .cs-vxe-table {
  .vxe-table--render-wrapper {
    border: 1px solid #e8eaec !important;
  }

  .vxe-header--column.is--current {
    background-color: #f0f7ff;
  }

  .vxe-table--fixed-left-wrapper {
    border-left: 1px solid #e8eaec !important;
  }

  .vxe-table--fixed-right-wrapper {
    border-right: 1px solid #e8eaec !important;
  }

  thead {
    th {
      color: #333333 !important;
      background: #EBEEF2;
      border: 1px solid #ffffff;

      .vxe-cell {
        display: flex !important;
        justify-content: center !important;
      }

      .vxe-resizable.is--line:before {
        background: #ffffff !important;
        height: 100% !important;
        width: 1px;
      }

      .vxe-cell--title {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }


    }
  }

  .vxe-footer--row {
    background: rgba(255, 124, 25, 0.12);
    font-weight: 600!important;
    .vxe-footer--column {
      height: 36px !important;
      line-height: 36px !important;
      padding: 0 !important;
    }
  }

  .vxe-header--column {
    text-align: center !important;
    height: 36px !important;
    padding: 0 !important;

    .vxe-icon-edit, .vxe-table .vxe-sort--asc-btn, .vxe-table .vxe-sort--desc-btn {
      line-height: inherit !important;
    }

    .c--tooltip.vxe-cell {
      display: flex !important;
      justify-content: center !important;
    }

    .vxe-cell--sort {
      i {
        font-size: 12px !important;
      }

      .vxe-sort--desc-btn {
        top: 8px;
      }

      .vxe-sort--asc-btn {
        top: 1px;
      }
    }


  }

  .row--stripe {
    background-color: #F5F7FA !important;
  }

  .vxe-body--row.row--hover.row--stripe,
  .vxe-body--row.row--hover,
  .vxe-table--render-default .vxe-body--row.row--hover.row--stripe,
  .vxe-body--row.row--current {
    background-color: #E2EDF6 !important;
  }

  .vxe-cell {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }

  .vxe-resizable.is--line:before {
    background: transparent !important;
  }

  .vxe-body--column {
    background-image: linear-gradient(#ffffff, #ffffff) !important;
    background-size: 2px 100%, 100% 0 !important;
    height: 36px !important;

    .vxe-cell--label {
      color: #333333;
      font-family: Microsoft YaHei, Microsoft YaHei;
    }

    .vxe-cell {
      color: #333333;
      font-family: Microsoft YaHei, Microsoft YaHei;

      div {
        color: #333333;
        font-family: Microsoft YaHei, Microsoft YaHei;
      }
    }
  }

  .vxe-body--column.col--last .vxe-cell button,
  .vxe-body--column.col--last .vxe-cell div button {
    font-size: 14px !important;
  }

  .vxe-table--body-wrapper,
  .vxe-table--footer-wrapper.body--wrapper {
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      background-color: #E5E5E5;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #BBBBBB;
      border-radius: 5px;
    }
  }

  .vxe-table--empty-content {
    height: 100%;
  }

  .vxe-body--column.col--checkbox {
    .vxe-cell {
      width: 55px !important;
      padding: 0;
    }
  }

  .vxe-header--column.col--checkbox {
    .vxe-cell {
      width: 55px !important;
      padding: 0;
    }
  }

  .col--checkbox {
    .vxe-cell {
      text-align: center;
    }
  }

  .vxe-body--column:not(.col--ellipsis) {
    padding: 0 0 !important;
  }

  .vxe-header--column:not(.col--ellipsis) {
    padding: 0 0 !important;
  }

  .vxe-table--fixed-left-wrapper .vxe-table--body-wrapper {
    overflow-x: inherit!important;
  }
}

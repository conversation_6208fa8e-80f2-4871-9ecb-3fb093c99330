<template>
  <div class="bt-tree-container" :class="{ 'collapsed': isCollapsed }">
    <slot name="header"></slot>
    <!-- 树形组件头部 -->
    <div class="bt-tree-header">
      <div class="bt-tree-header-left">
        <div class="bt-tree-top">
          <!-- 标题 -->
          <div v-if="title" class="bt-tree-title">{{ title }}</div>
          <!-- 新增按钮 -->
          <div v-if="showAddButton && !isCollapsed" class="bt-tree-add">
            <el-button v-if="!$slots['add']" type="text"  icon="el-icon-plus" @click="handleAdd">新增</el-button>
            <slot v-else name="add"></slot>
          </div>
        </div>
        <!-- 搜索框 -->
        <div v-if="showSearch" class="bt-tree-search">
          <!-- 搜索框左侧插槽 -->
          <slot name="search-left"></slot>
          <el-input
            v-if="!$slots['search']"
            v-model="filterText"
            :placeholder="searchPlaceholder"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          ></el-input>
          <slot v-else name="search"></slot>
          <!-- 搜索框右侧插槽 -->
          <slot name="search-right"></slot>
        </div>
      </div>

    </div>

    <!-- 展开/收起按钮 -->
    <div class="bt-tree-collapse-btn" @click="toggleCollapse">
      <img :src="arrowIcon" class="collapse-icon" />
    </div>


    <slot name="sub-header"></slot>
    <!-- 树形内容区域 -->
    <div class="bt-tree-content">
      <el-tree
        ref="tree"
        :data="treeData"
        :props="defaultProps"
        :highlight-current="true"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :node-key="nodeKey"
        :default-expanded-keys="defaultExpandedKeys"
        :render-content="renderContent"
        :draggable="draggable"
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        :default-expand-all="defaultExpandAll"
        :show-checkbox="showCheckbox"
        @node-click="handleNodeClick"
        @node-drag-start="handleDragStart"
        @node-drag-enter="handleDragEnter"
        @check="handleCheck"
        @check-change="handleCheckChange"
        @node-drag-leave="handleDragLeave"
        @node-drag-over="handleDragOver"
        @node-drag-end="handleDragEnd"
        @node-drop="handleDrop"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <!-- 图标插槽 -->
            <template v-if="$scopedSlots['icon']">
              <slot name="icon" :node="node" :data="data"></slot>
            </template>
            <template v-else>
              <img :src="folderIcon" class="svg-icon">
            </template>

            <!-- 默认插槽用于自定义节点标签 -->
            <slot v-if="hasDefaultSlot" :node="node" :data="data"></slot>
            <span v-else style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ data[defaultProps.label] }}</span>

            <!-- 节点操作按钮 -->
            <div v-if="showNodeActions" class="node-actions">
              <template v-for="action in nodeActions">
                <i
                  v-if="action.condition(node, data)"
                  :key="action.type"
                  :class="action.icon"
                  class="node-action-icon"
                  :style="{color: action.color || '#409EFF'}"
                  @click.stop="handleNodeAction(action.type, node, data)"
                ></i>
              </template>
            </div>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 拖动调整宽度的手柄 -->
    <div
      v-if="!isCollapsed && resizable"
      class="bt-tree-resizer"
      @mousedown="startResize"
    ></div>
  </div>
</template>

<script>
export default {
  name: 'BtTree',
  props: {
    // 树形数据
    data: {
      type: Array,
      required: true
    },
    // 树形标题
    title: {
      type: String,
      default: ''
    },
    // 是否显示搜索框
    showSearch: {
      type: Boolean,
      default: true
    },
    // 拖拽排序记忆功能的唯一标识，存在时开启拖拽功能
    code: {
      type: String,
      default: ''
    },
    // 是否可调整宽度
    resizable: {
      type: Boolean,
      default: true
    },
    // 默认宽度
    defaultWidth: {
      type: [Number,String],
      default: 300
    },
    // 最小宽度
    minWidth: {
      type: Number,
      default: 180
    },
    // 最大宽度
    maxWidth: {
      type: Number,
      default: 500
    },
    // 是否显示新增按钮
    showAddButton: {
      type: Boolean,
      default: false
    },
    // 节点配置
    props: {
      type: Object,
      default: () => ({
        children: 'Children',
        label: 'Label'
      })
    },
    // 节点唯一标识
    nodeKey: {
      type: String,
      default: 'Id'
    },
    // 默认展开的节点
    defaultExpandedKeys: {
      type: Array,
      default() {
        return this.allNodeKeys;
      }
    },
    // 是否默认展开所有节点
    defaultExpandAll: {
      type: Boolean,
      default: true
    },
    // 搜索框的占位文本
    searchPlaceholder: {
      type: String,
      default: '搜索关键词'
    },
    // 是否默认收起
    defaultCollapsed: {
      type: Boolean,
      default: false
    },
    // 默认选中的节点key
    defaultSelectedKey: {
      type: [String, Number],
      default: ''
    },
    // 自定义渲染函数
    renderContent: {
      type: Function,
      default: null
    },
    // 允许拖拽的条件
    allowDragFunc: {
      type: Function,
      default: null
    },
    // 允许放置的条件
    allowDropFunc: {
      type: Function,
      default: null
    },
    // 节点图标
    nodeIcon: {
      type: String,
      default: 'icon-folder'
    },
    // 是否显示复选框
    showCheckbox: {
      type: Boolean,
      default: false
    },
    // 是否显示节点操作按钮
    showNodeActions: {
      type: Boolean,
      default: false
    },
    // 节点操作按钮配置
    nodeActions: {
      type: Array,
      default: () => [
        { type: 'add', icon: 'el-icon-plus', condition: () => true, color: '#409EFF' },
        { type: 'edit', icon: 'el-icon-edit', condition: () => true, color: '#409EFF' },
        { type: 'delete', icon: 'el-icon-delete', condition: () => true, color: '#F56C6C' }
      ]
    },
  },
  data() {
    return {
      filterText: '',
      isCollapsed: this.defaultCollapsed,
      width: this.defaultWidth,
      defaultProps: this.props,
      arrowIcon:'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABNlJREFUaEPVWt1OG0cU/mbtOLYhwTjEMpAmXhEqpKhtEiltLiqFNFXUqhehb5A+Ae4TlD5B3Sdo36DkCXDVB8ColyFd1ApjOcSYJsWua3aqb9Zr1maB9Xo3xHO1LJ6Z852fb86eMwIBDMPYSx2i+UQAOQm5yCUlZEpA3O48lwREnc8CoiiBrQjiz3R9Ur0bZgi/kw1jJ2dCPpHAEgAltI9RFMCqBvFM16e3fMzHwACobRPNZQm54txwLBlHLBZFIn5RvY5omvqbo9Vq49A01XOj+a/6+5+DZo+8AmJFQ/zHQa3iGcCR4GYeECnufmk8AQpOoTXN81JKcNOUCgyBvH7T6ICRdQGtMAgQT7vSXdowf7F9Oh6P4Wp6oqthP6Z3zqFFXtb20Wy21GsJWYpC+9qLW50JwDCqtw/x3xq1TpeYSk8gEY8NK7Pr/Eazhd3avnIxQNYjuPBQ1zOl0zY7FcCmsfMUkD9xAbpKZio1sKsMipSutVOtda0BiG9u6tM/n7TOiQCcwk9cHsNU+vKgsgz1++pu3REbJ4NwBWC5TXudElDrDNbzGAxuAlGshugdN3c6BoABewhznT5/HprvV9SRJRgT2p3+wO4BQKps42CNbEOfz2Ymz0Pxx/bcrrxSMWGxU/Kh86zoAfDc2F4REN+RbWazU6EHrFftMLC3K7uKnSTk9/P6bPcQ7QKw8pkDg64zk70SGlV6Fbr/d6TYcuVVh16Tum2FLgBb+zykZrNX/O4T6jyHK3WtoABYgSsNPr83czWwE7Yfzdpv6xgfS+De3QVfQOlCf5VfdlhJ6AxoBeCFUc5L4AfSJWkzjEHhS79vqqUJ4NP7H/jaxmYlAXw7p88UFIDnRplp7QOyDtkn6OEUnmt/eGsOjx7c9bUNk79KdQ8S+HVen1kUVvA29riafj0bOPMEKTxlJCMZf1Y6bpSYFHbKEAbvBy28bTI7mJknCZt9JlPjSKcu+TKr26R+4W8t5PD4s3uBrF+rv8Ze/Y06E4Tt/0Fyf5jCUwP2mcA4CBxA2MK7ANheZ+5z41oG0WhkKBP3C7/w/nV88ehjCHHmd9NA+9rnAXMjsWmUJWfP5aYHWsTtkLJ5nv8LS3h73xdbO+oxNADzc9fw1eP7gWv/OIA/ylsQuDGiLrQRehDTlb78/JOh3LN/cqgsxM3CZqIeAJtGuQBgmR/t/IQMaoQJoucgG/lUYuSTuZFPpwlg5D9oRv6T0nIjq6TCwi0z03dxsCpBCnWWVnrLKrLBU3kiyNQ6KEV0yyoS+xGRyB0rqzitMJKFLQLolBaLAuKjMD4x/VrD4Tob8/qsahzaw724K2WJrvROFXf7XOdEAJYlRri8biMb6QaHGwjSazaTDrxu1B8XrPtUqjVFl5D4G0Is+2ox2Qsrd5LtImPirTb5lM9HF4dq8h2BUG3WVbIT39Ea7FbajWy/7GLP40c6u5NK61abdSOK5KKXprfnckGHYvNCijytwY0Cb3RL7EshC84GxlnK8QzgyBqqDZVn2uFcnOfGxdiFbmNE67tqYHavGrRUh97Wtr0G04MokgUvWj/1HDgLsdOtTMglE1hiZdvrPOfvWFnTrMseq1668m57DGwBt0U6122WJMwcIDo3V9R1GxUz9Gl0rtsAsiig8brN6qDadtv7f3MXI7cstix5AAAAAElFTkSuQmCC',
      folderIcon:'data:image/svg+xml;charset=utf-8;base64,PHN2ZyBpZD0iaWNvbi1mb2xkZXIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiB2aWV3Qm94PSIwIDAgMTQgMTQiPgogIDxyZWN0IGlkPSLnn6nlvaJfMjExOSIgZGF0YS1uYW1lPSLnn6nlvaIgMjExOSIgd2lkdGg9IjE0IiBoZWlnaHQ9IjE0IiBmaWxsPSJub25lIi8+CiAgPGcgaWQ9ImZvbGRlciIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAyKSI+CiAgICA8cGF0aCBpZD0i6Lev5b6EXzM0MTkiIGRhdGEtbmFtZT0i6Lev5b6EIDM0MTkiIGQ9Ik05Ljc4Nyw4SDUuMDU0QTEuMDU0LDEuMDU0LDAsMCwwLDQsOS4wNTR2OS4wOTJBMS4wNTQsMS4wNTQsMCwwLDAsNS4wNTQsMTkuMkgxNi45NDZBMS4wNTQsMS4wNTQsMCwwLDAsMTgsMTguMTQ2VjEwLjhBMS4wNTQsMS4wNTQsMCwwLDAsMTYuOTQ2LDkuNzVIMTEuOTkxYS42MDYuNjA2LDAsMCwxLS40NzMtLjIyN2wtLjktMS4xMjFBMS4wNjgsMS4wNjgsMCwwLDAsOS43ODcsOFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC00IC04KSIgZmlsbD0iI2Y1YzE1YSIvPgogIDwvZz4KPC9zdmc+Cg=='
    };
  },
  computed: {
    treeData() {
      // 如果提供了code，则启用拖拽功能并应用本地存储的排序
      if (this.code) {
        return this.applyStoredOrder(this.data);
      }
      return this.data;
    },
    // 获取所有节点的key值
    allNodeKeys() {
      const keys = [];
      const getKeys = (nodes) => {
        nodes.forEach(node => {
          keys.push(node[this.nodeKey]);
          if (node[this.defaultProps.children]) {
            getKeys(node[this.defaultProps.children]);
          }
        });
      };
      getKeys(this.data);
      return keys;
    },
    // 本地存储的key
    localStorageKey() {
      return `bt-tree-order-${this.code}`;
    },
    // 是否启用拖拽功能
    draggable() {
      return !!this.code;
    },
    // 检测是否有默认插槽
    hasDefaultSlot() {
      return !!this.$scopedSlots.default;
    }
  },
  methods: {
    handleSearch(){
      this.$refs.tree.filter(this.filterText);
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      const label = data[this.defaultProps.label] || '';
      return label.toString().toLowerCase().includes(value.toLowerCase());
    },
    // 切换收起/展开状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed;
      this.$emit('collapse-change', this.isCollapsed);
    },
    // 开始调整宽度
    startResize(event) {
      event.preventDefault();
      const startX = event.clientX;
      const startWidth = this.width;
      let animationFrameId = null;
      let lastTime = performance.now();
      const throttleDelay = 16; // 约60fps

      // 添加拖动时的视觉反馈
      const resizer = event.target;
      resizer.style.backgroundColor = 'rgba(64, 158, 255, 0.3)';
      document.body.style.cursor = 'col-resize';

      const doDrag = (e) => {
        const currentTime = performance.now();
        if (currentTime - lastTime < throttleDelay) {
          return;
        }

        lastTime = currentTime;
        const newWidth = startWidth + e.clientX - startX;

        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }

        animationFrameId = requestAnimationFrame(() => {
          if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
            this.width = newWidth;
            this.$el.style.width = `${newWidth}px`;
            this.$emit('resize', newWidth);
          }
        });
      };

      const stopDrag = () => {
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }
        resizer.style.backgroundColor = '';
        document.body.style.cursor = '';
        document.removeEventListener('mousemove', doDrag);
        document.removeEventListener('mouseup', stopDrag);
      };

      document.addEventListener('mousemove', doDrag);
      document.addEventListener('mouseup', stopDrag);
    },
    // 节点点击事件
    handleNodeClick(data, node) {
      this.$emit('node-click', data, node);
    },
    // 拖拽开始
    handleDragStart(node, event) {
      this.$emit('node-drag-start', node, event);
    },
    // 拖拽进入目标节点
    handleDragEnter(draggingNode, dropNode, event) {
      this.$emit('node-drag-enter', draggingNode, dropNode, event);
    },
    // 拖拽离开目标节点
    handleDragLeave(draggingNode, dropNode, event) {
      this.$emit('node-drag-leave', draggingNode, dropNode, event);
    },
    // 拖拽在目标节点上方
    handleDragOver(draggingNode, dropNode, event) {
      this.$emit('node-drag-over', draggingNode, dropNode, event);
    },
    // 拖拽结束
    handleDragEnd(draggingNode, dropNode, dropType, event) {
      this.$emit('node-drag-end', draggingNode, dropNode, dropType, event);
    },
    // 放置节点
    handleDrop() {
      this.$emit('drag-change', this.treeData);

      // 如果启用了拖拽功能（提供了code），保存排序到localStorage
      if (this.code) {
        this.saveOrderToStorage(this.treeData);
      }
    },

    // 保存排序到localStorage
    saveOrderToStorage(data) {
      try {
        // 提取节点顺序信息
        const orderMap = {};

        const extractOrder = (nodes, parentKey = 'root') => {
          // 初始化父节点的子节点顺序数组
          if (!orderMap[parentKey]) {
            orderMap[parentKey] = [];
          }

          // 记录当前层级的节点顺序
          nodes.forEach(node => {
            const nodeKey = node[this.nodeKey];
            orderMap[parentKey].push(nodeKey);

            // 递归处理子节点
            if (node[this.defaultProps.children] && node[this.defaultProps.children].length > 0) {
              extractOrder(node[this.defaultProps.children], nodeKey);
            }
          });
        };

        extractOrder(data);
        localStorage.setItem(this.localStorageKey, JSON.stringify(orderMap));
      } catch (error) {
        console.error('保存树形排序失败:', error);
      }
    },

    // 应用本地存储的排序
    applyStoredOrder(data) {
      try {
        // 从localStorage获取排序信息
        const orderMapStr = localStorage.getItem(this.localStorageKey);
        if (!orderMapStr) return data;

        const orderMap = JSON.parse(orderMapStr);

        // 深拷贝原始数据，避免修改原始数据
        const clonedData = JSON.parse(JSON.stringify(data));

        // 递归排序函数
        const sortByStoredOrder = (nodes, parentKey = 'root') => {
          // 如果没有该父节点的排序信息，返回原始顺序
          if (!orderMap[parentKey]) return nodes;

          const storedOrder = orderMap[parentKey];
          const nodeMap = {};
          const newNodes = [];
          const newNodesKeys = new Set();

          // 创建节点映射，方便查找
          nodes.forEach(node => {
            nodeMap[node[this.nodeKey]] = node;
          });

          // 按照存储的顺序重新排列节点
          storedOrder.forEach(key => {
            if (nodeMap[key]) {
              newNodes.push(nodeMap[key]);
              newNodesKeys.add(key);
            }
          });

          // 将新增的节点（localStorage中没有的）添加到末尾
          nodes.forEach(node => {
            const nodeKey = node[this.nodeKey];
            if (!newNodesKeys.has(nodeKey)) {
              newNodes.push(node);
            }
          });

          // 递归处理子节点
          newNodes.forEach(node => {
            if (node[this.defaultProps.children] && node[this.defaultProps.children].length > 0) {
              node[this.defaultProps.children] = sortByStoredOrder(
                node[this.defaultProps.children],
                node[this.nodeKey]
              );
            }
          });

          return newNodes;
        };

        // 应用排序
        return sortByStoredOrder(clonedData);
      } catch (error) {
        console.error('应用树形排序失败:', error);
        return data;
      }
    },
    // 是否允许拖拽
    allowDrag(node) {
      if (!node.data[this.nodeKey]) return false;
      if (this.allowDragFunc) {
        return this.allowDragFunc(node);
      }
      return true;
    },
    // 是否允许放置
    allowDrop(draggingNode, dropNode, type) {
      if (this.allowDropFunc) {
        return this.allowDropFunc(draggingNode, dropNode, type);
      }
      // 默认只允许同级拖拽排序
      if (type === 'inner') {
        return false;
      }
      return draggingNode.parent === dropNode.parent;
    },

    // 获取节点图标
    getNodeIcon(data) {
      // 优先使用节点自定义图标
      if (data.customIcon) {
        return data.customIcon;
      }
      // 使用统一的节点图标
      return this.nodeIcon;
    },

    // 复选框相关方法
    handleCheck(data, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }) {
      this.$emit('check', data, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys });
    },

    handleCheckChange(data, checked, indeterminate) {
      this.$emit('check-change', data, checked, indeterminate);
    },

    // 获取当前选中的节点
    getCheckedNodes() {
      return this.$refs.tree.getCheckedNodes();
    },

    getCheckedKeys() {
      return this.$refs.tree.getCheckedKeys();
    },

    getHalfCheckedNodes() {
      return this.$refs.tree.getHalfCheckedNodes();
    },

    getHalfCheckedKeys() {
      return this.$refs.tree.getHalfCheckedKeys();
    },

    // 节点操作按钮相关方法
    handleNodeAction(type, node, data) {
      this.$emit('node-action', { type, node, data });
    },

    // 处理新增按钮点击
    handleAdd() {
      this.$emit('add');
    },

    // 清除排序记忆
    clearOrderMemory() {
      if (this.code) {
        try {
          localStorage.removeItem(this.localStorageKey);
          // 重新渲染树形组件
          this.$forceUpdate();
          return true;
        } catch (error) {
          console.error('清除树形排序记忆失败:', error);
          return false;
        }
      }
      return false;
    }
  },
  watch: {
    defaultSelectedKey: {
      handler(newVal) {
        if (newVal && this.$refs.tree) {
          this.$refs.tree.setCurrentKey(newVal);
        }
      },
      immediate: true
    },
    treeData: {
      handler() {
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.$refs.tree.setCurrentKey(this.defaultSelectedKey);
          }
        });
      },
      immediate: true
    },
    data: {
      handler(newVal) {
        // 当原始数据变化时，重新应用排序
        if (this.code) {
          // 这里不需要额外操作，因为treeData计算属性会自动应用排序
          // 触发一次更新以确保视图更新
          this.$forceUpdate();
        }
      },
      deep: true
    }
  },
  mounted() {
    if (!this.isCollapsed) {
      this.$el.style.width = `${this.width}px`;
    }
  }
};
</script>

<style scoped>
.bt-tree-container {
  position: relative;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 12px;
  will-change: width;
  display: flex;
  flex-direction: column;
}

.bt-tree-container.collapsed {
  width: 15px !important;
  padding: 0!important;
}

.bt-tree-header {
  color: #333333;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.bt-tree-header-left {
  flex: 1;
  min-width: 0;
}

.bt-tree-add {
  margin-left: 12px;
  flex-shrink: 0;
  position: relative;
  top: -6px;
}

.bt-tree-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 12px;
}

.bt-tree-collapse-btn {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
  border-radius: 50%;
  transition: transform 0.3s;
}

.collapsed .bt-tree-collapse-btn {
  border-left: none;
  border-right: 1px solid #e6e6e6;
  transform: translateY(-50%) scaleX(-1);
}

.collapse-icon {
  width: 24px;
  height: 24px;
}

.collapse-icon:hover {
  opacity: 0.8;
}


.bt-tree-content {
  position: relative;
  overflow: auto;
  margin: 12px 0 0 0;
  overflow-y: auto;
  flex: 1;
  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-thumb {
    background: rgba(144, 147, 153, 0.3);
    border-radius: 3px;
    transition: background-color 0.3s;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(144, 147, 153, 0.5);
  }
}
.bt-tree-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  cursor: col-resize;
  background-color: transparent;
}

.bt-tree-resizer:hover {
  background-color: rgba(64, 158, 255, 0.2);
  transition: background-color 0.2s ease;
}

/* 收起状态下隐藏内容，只显示图标 */
.collapsed .bt-tree-title,
.collapsed .bt-tree-search,
.collapsed .bt-tree-content {
  display: none;
}

.collapsed .bt-tree-header {
  justify-content: center;
  padding: 0;
}

.collapsed .bt-tree-collapse-btn {
  border-left: none;
  border-right: 1px solid #e6e6e6;
}

/* 树节点样式 */
.bt-tree-container >>> .el-tree-node__content {
  height: 36px;
}

.bt-tree-container >>> .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.bt-tree-container >>> .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
}

/* 调整树节点的缩进 */
.bt-tree-container >>> .el-tree-node {
  position: relative;
}



.bt-tree-container >>> .el-tree-node__expand-icon {
  padding: 6px;
  margin-right: 4px;
}

/* 自定义节点样式 */
.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  width: 100%;
  min-width: 0;
}

.custom-tree-node i {
  font-size: 16px;
  color: #909399;
}

/* 拖拽样式 */
.is-drop-inner {
  background-color: #f0f7ff !important;
}

.svg-icon{
  min-width: 14px;
  min-height: 14px;
  vertical-align: -0.15em;
  overflow: hidden;
  font-size: 14px;
  margin-right: 3px;
}

/* 节点操作按钮样式 */
.node-actions {
  display: none;
  margin-left: auto;
  flex-shrink: 0;
  padding-right: 6px;
}

.custom-tree-node:hover .node-actions,
.bt-tree-container >>> .el-tree-node.is-current > .el-tree-node__content .node-actions {
  display: flex;
  align-items: center;
}

.node-action-icon {
  font-size: 14px;
  margin-left: 8px;
  cursor: pointer;
  color: var(--node-action-color, #909399);
}

.node-action-icon:hover {
  opacity: 0.8;
}

.bt-tree-top{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
</style>

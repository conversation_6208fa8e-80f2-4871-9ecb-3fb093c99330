# BtTree 树形组件

一个功能丰富的树形组件，支持搜索、拖拽、宽度调整等功能。

## 基本用法

```vue
<template>
  <bt-tree
    :data="treeData"
    :props="defaultProps"
    node-key="Id"
    :default-selected-key="selectedKey"
    @node-click="handleNodeClick"
  />
</template>

<script>
export default {
  data() {
    return {
      treeData: [
        {
          id: 1,
          label: '一级节点',
          children: [{
            id: 2,
            label: '二级节点'
          }]
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      selectedKey: 1 // 默认选中节点的key值
    }
  },
  methods: {
    handleNodeClick(data, node) {
      console.log(data, node)
    }
  }
}
</script>
```

## 属性说明

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|------|------|
| data | 展示数据 | array | — | — |
| props | 配置选项，具体看下表 | object | — | — |
| node-key | 每个树节点用来作为唯一标识的属性 | string | — | Id |
| default-selected-key | 默认选中的节点key | string/number | — | — |
| title | 树形标题 | string | — | — |
| show-search | 是否显示搜索框 | boolean | — | true |
| draggable | 是否开启拖拽节点功能 | boolean | — | false |
| resizable | 是否允许调整宽度 | boolean | — | true |
| default-width | 默认宽度 | number/string | — | 300 |
| min-width | 最小宽度 | number | — | 180 |
| max-width | 最大宽度 | number | — | 500 |
| show-add-button | 是否显示新增按钮 | boolean | — | false |
| default-expanded-keys | 默认展开的节点的key数组 | array | — | [] |
| default-expand-all | 是否默认展开所有节点 | boolean | — | true |
| search-placeholder | 搜索框占位文本 | string | — | '搜索关键词' |
| default-collapsed | 是否默认收起 | boolean | — | false |
| show-checkbox | 节点是否可被选择 | boolean | — | false |
| show-node-actions | 是否显示节点操作按钮 | boolean | — | false |
| node-actions | 节点操作按钮配置 | array | — | [] |

### props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|------|------|
| label | 指定节点标签为节点对象的某个属性值 | string | — | 'Label' |
| children | 指定子树为节点对象的某个属性值 | string | — | 'Children' |

## 事件

| 事件名称 | 说明 | 回调参数 |
|---------|---------|---------|
| node-click | 节点被点击时的回调 | 共两个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、节点对应的 Node |
| check | 节点选中状态发生变化时的回调 | 共两个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、包含 checkedNodes等信息的对象 |
| check-change | 节点选中状态发生变化时的回调 | 共三个参数，依次为：传递给 data 属性的数组中该节点所对应的对象、是否选中、节点是否被间接选中 |
| node-drag-start | 节点开始拖拽时触发的事件 | 共两个参数，依次为：被拖拽节点对应的 Node、event |
| node-drag-enter | 拖拽进入其他节点时触发的事件 | 共三个参数，依次为：被拖拽节点对应的 Node、所进入节点对应的 Node、event |
| node-drag-leave | 拖拽离开某个节点时触发的事件 | 共三个参数，依次为：被拖拽节点对应的 Node、所离开节点对应的 Node、event |
| node-drag-over | 在拖拽节点时触发的事件 | 共三个参数，依次为：被拖拽节点对应的 Node、当前进入节点对应的 Node、event |
| node-drag-end | 拖拽结束时（可能未成功）触发的事件 | 共四个参数，依次为：被拖拽节点对应的 Node、结束拖拽时最后进入的节点（可能为空）、被拖拽节点的放置位置（before、after、inner）、event |
| drag-change | 拖拽成功完成时触发的事件 | 当前树的数据 |
| collapse-change | 展开/收起状态改变时触发的事件 | 当前是否收起 |
| resize | 宽度调整时触发的事件 | 当前宽度值 |
| node-action | 节点操作按钮被点击时的回调 | 共一个参数：包含type（操作类型）、node（节点）、data（节点数据）的对象 |
| add | 新增按钮被点击时的回调 | — |

## 插槽

| 插槽名称 | 说明 |
|---------|---------|
| default | 自定义树节点的内容，参数为 { node, data } |
| icon | 自定义树节点的图标，参数为 { node, data } |
| add | 自定义新增按钮 |
| search | 自定义搜索框 |
| search-left | 搜索框左侧内容 |
| search-right | 搜索框右侧内容 |

## 方法

| 方法名 | 说明 | 参数 |
|--------|--------|--------|
| getCheckedNodes | 若节点可被选择，则返回目前被选中的节点所组成的数组 | — |
| getCheckedKeys | 若节点可被选择，则返回目前被选中的节点的 key 所组成的数组 | — |
| getHalfCheckedNodes | 若节点可被选择，则返回目前半选中的节点所组成的数组 | — |
| getHalfCheckedKeys | 若节点可被选择，则返回目前半选中的节点的 key 所组成的数组 | — |

## 注意事项

1. 使用`default-selected-key`属性时，需要确保提供了正确的`node-key`属性，且值在树节点中存在。
2. 拖拽功能需要设置`draggable`属性为`true`，同时可以通过`allow-drag`和`allow-drop`函数来自定义节点是否可拖拽和放置。
3. 节点操作按钮需要设置`show-node-actions`为`true`，并通过`node-actions`配置具体的操作按钮。
4. 树节点数据中的`label`属性名可以通过`props`配置项进行自定义。
5. 搜索功能默认对节点的`label`属性进行过滤，搜索不区分大小写。
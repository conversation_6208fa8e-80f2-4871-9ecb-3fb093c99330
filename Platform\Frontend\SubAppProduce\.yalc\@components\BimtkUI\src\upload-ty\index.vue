<template>
  <div>
    <el-upload
      ref="upload"
      :class="[type==='avatar' ? 'avatar-uploader' : type==='drag' ? 'upload-demo upload-drag' : 'upload-demo', disabled===true ? 'el-upload-disabled' : '']"
      action="#"
      :disabled="disabled"
      :before-upload="beforeAvatarUpload"
      :on-change="handleChange"
      :on-exceed="handleExceed"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      :drag="type==='drag'"
      :show-file-list="false"
      :file-list="fileList"
    >
      <slot name="uploadIcon" />
      <template v-if="type==='default' && !hasDefaultSlot">
        <el-button type="primary" size="mini" class="blue-btn" icon="md-cloud-upload">上传文件</el-button>
      </template>
      <template v-if="type==='avatar' && !hasDefaultSlot">
        <img v-if="imageUrl" :src="imageUrl" class="avatar">
        <i v-else class="el-icon-plus avatar-uploader-icon" />
        <div v-if="imageUrl" class="del-wrapper" @click.stop="deleteFile()">
          <i class="el-icon-delete-solid" />
        </div>
      </template>
      <template v-if="type==='drag' && !hasDefaultSlot">
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
    </el-upload>
    <template v-if="showList">
      <div class="file-list-wrapper">
        <div v-for="(item, idx) in fileList" :key="idx" class="file-list" @mouseenter="handleMouseEnter(item, idx)" @mouseleave="handleMouseLeave(item, idx)">
          <div class="file-name" @click="openFile(item)">
            <span class="name" :title="item.name"><i class="el-icon-link" />{{ item.name }}</span>
            <span class="close" @click.stop="deleteFile(idx)"><i v-if="item.percentage===100" :class="hoveredRow === idx ? percentageIconClose : percentageIconCheck" /></span>
          </div>
          <el-progress
            v-show="item.showProgress"
            :stroke-width="4"
            :percentage="item.percentage"
            :show-text="false"
          />
        </div>
      </div>
    </template>

    <!-- :on-change="handleChange"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-progress="handleProgress"
      :on-exceed="handleExceed" -->
  </div>
</template>
<script>
import { v4 as uuidv4 } from 'uuid'
import { GetLatestUrl, GetConfigOss, AddAttachment } from './api'
import {  watermarkToPdf } from '../utils/index.js'
export default {
  name: 'BtUploadTy',
  props: {
    fileListData: {
      type: Array,
      default: () => {
        return []
      }
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 99
    },
    size: {
      type: Number,
      default: 5120
    },
    showList: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    acceptType: {
      type: String,
      default: 'file'
    },
    // 件上传的时候的初始样式
    type: {
      type: String,
      default: 'default'
    },
    // 存储的文件夹
    folder: {
      type: String,
      default: ''
    },
    // 是否保存文件
    saveFile: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showProgress: false,
      percentage: 0,
      percentageIconCheck: 'el-icon-circle-check',
      percentageIconClose: 'el-icon-circle-close',

      imageUrl: '',
      addAttachmentIds: [],

      fileList: [],
      hoveredRow: -1,
      accept: '',

      clickTime: null,
      initialId: '', // 分片上传需要的 initial
      // 分片上传 complete 所需参数
      MultipartUpload: {
        Parts: []
      },
      chunk: 10485760, // 10M
      index: 0,
      Bucket: null,
      endPoint: '',

      fileSize: 0,
      packageVersionName: '',

      clientOos: null,

      AddAttachmentFileData: [],
      i: 0
    }
  },
  computed: {
    hasDefaultSlot() {
      return !!this.$slots.uploadIcon && this.$slots.uploadIcon.length > 0
    }
  },
  watch: {
    acceptType: {
      handler(val) {
        if (val === 'file') {
          this.accept = '.docx,.xlsx,.doc,.xls,.rar,.zip,.ppt,.pptx,.txt,.pdf,.jpg,.jpeg,.png,.gif'
        } else if (val === 'image') {
          this.accept = 'image/*'
        } else {
          this.accept = val
        }
      },
      deep: true,
      immediate: true
    },
    fileListData: {
      handler(val) {
        if (val.length) {
          this.i = val.length
          if (this.type === 'avatar') {
            const url = val[0].Url ? val[0].Url : val[0].url ? val[0].url : val[0].FileUrl
            this.setImageUrl(url)
          }
          this.fileList = val.map(item => {
            const file_url = item.Url ? item.Url : item.url ? item.url : item.FileUrl
            const file_name = item.Name ? item.Name : item.name ? item.name : item.FileName
            const file_suffix = item.Suffix ? item.Suffix : item.suffix ? item.suffix : item.FileType
            const file_size = item.Size ? item.Size : item.size ? item.size : item.FileSize

            item.url = file_url
            item.name = file_name
            item.suffix = file_suffix
            item.size = file_size
            item.FileUrl = file_url
            item.FileName = file_name
            item.FileSize = file_size
            item.FileType = file_suffix

            const fileDataJson = {
              url: file_url,
              name: file_name,
              size: file_size,
              suffix: file_suffix
            }
            item.fileData = fileDataJson
            item.percentage = 100
            item.showProgress = true
            return item
          })
        } else {
          this.i = 0
          this.fileList = []
          if (this.$refs.upload) {
            this.$refs.upload.uploadFiles = []
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    await this.getOos()
  },
  mounted() {

  },
  methods: {
    async getOos() {
      const res = await GetConfigOss({})
      if (res.IsSucceed) {
        this.clientOos = new OOS.S3({
          accessKeyId: res.Data.AccessKeyId,
          secretAccessKey: res.Data.AccessKeySecret,
          endpoint: res.Data.Endpoint,
          signatureVersion: 'v2', // 可选v2 或v4
          s3ForcePathStyle: true,
          sessionToken: res.Data.SecurityToken
        })
        this.Bucket = res.Data.BucketName
        this.endPoint = res.Data.Endpoint
      }
    },
    async setImageUrl(url) {
      const GetOssUrl = await GetLatestUrl({ url: url })
      this.imageUrl = GetOssUrl.Data
    },
    async addAttachment(data, file) {
      if (this.saveFile) {
        const fileData = []
        this.fileList.forEach(item => {
          // const fileDataJson = {
          //   url: data.Location,
          //   name: file.name,
          //   size: file.size,
          //   suffix: file.name.substr(file.name.lastIndexOf('.')),
          //   percentage: 100
          // }
          // this.AddAttachmentFileData.push(fileDataJson)

          const fileDataJson = {
            url: item.url,
            name: item.name,
            size: item.size,
            suffix: item.suffix,
            percentage: 100
          }
          fileData.push(fileDataJson)
        })
        if (fileData.length === this.fileList.length && fileData.every(i => i.url)) {
          const addAttachmentRes = await AddAttachment(fileData)
          this.$emit('getFile', { fileListIds: addAttachmentRes.Data, fileListData: this.fileList })
        }
      } else {
        if (this.i === this.fileList.length) {
          this.$emit('getFile', { fileListData: this.fileList })
        }
      }
      this.setImageUrl(data.Location)
      this.$forceUpdate()
    },
    async addAttachmentImage(file, url) {
      console.log('file', file)
      console.log('url', url)
      if (this.saveFile) {
        const fileData = []
        this.fileList.forEach(item => {
          const fileDataJson = {
            url: item.url,
            name: item.name,
            size: item.size,
            suffix: item.suffix,
            percentage: 100
          }
          fileData.push(fileDataJson)
        })
        if (fileData.length === this.fileList.length && fileData.every(i => i.url)) {
          const addAttachmentRes = await AddAttachment(fileData)
          this.$emit('getFile', { fileListIds: addAttachmentRes.Data, fileListData: this.fileList })
        }
      } else {
        if (this.i === this.fileList.length) {
          this.$emit('getFile', { fileListData: this.fileList })
        }
      }
      this.setImageUrl(url)
      this.$forceUpdate()
    },
    handleChange(file, fileList) {
      console.log(file, 'zzzz')
      if (Number(file.size / 1024 / 1024) > this.size) {
        return false
      }
      if (file.status === 'ready') {
        file.percentage = 0
        file.showProgress = true
        const interval = setInterval(() => {
          if (file.percentage >= 99) {
            clearInterval(interval)
            return
          }
          file.percentage += 1
        }, Number(file.size / 1024 / 1024) > 100 ? 150 : 50)
      }
      this.fileList.push(file)

      if (file.status === 'fail') {
        const index = this.fileList.findIndex(item => item.uid === file.uid)
        this.fileList.splice(index, 1)
      }

      // if (file.status === 'success') {
      //   this.percentage = 100
      //   this.showProgress = false
      // }
    },
    beforeAvatarUpload(file) {
      this.$emit('beforeAvatarUpload', file)
      this.fileSize = Number(file.size / 1024 / 1024).toFixed(2) // 计算文件的大小mb
      const isLtSize = this.fileSize < this.size
      if (!isLtSize) {
        this.$message.error(`上传的文件大小不能超过${this.size}MB!`)
        return false
      }
      const lastIndex = file.name.lastIndexOf('.')
      const fileName = uuidv4() + '-' + this.$moment().unix()
      const fileExt = file.name.substring(lastIndex + 1)
      const fileFullName = fileName + '.' + fileExt
      const that = this
      return new Promise((resolve, reject) => {
        that.clickTime = that.$moment().format('YYYYMMDD')
        var MultipartUpload = {
          Parts: []
        }
        var initialId = ''
        that.index = 0
        // 上传图片
        async function putUPloadImage() {
          try {
            // 图片的key必须在image/目录下
            const key = 'image/' + that.clickTime + '/' + fileFullName
            var params = {
              Body: file,
              Bucket: that.Bucket,
              Key: key
            }
            that.clientOos.putObject(params, function(err, data) {
              if (err) {
                console.log(err, err.stack) // an error occurred
              } else {
                console.log(data) // successful response
                console.log(`${that.endPoint}/${params.Bucket}/${params.Key}`)
                const url = `${that.endPoint}/${params.Bucket}/${params.Key}`
                that.i = that.i + 1
                that.fileList.find(item => item.uid === file.uid).percentage = 100
                that.fileList.map((item) => {
                  const fileDataJson = {
                    url: url,
                    name: file.name,
                    size: file.size,
                    suffix: file.name.substr(file.name.lastIndexOf('.')),
                    percentage: 100
                  }
                  if (item.uid === file.uid) {
                    item.fileData = fileDataJson
                    item.fileUrl = url
                    item.url = url
                    item.file = {
                      Bucket: params.Bucket,
                      Key: params.Key,
                      Location: url
                    }
                    item.FileUrl = url
                    item.FileName = file.name
                    item.FileSize = file.size
                    item.FileType = file.name.substr(file.name.lastIndexOf('.'))
                  }
                  return item
                })
                that.addAttachmentImage(file, url)
              }
            })
          } catch (e) {
            console.log('错误了吗')
          }
        }
        // 上传附件
        async function putUPload() {
          try {
            // 文件的key不可以在image/目录下
            let folderStr = ''
            if (that.folder) {
              folderStr = that.folder + '/'
            } else {
              folderStr = ''
            }
            const key = that.clickTime + '/' + folderStr + fileFullName
            // let [name, ext] = key.split('.');
            const lastIndex = key.lastIndexOf('.')
            const name = key.substring(0, lastIndex)
            const ext = key.substring(lastIndex + 1)
            that.packageVersionName = name.split('/')[1]
            // 每次的起始位置
            const start = that.chunk * that.index
            if (start > file.size) { // 分片上传完成，文件合成
              mergeUpload(key, that.Bucket)
              return
            }
            // //每次分片的大小
            const bold = file.slice(start, start + that.chunk)
            // 得到文件名称,index的目的是分片不重复
            const boldName = `${name}${that.index}.${ext}`
            // 需要在转换为文件对象
            const boldFile = new File([bold], boldName)
            const PartNumber = that.index + 1
            if (that.index === 0) { // 第一次需要获取uploadId
              getUploadId(boldFile, PartNumber, key, that.Bucket)
            } else {
              // 分片上传
              getUploadPart(boldFile, PartNumber, key, that.Bucket)
            }
          } catch (e) {
            console.log('错误了吗')
            // 捕获超时异常。
          }
        }
        if (file.type.startsWith('image/')) {
          putUPloadImage()
        } else {
          putUPload()
        }

        // 本接口初始化一个分片上传（Multipart Upload）操作，并返回一个上传 ID，
        // 此 ID用来将此次分片上传操作中上传的所有片段合并成一个对象。用
        function getUploadId(file, PartNumber, largeName, BucketName) {
          var params = {
            ContentType: largeName.substring(largeName.lastIndexOf('.') + 1) === 'pdf' ? 'application/pdf' : '',
            Bucket: BucketName,
            Key: largeName// 文件名称
          }
          that.clientOos.createMultipartUpload(params, async function(err, data) {
            if (err) {
              console.log(err, err.stack) // an error occurredw
              await that.getOos()
              getUploadId(file, PartNumber, largeName, BucketName)
            } else { // successful response
              // 拿到分片上传需要的id后开始分片上传操作
              initialId = data.UploadId
              getUploadPart(file, PartNumber, largeName, that.Bucket)
            }
          })
        }

        // 该接口用于实现分片上传操作中片段的上传
        function getUploadPart(file, PartNumber, largeName, BucketName) {
          // 天翼云OOS分片上传
          var params = {
            Body: file,
            Bucket: BucketName,
            Key: largeName, // 文件名称
            PartNumber: PartNumber,
            UploadId: initialId // that.initialId// 分片需要的uploadId
          }
          that.clientOos.uploadPart(params, function(err, data) {
            if (err) {
              console.log(err, err.stack)
            } else { // an error occurred
              // 存储分片数据
              // that.MultipartUpload.Parts.push({ PartNumber: PartNumber, ETag: data.ETag })
              MultipartUpload.Parts.push({ PartNumber: PartNumber, ETag: data.ETag })
              that.index++
              putUPload()
            }
          })
        }

        // 该接口通过合并之前的上传片段来完成一次分片上传过程。
        function mergeUpload(largeName, BucketName) {
          // 天翼云OOS合并分片
          var params = {
            Bucket: BucketName,
            Key: largeName, // 文件名称
            UploadId: initialId, // that.initialId, // 分片需要的uploadId
            MultipartUpload: MultipartUpload // that.MultipartUpload// 之前所有分片的集合
          }
          that.clientOos.completeMultipartUpload(params, function(err, data) {
            if (err) {
              that.index = 0
              initialId = ''// 分片需要传的值
              MultipartUpload.Parts = []// 分片集合
              that.$forceUpdate()
            } else {
              that.i = that.i + 1
              that.fileList.find(item => item.uid === file.uid).percentage = 100
              that.fileList.map((item) => {
                const fileDataJson = {
                  url: data.Location,
                  name: file.name,
                  size: file.size,
                  suffix: file.name.substr(file.name.lastIndexOf('.')),
                  percentage: 100
                }
                if (item.uid === file.uid) {
                  item.fileData = fileDataJson
                  item.fileUrl = data.Location
                  item.url = data.Location
                  item.file = data
                  item.FileUrl = data.Location
                  item.FileName = file.name
                  item.FileSize = file.size
                  item.FileType = file.name.substr(file.name.lastIndexOf('.'))
                }
                return item
              })
              // console.log('上传成功的数据', data)
              that.addAttachment(data, file)
            }
          })
        }

      })
    },
    async openFile(item) {
      // const params = {
      //   Bucket: item.file.Bucket,
      //   Key: item.file.Key,
      //   Expires: 900
      // }
      // var url = this.clientOos.getSignedUrl('getObject', params)
      if (item.percentage !== 100) {
        return
      }
      console.log('item', item)
      const suffixImage = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
      if (suffixImage.includes(item.suffix)) {
        // window.open(item.url)
        watermarkToPdf(item.url)
      } else {
        const GetOssUrl = await GetLatestUrl({ url: item.url })
        // window.open(GetOssUrl.Data)
        watermarkToPdf(GetOssUrl.Data)
      }
    },
    async deleteFile(index) {
      if (this.type === 'avatar') {
        this.imageUrl = ''
        this.fileList = []
        this.$refs.upload.uploadFiles = []
      } else {
        this.fileList.splice(index, 1)
        this.$refs.upload.uploadFiles = this.fileList
      }
      if (this.fileList.length) {
        if (this.saveFile) {
          const fileData = []
          this.fileList.forEach(item => {
            const fileDataJson = {
              url: item.url,
              name: item.name,
              size: item.size,
              suffix: item.name.substr(item.name.lastIndexOf('.')),
              percentage: 100
            }
            fileData.push(fileDataJson)
          })
          if (fileData.length === this.fileList.length && fileData.every(i => i.url)) {
            const addAttachmentRes = await AddAttachment(fileData)
            this.$emit('getFile', { fileListIds: addAttachmentRes.Data, fileListData: this.fileList })
            this.$emit('on-close', { fileListIds: addAttachmentRes.Data, fileListData: this.fileList })
          }
        } else {
          this.$emit('getFile', { fileListData: this.fileList })
          this.$emit('on-close', { fileListData: this.fileList })
        }
      } else {
        if (this.saveFile) {
          this.$emit('getFile', { fileListIds: [], fileListData: [] })
          this.$emit('on-close', { fileListIds: [], fileListData: [] })
        } else {
          this.$emit('getFile', { fileListData: [] })
          this.$emit('on-close', { fileListData: [] })
        }
      }
    },
    handleProgress(event, file, fileList) {
      console.log('event.loaded', event.loaded)
    },
    handlePreview(file) {
      // console.log(file)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件`)
      // this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    handleMouseEnter(item, index) {
      this.hoveredRow = index
    },
    handleMouseLeave(item) {
      this.hoveredRow = 0
    }
  }
}
</script>
<style scoped lang="scss">
.file-list-wrapper {
  max-height: 280px;
  overflow-y: auto;
  /* 设置滚动条宽度 */
  scrollbar-width: thin;
  scrollbar-color: #999 #f1f1f1; /* 设置滚动条滑块颜色和轨道颜色 */
  /* 滚动条轨道 */
  ::-webkit-scrollbar-track,
  ::-moz-scrollbar-track {
    background: #f1f1f1; /* 设置滚动条轨道的背景色 */
  }
  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb,
  ::-moz-scrollbar-thumb {
    background: #999; /* 设置滚动条滑块的背景色 */
    border-radius: 6px; /* 设置滚动条滑块的圆角 */
  }
  /* 鼠标悬停在滚动条上的样式 */
  ::-webkit-scrollbar-thumb:hover,
  ::-moz-scrollbar-thumb:hover {
    background: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */
  }
}

.file-name {
  font-size: 14px;
  color: #333333;
  height: 36px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 12px;
  .name {
    display: block;
    flex: 0 0 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    i {
      margin-right: 5px;
    }
  }
  .close {
    width: 36px;
    font-size: 16px;
    text-align: center;
    padding-top: 3px;
    i {
      color: #333333;
    }
  }
}
.file-name:hover {
  background-color: rgba(190, 190, 190, 0.1);
}

::v-deep {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader .el-upload .del-wrapper {
    width: 178px;
    height: 178px;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center
  }
  .avatar-uploader .el-upload .del-wrapper i {
    font-size: 20px;
    color: #fff;
    display: none;
  }
  .avatar-uploader .el-upload:hover .del-wrapper {
    background-color: rgba(0, 0, 0, 0.3)
  }
  .avatar-uploader .el-upload:hover .del-wrapper i {
    display: block;
  }

  .upload-drag .el-upload {
    width: 100%;
  }
  .el-upload-dragger {
    width: 100%;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .el-upload-disabled .el-upload {
    opacity: .8;
  }
}
</style>

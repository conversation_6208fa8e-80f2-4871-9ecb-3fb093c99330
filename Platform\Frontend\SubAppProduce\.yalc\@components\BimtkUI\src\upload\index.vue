<template>
  <div>
    <el-upload
      v-if="!readonly"
      ref="upload"
      :class="[type==='avatar' ? 'avatar-uploader' : type==='drag' ? 'upload-demo upload-drag' : 'upload-demo', disabled===true ? 'el-upload-disabled' : '']"
      action="#"
      :disabled="disabled"
      :before-upload="beforeAvatarUpload"
      :on-change="handleChange"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :multiple="multiple"
      :accept="acceptType"
      :limit="limit"
      :drag="type==='drag'"
      :show-file-list="false"
      :file-list="localFileList"
      :http-request="httpRequest"
      :on-error="onError"
    >
      <div v-if="$slots.default">
        <slot name="default"></slot>
      </div>
      <div v-else>
        <template v-if="type==='default'">
          <el-button type="primary">上传文件</el-button>
        </template>
        <template v-if="type==='drag'">
          <i class="el-icon-upload"/>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        </template>
      </div>
    </el-upload>
    <template v-if="showList">
      <div class="file-list-wrapper">
        <div v-if="readonly && localFileList.length === 0" class="empty-tip">暂无文件</div>
        <div v-for="(item, idx) in localFileList" :key="idx" class="file-list" @mouseenter="handleMouseEnter(item, idx)"
             @mouseleave="handleMouseLeave(item, idx)">
          <div class="file-name" @click="openFile(item)">
            <span class="name" :title="item.name"><i class="el-icon-link"/>{{ item.name }}</span>
            <span v-if="!readonly" class="close" @click.stop="deleteFile(idx,item)">
              <i v-if="item.percentage===100" :class="hoveredRow === idx ? percentageIconClose : percentageIconCheck"/>
            </span>
          </div>
          <el-progress
            v-show="item.showProgress"
            :stroke-width="4"
            :percentage="item.percentage"
            :show-text="false"
          />
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import OSS from 'ali-oss'
import {GetOssUrl, SecurityToken} from './api'

const getToken = async () => {
  const res = await SecurityToken()
  sessionStorage.setItem('aliOssToken', JSON.stringify(res.Data))
}
let timer = null
export default {
  name: 'BtUpload',
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 0
    },
    size: {
      type: Number,
      default: 5120
    },
    showList: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: 'file'
    },
    // 文件上传的时候的初始样式,
    type: {
      type: String,
      default: 'default' // 可选项 drag
    },
    httpRequest: {
      type: Function,
      default: function (params) {
        try {
          let token = JSON.parse(sessionStorage.getItem('aliOssToken'))
          const ossClient = new OSS({
            region: 'oss-' + token.regionId,
            secure: true,
            accessKeyId: token.AccessKeyId,
            accessKeySecret: token.AccessKeySecret,
            stsToken: token.SecurityToken,
            bucket: token.bucket,
            refreshSTSToken: async () => { // 刷新临时访问凭证的回调，在上传大文件的时候会用到
              const info = await this.securityToken()
              return {
                accessKeyId: info.AccessKeyId,
                accessKeySecret: info.AccessKeySecret,
                stsToken: info.SecurityToken
              }
            },
          })
          const file = params.file
          const date = new Date()
          const fileExtension = (file.name && file.name.split('.'))[(file.name && file.name.split('.')).length - 1]
          ossClient.multipartUpload('/' + date.getFullYear() + '/' + (date.getMonth() * 1 + 1) + '/' + date.getDate() + '/' + (date.getMinutes() + '_' + date.getSeconds() + '_' + date.getMilliseconds()) + '/' + file.uid + '.' + fileExtension, file, {
            progress: function (p, checkpoint) {
              // checkpoint参数用于记录上传进度，断点续传上传时将记录的checkpoint参数传入即可。浏览器重启后无法直接继续上传，您需要手动触发上传操作。
              this.process = checkpoint
              params.onProgress({percent: Math.floor(p * 100)}) // 触发el-upload组件的onProgress方法
            },
            meta: {}
          }).then(val => {
            if (val.res.statusCode === 200) {
              const fileUrl = val.res.requestUrls[0] && val.res.requestUrls[0].split('?')[0]
              params.onSuccess({
                Data: fileUrl + '*' + file.size + '*' + file.name.substr(file.name.lastIndexOf('.')) + '*' + file.name,
                url: fileUrl
              })
            }
          }, err => {
            console.log('err', err)
            params.onError(err)
          })

        } catch (e) {
          console.log('e', e)
          params.onError(e)
        }
      }
    },
    beforeUpload: {
      type: Function,
      default: null
    },
    onSuccess: {
      type: Function,
      default: null
    },
    onChange: {
      type: Function,
      default: null
    },
    onError: {
      type: Function,
      default: () =>{ }
    },
  },
  data() {
    return {
      process: null,
      acceptType: '',
      percentage: 0,
      percentageIconCheck: 'el-icon-circle-check',
      percentageIconClose: 'el-icon-circle-close',
      hoveredRow: -1,
      localFileList: []
    }
  },
  computed: {},
  watch: {
    accept: {
      handler(val) {
        if (val === 'file') {
          this.acceptType = 'image/*,video/*,.docx,.xlsx,.doc,.xls,.rar,.zip,.ppt,.pptx,.txt,.pdf'
        } else if (val === 'image') {
          this.acceptType = 'image/*'
        } else {
          this.acceptType = val
        }
      },
      deep: true,
      immediate: true
    },
    fileList: {
      handler(val) {
        this.localFileList = val.map(item => ({
          ...item,
          uid: item.uid || Math.random().toString(36).substring(2),
          percentage: 100,
          showProgress: false,
          status: 'success',
          url: item.url || ''
        }))
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    getToken()
    timer = setInterval(() => {
      getToken()
    }, 1000 * 60 * 19) // token有效期为20分钟
  },
  beforeDestroy() {
    clearInterval(timer)
  },
  methods: {
    securityToken() {
      return new Promise((resolve, reject) => {
        SecurityToken({}).then(res => {
          resolve(res.Data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    beforeAvatarUpload(file) {
      if (this.beforeUpload) {
        const result = this.beforeUpload(file);
        // 支持 Promise 或 Boolean
        if (result instanceof Promise) {
          return result.then(valid => {
            if (!valid) console.warn('文件被拦截');
            return valid;
          });
        } else if (result === false) {
          console.warn('文件被拦截');
          return result;
        }
      }
      // 检验文件大小
      const isLt = file.size / 1024 / 1024 < this.size;
      if (!isLt) {
        this.$message.error(`文件大小不能超过 ${this.size}MB!`);
        return false;
      }
      // 检验文件类型
      if (this.acceptType) {
        const fileType = file.name.substring(file.name.lastIndexOf('.'));
        const acceptTypes = this.acceptType.split(',');
        const isValidType = acceptTypes.some(type => {
          if (type === 'image/*') {
            return file.type.startsWith('image/');
          }
          return type.includes(fileType.toLowerCase());
        });
        if (!isValidType) {
          this.$message.error('文件类型不正确！');
          return false;
        }
      }
      return true;
    },
    handleChange(file, fileList) {
      if(this.onChange){
        this.onChange(file, fileList)
      }
      if (file.status === 'ready') {
        file.percentage = 0
        file.showProgress = true
        const interval = setInterval(() => {
          if (file.percentage >= 99) {
            clearInterval(interval)
            return
          }
          file.percentage += 1
        }, 50)
      }
      this.localFileList = fileList
      if (file.status === 'fail') {
        const index = this.localFileList.findIndex(item => item.uid === file.uid)
        this.localFileList.splice(index, 1)
      }
    },
    handleExceed() {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件`)
    },
    handleMouseEnter(item, index) {
      this.hoveredRow = index
    },
    handleMouseLeave(item) {
      this.hoveredRow = -1
    },
    handleSuccess(response, file, fileList) {
      this.localFileList = fileList.map(item => ({
        ...item,
        uid: item.uid || Math.random().toString(36).substring(2),
        url: (item.response && item.response.url) || item.url || '',
        percentage: item.percentage || 100,
        showProgress: item.showProgress || false,
        status: item.status || 'success'
      }))
      this.$emit('update:fileList', this.localFileList)
      if (fileList.every((item) => item.status === 'success')) {
        this.$emit('finish', this.localFileList)
      }
      if (this.onSuccess) {
        this.onSuccess(response, file, fileList); // 触发父组件的回调
      }

    },
    async openFile(item) {
      console.log({item})
      const res = await GetOssUrl({url: item.url || item.response.url})
      if (res.Data) {
        // watermarkToPdf(res.Data)
        window.open(res.Data)
      } else {
        this.$message.error('文件不存在')
      }
    },
    // 删除
    async deleteFile(index, file) {
      this.localFileList.splice(index, 1)
      this.$emit('delete', index, file)
      this.$emit('update:fileList', this.localFileList)
      if (this.localFileList.every((item) => item.status === 'success')) {
        this.$emit('finish', this.localFileList)
      }
    },

    getFileList() {
      return this.localFileList
    }
  },
}

</script>

<style scoped lang="scss">
.file-list-wrapper {
  max-height: 280px;
  overflow-y: auto;
  /* 设置滚动条宽度 */
  scrollbar-width: thin;
  scrollbar-color: #999 #f1f1f1; /* 设置滚动条滑块颜色和轨道颜色 */
  /* 滚动条轨道 */
  ::-webkit-scrollbar-track,
  ::-moz-scrollbar-track {
    background: #f1f1f1; /* 设置滚动条轨道的背景色 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb,
  ::-moz-scrollbar-thumb {
    background: #999; /* 设置滚动条滑块的背景色 */
    border-radius: 6px; /* 设置滚动条滑块的圆角 */
  }

  /* 鼠标悬停在滚动条上的样式 */
  ::-webkit-scrollbar-thumb:hover,
  ::-moz-scrollbar-thumb:hover {
    background: #555; /* 设置鼠标悬停时滚动条滑块的背景色 */
  }
}

.file-name {
  font-size: 14px;
  color: #333333;
  height: 36px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 12px;

  .name {
    display: block;
    flex: 0 0 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    i {
      margin-right: 5px;
    }
  }

  .close {
    width: 36px;
    font-size: 16px;
    text-align: center;
    padding-top: 3px;

    i {
      color: #333333;
    }
  }
}

.file-name:hover {
  background-color: rgba(190, 190, 190, 0.1);
}

.empty-tip {
  color: #909399;
}

::v-deep {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader .el-upload .del-wrapper {
    width: 178px;
    height: 178px;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center
  }

  .avatar-uploader .el-upload .del-wrapper i {
    font-size: 20px;
    color: #fff;
    display: none;
  }

  .avatar-uploader .el-upload:hover .del-wrapper {
    background-color: rgba(0, 0, 0, 0.3)
  }

  .avatar-uploader .el-upload:hover .del-wrapper i {
    display: block;
  }

  .upload-drag .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }

  .el-upload-disabled .el-upload {
    opacity: .8;
  }
}
</style>

import numeral from 'numeral'
import Decimal from 'decimal.js';

export function toThousands(value) {
    return numeral(value).format('0,0')
}
// 千分位显示并保留两位小数
export function formatCurrency(value, precision = 2) {
    return numeral(value).format(`0,0.${'0'.repeat(precision)}`)
}
export function filterEpcMounth(value) {
    if (!value) return ''
    const dateMonthString = value.split('月')[0] || ''
    if (!dateMonthString) return ''
    const filterMonthString = new Date(this.$store.state.epcBigScreen.filterTime).getMonth()+1
    if(Number(filterMonthString)===Number(dateMonthString)){
        return ''
    }else{
        return `(${dateMonthString}月)`
    }
}
// 处理数据相乘的浮点精度问题
export function calculateProduct(num1, num2) {
    num1=Number(num1)
    num2=Number(num2)
    const result = new Decimal(num1).times(new Decimal(num2));
    return result.toNumber() || 0;
}
// 处理除法的浮点精度问题
export function calculateDivProduct(num1, num2) {
    num1=Number(num1)
    num2=Number(num2)
    const result = new Decimal(num1).div(new Decimal(num2));
    return result.toNumber() || 0;
}


import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
    return Cookies.get(TokenKey)
}

export function setToken(token) {
    return Cookies.set(TokenKey, token)
}

export function removeToken() {
    return Cookies.remove(TokenKey)
}


export function utf8ToBase64(str) {
    // 将字符串转换为UTF-8编码的二进制数据
    const utf8Bytes = encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
        return String.fromCharCode('0x' + p1);
    });
    // 将二进制数据转换为Base64编码
    return btoa(utf8Bytes);
}

// pdf预览加水印
export const watermarkToPdf = async (fileUrl) => {
    const kkFileUrl = 'https://fileview.jgsteel.cn/onlinePreview?url=' + encodeURIComponent(Base64.encode(fileUrl))
    window.open(`/project/sys/test-product-report?linkUrl=${kkFileUrl}`)
}
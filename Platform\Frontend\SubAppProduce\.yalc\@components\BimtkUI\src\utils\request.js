import axios from 'axios'
import { Message } from 'element-ui'
import { getToken,utf8ToBase64 } from './index.js'
import Vue from 'vue'

// create an axios instance
// Vue.prototype.$config = window.URLGLOBAL

/* setTimeout(() => {
  delete window.URLGLOBAL // 用完之后删除
}, 500) */
// function baseURL() {
// const regexSYS = /\/Platform\//
// const regexPRO = /\/PRO\//
// const baseUrl = regexSYS.test(data) ? window.URLGLOBAL.Platform : regexPRO.test(data) ? window.URLGLOBAL.PRO : window.URLGLOBAL.URL ?? ''
// return baseUrl
// return process.env.VUE_APP_BASE_API
// }
function Axios(option) {
  const service = axios.create({
    // baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
    baseURL: Vue.prototype.$BimtkUIConfig?.baseURL,
    // withCredentials: true, // send cookies when cross-domain requests
    timeout: 60000 * 10 // request timeout
  })

  // request interceptor
  service.interceptors.request.use(
    config => {
      // do something before request is sent

      config.headers['Authorization'] = option.token ? option.token : getToken()
      if (localStorage.getItem('projectId')) {
        config.headers['ProjectId'] = localStorage.getItem('projectId')
      }
      if (localStorage.getItem('projectId')) {
        config.headers['TsjProjectId'] = localStorage.getItem('projectId')
      }
      if (localStorage.getItem('ShuChangUserId')) {
        config.headers['UserId'] = localStorage.getItem('ShuChangUserId') || ''
      }
      if (localStorage.getItem('ShuChangUserAccount')) {
        config.headers['UserAccount'] = localStorage.getItem('ShuChangUserAccount') || ''
      }
      if (localStorage.getItem('ShuChangUserName')) {
        config.headers['UserName'] = utf8ToBase64(localStorage.getItem('ShuChangUserName') || '')
      }
      config.headers.LastWorkingObjectId = localStorage.getItem('Last_Working_Object_Id')
      config.headers.last_working_object_id = localStorage.getItem('Last_Working_Object_Id')
      return config
    },
    error => {
      // do something with request error
      console.log(error) // for debug
      return Promise.reject(error)
    }
  )

  // response interceptor
  service.interceptors.response.use(
    /**
     * If you want to get http information such as headers or status
     * Please return  response => response
     */

    /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
    response => {
      const res = response.data

      // if (res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {
      //   // 服务端返回false，同时需要获取数据
      //   // to re-login
      //   // MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
      //   //   confirmButtonText: 'Re-Login',
      //   //   cancelButtonText: 'Cancel',
      //   //   type: 'warning'
      //   // }).then(() => {
      //   //   store.dispatch('user/resetToken').then(() => {
      //   //     location.reload()
      //   //   })
      //   // })
      //   return res
      // } else if (res.StatusCode === 200 || res.StatusCode === 502 || res.StatusCode === 502 || res.StatusCode === 501 || res.StatusCode === 500) {
      //   // Message({
      //   //   message: res.Message || 'Error',
      //   //   type: 'error',
      //   //   duration: 5 * 1000
      //   // })
      // } else
      if (res.StatusCode === 401) {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      } else if (res.StatusCode === 502) { // res.StatusCode === 501 ||
        Message({
          message: res.Message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        return res
        // return Promise.reject(new Error(res.Message || ' '))
      }
    },
    error => {
      console.log('err', error) // for debug
      let message = ''
      let status = null
      if (error.response) {
        // 有响应，但响应状态码不在 2xx 范围内
        status = error.response.status
        switch (status) {
          case 401:
            message = '登录过期，请重新登录'
            break
          case 403:
            message = '无权访问'
            break
          case 404:
            message = '请求地址错误'
            break
          case 500:
            message = '服务器出现错误'
            break
          default:
            message = '网络问题或连接超时'
            break
        }
      } else if (error.request) {
        // 请求已发送，但没有收到响应
        message = '没有响应或连接超时'
      } else {
        // 发生了错误，请求无法完成
        message = error.message
        // 判断是否是超时错误
        if (error.message.includes('timeout')) {
          message = '网络问题或连接超时'
          // 在这里可以进行相关处理，例如重新发送请求
        }
      }
      // Message({
      //   message: message,
      //   type: 'error',
      //   duration: 5 * 1000
      // })
      return Promise.reject(error)
    }
  )
  // 请求处理
  return new Promise((resolve, reject) => {
    service(option).then(res => {
      resolve(res)
    }).catch(error => {
      reject(error)
    })
  })
}

export default Axios

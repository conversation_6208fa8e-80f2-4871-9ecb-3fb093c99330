import request from '@/utils/request'
import qs from 'qs'

// 构件导入分配（生产任务-》构件分配->导入分配） (Auth)
export function ImportUpdateComponentWorkingTeam(data) {
  return request({
    url: '/PRO/ProductionTask/ImportUpdateComponentWorkingTeam',
    method: 'post',
    data
  })
}

// 构件分配分页列表 (Auth)
export function GetProcessAllocationComponentPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessAllocationComponentPageList',
    method: 'post',
    data
  })
}

// 撤销分配（生产任务-》构件分配->撤销分配） (Auth)
export function RevocationComponentAllocation(data) {
  return request({
    url: '/PRO/ProductionTask/RevocationComponentAllocation',
    method: 'post',
    data
  })
}

export function GetWorkingTeamComponentCountBase(data) {
  return request({
    url: '/PRO/ProductionTask/GetWorkingTeamComponentCountBase',
    method: 'post',
    data
  })
}

export function UpdateComponentAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/UpdateComponentAllocationWorkingTeamBase',
    method: 'post',
    data
  })
}
export function UpdateBatchCompAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/UpdateBatchCompAllocationWorkingTeamBase',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》构件分配->分配班组） (Auth)
export function ComponentAllocationWorkingTeam(data) {
  return request({
    url: '/PRO/ProductionTask/ComponentAllocationWorkingTeam',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 分配班组（生产任务-》构件分配->分配班组） (Auth)
export function ComponentAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/ComponentAllocationWorkingTeamBase',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》得到工艺下的班组统计 (Auth)
export function GetWorkingTeamComponentCount(data) {
  return request({
    url: '/PRO/ProductionTask/GetWorkingTeamComponentCount',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 分配班组（生产任务-》得到所有的班组统计 (Auth)
export function GetAllWorkingTeamComponentCount(data) {
  return request({
    url: '/PRO/ProductionTask/GetAllWorkingTeamComponentCount',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》得到所有的班组统计 (Auth)
export function GetAllWorkingTeamPartCountBase(data) {
  return request({
    url: '/PRO/ProductionTask/GetAllWorkingTeamPartCountBase',
    method: 'post',
    data
  })
}

export function GetAllWorkingTeamComponentCountBase(data) {
  return request({
    url: '/PRO/ProductionTask/GetAllWorkingTeamComponentCountBase',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》构件包含零件复杂度 (Auth)
export function GetComponentPartComplexity(data) {
  return request({
    url: '/PRO/ProductionTask/GetComponentPartComplexity',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 分配班组（生产任务-》套料信息导入 (Auth)
export function ImportNestingInfo(data) {
  return request({
    url: '/PRO/ProductionTask/ImportNestingInfo',
    method: 'post',
    data
  })
}

// 套料图及数控文件上传 (Auth)
export function UploadNestingFiles(data) {
  return request({
    url: '/PRO/ProductionTask/UploadNestingFiles',
    method: 'post',
    data
  })
}

// 协调人协调的工序 (Auth)
export function GetCoordinateProcess(data) {
  return request({
    url: '/PRO/ProductionTask/GetCoordinateProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 协调人协调的工序 (Auth)
export function GetProcessAllocationComponentBasePageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessAllocationComponentBasePageList',
    method: 'post',
    data
  })
}

// 构件分配导出
export function ExportAllocationComponent(data) {
  return request({
    url: '/PRO/ProductionTask/ExportAllocationComponent',
    method: 'post',
    data
  })
}

// 零件分配分页列表 (Auth)
export function GetProcessAllocationPartPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessAllocationPartPageList',
    method: 'post',
    data
  })
}

// 零件分配分页列表 (Auth)
export function GetProcessAllocationPartBasePageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessAllocationPartBasePageList',
    method: 'post',
    data
  })
}

// 零件导入分配（生产任务-》零件分配->导入分配） (Auth)
export function ImportUpdatePartWorkingTeam(data) {
  return request({
    url: '/PRO/ProductionTask/ImportUpdatePartWorkingTeam',
    method: 'post',
    data
  })
}

// 得到所有的班组统计(零件)--分配辅助 (Auth)
export function GetAllWorkingTeamPartCount(data) {
  return request({
    url: '/PRO/ProductionTask/GetAllWorkingTeamPartCount',
    method: 'post',
    data
  })
}

// 得到工艺下的班组统计(零件) (Auth)
export function GetWorkingTeamPartCount(data) {
  return request({
    url: '/PRO/ProductionTask/GetWorkingTeamPartCount',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 得到工艺下的班组统计(零件) (Auth)
export function GetWorkingTeamPartCountBase(data) {
  return request({
    url: '/PRO/ProductionTask/GetWorkingTeamPartCountBase',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 下料任务单分页列表 (Auth)
export function GetNestingBillPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetNestingBillPageList',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》零件分配->分配班组
export function PartsAllocationWrokingTeam(data) {
  return request({
    url: '/PRO/ProductionTask/PartsAllocationWrokingTeam',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 分配班组（生产任务-》零件分配->分配班组
export function PartsAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/PartsAllocationWorkingTeamBase',
    method: 'post',
    data: qs.stringify(data)
  })
}// 分配班组（生产任务-》零件分配->分配班组
export function PartsBatchAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/PartsBatchAllocationWorkingTeamBase',
    method: 'post',
    data
  })
}

// 分配班组（生产任务-》零件分配->分配班组
export function UpdatePartsAllocationWorkingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/UpdatePartsAllocationWorkingTeamBase',
    method: 'post',
    data
  })
}
// 批量调整分配
export function UpdateBatchPartsAllocationWrokingTeamBase(data) {
  return request({
    url: '/PRO/ProductionTask/UpdateBatchPartsAllocationWrokingTeamBase',
    method: 'post',
    data
  })
}

// 套料信息保存(利用套料零件上传时返回的数据) (Auth)
export function SaveNestingPartInfo(data) {
  return request({
    url: '/PRO/ProductionTask/SaveNestingPartInfo',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 套料任务单详情 (Auth)
export function GetNestingTaskInfoDetail(data) {
  return request({
    url: '/PRO/ProductionTask/GetNestingTaskInfoDetail',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 生产任务-> 任务看板(班组-单构件) (Auth)
export function GetTeamTaskBoardPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamTaskBoardPageList',
    method: 'post',
    data
  })
}

// 任务看板，零构件转移单 (Auth)
export function GetTransferPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTransferPageList',
    method: 'post',
    data
  })
}

// 任务看板，零构件转移单 (Auth)
export function GetPartTaskBoard(data) {
  return request({
    url: '/PRO/ProductionTask/GetPartTaskBoard',
    method: 'post',
    data
  })
}

// 任务看板，零构件转移单 (Auth)
export function GetNestingBillBoardPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetNestingBillBoardPageList',
    method: 'post',
    data
  })
}

// 任务看板，零构件转移单 (Auth)
export function GetProcessTransferPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessTransferPageList',
    method: 'post',
    data
  })
}

// 单构件->开始加工 (Auth)
export function BeginProcess(data) {
  return request({
    url: '/PRO/ProductionTask/BeginProcess',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 任务看板(构件工序)->创建转移单 单构件->转移... (Auth)
export function CreateCompTransferBill(data) {
  return request({
    url: '/PRO/ProductionTask/CreateCompTransferBill',
    method: 'post',
    data
  })
}

// 任务看板(零件工序)->创建转移单 下料任务单->转移... (Auth)
export function CreatePartTransferByTaskBill(data) {
  return request({
    url: '/PRO/ProductionTask/CreatePartTransferByTaskBill',
    method: 'post',
    data
  })
}

// 任务看板(零件工序)->创建转移单 下料任务单->转移... (Auth)
export function SaveCompTransferBill(data) {
  return request({
    url: '/PRO/ProductionTask/SaveCompTransferBill',
    method: 'post',
    data
  })
}

// 生产任务->（我的）零构件转移单->接收拒收 (Auth)
export function ReceiveTransferBill(data) {
  return request({
    url: '/PRO/ProductionTask/ReceiveTransferBill',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 任务看板(零件工序)->保存转移单 下料任务单->转移... (Auth)
export function SavePartTransferBill(data) {
  return request({
    url: '/PRO/ProductionTask/SavePartTransferBill',
    method: 'post',
    data
  })
}

// 撤消任务单 (Auth)
export function CancelNestingBill(data) {
  return request({
    url: '/PRO/ProductionTask/CancelNestingBill',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 任务看板(零件工序)->创建转移单 单零件 ->转移... (Auth)
export function CreatePartTransferByPartCodes(data) {
  return request({
    url: '/PRO/ProductionTask/CreatePartTransferByPartCodes',
    method: 'post',
    data
  })
}

// 任务看板(零件工序)->创建转移单 单零件 ->转移... (Auth)
export function CreatePartTransferByTransferCode(data) {
  return request({
    url: '/PRO/ProductionTask/CreatePartTransferByTransferCode',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 任务看板(零件工序)->创建转移单 单零件 ->转移... (Auth)
export function CreateCompTransferByTransferCode(data) {
  return request({
    url: '/PRO/ProductionTask/CreateCompTransferByTransferCode',
    method: 'post',
    data
  })
}

export function GetProcessTransferDetail(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessTransferDetail',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function GetProcessPartTransferDetail(data) {
  return request({
    url: '/PRO/ProductionTask/GetProcessPartTransferDetail',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 生产系统向BIM+系统发起质检请求 (Auth)

export function ProAddQuest(data) {
  return request({
    url: '/PRO/ProductionTask/ProAddQuest',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function DownNestingTaskTemplate(data) {
  return request({
    url: '/PRO/ProductionTask/DownNestingTaskTemplate',
    method: 'post',
    data
  })
}

// 1为a类工厂-有零件 2为b类工厂-无零件
export function GetTenantFactoryType() {
  return request({
    url: '/PRO/ProductionTask/GetTenantFactoryType',
    method: 'post'
  })
}

export function GetTeamCompHistory(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamCompHistory',
    method: 'post',
    data
  })
}

export function GetTeamStockPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamStockPageList',
    method: 'post',
    data
  })
}

export function GetPageProcessTransferDetailBase(data) {
  return request({
    url: '/PRO/ProductionTask/GetPageProcessTransferDetailBase',
    method: 'post',
    data
  })
}

export function GetPageSchdulingComps(data) {
  return request({
    url: '/PRO/ProductionTask/GetPageSchdulingComps',
    method: 'post',
    data
  })
}

export function GetCanSchdulingComps(data) {
  return request({
    url: '/PRO/ProductionTask/GetCanSchdulingComps',
    method: 'post',
    data
  })
}

export function GetCompSchdulingInfoDetail(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetCompSchdulingInfoDetail',
    method: 'post',
    data
  })
}

export function GetCanSchdulingPartList(data) {
  return request({
    url: '/PRO/nesting/GetCanSchdulingPartList',
    method: 'post',
    data
  })
}

export function GetPartSchdulingPageList(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetPartSchdulingPageList',
    method: 'post',
    data
  })
}

export function SaveSchdulingDraft(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveSchdulingDraft',
    method: 'post',
    data
  })
}

export function GetSchdulingPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetSchdulingPageList',
    method: 'post',
    data
  })
}

export function GetCompSchdulingPageList(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetCompSchdulingPageList',
    method: 'post',
    data
  })
}

export function GetUnitSchdulingPageList(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetUnitSchdulingPageList',
    method: 'post',
    data
  })
}

export function GetSchdulingWorkingTeams(data) {
  return request({
    url: '/PRO/ProductionTask/GetSchdulingWorkingTeams',
    method: 'post',
    data
  })
}

export function ImportSchduling(data) {
  return request({
    url: '/PRO/ProductionSchduling/ImportCompSchduling',
    method: 'post',
    timeout: 20 * 60 * 1000,
    data
  })
}

export function SaveSchdulingTask(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveSchdulingTask',
    method: 'post',
    data
  })
}

export function CancelSchduling(data) {
  return request({
    url: '/PRO/ProductionTask/CancelSchduling',
    method: 'post',
    data
  })
}

export function DelSchdulingPlan(data) {
  return request({
    url: '/PRO/ProductionSchduling/DelSchdulingPlan',
    method: 'post',
    data
  })
}
export function DelSchdulingPlanById(data) {
  return request({
    url: '/PRO/ProductionSchduling/DelSchdulingPlanById',
    method: 'post',
    data
  })
}

export function SaveSchdulingTaskById(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveSchdulingTaskById',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}

export function GetTeamTaskPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamTaskPageList',
    method: 'post',
    data
  })
}

export function GetTeamTaskDetails(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamTaskDetails',
    method: 'post',
    data
  })
}

// 导出任务单详情
export function ExportTaskCodeDetails(data) {
  return request({
    url: '/PRO/ProductionTask/ExportTaskCodeDetails',
    method: 'post',
    data
  })
}

export function TeamTaskTransfer(data, data2) {
  return request({
    url: '/PRO/ProductionTask/TeamTaskTransfer',
    method: 'post',
    data,
    params: data2
  })
}

export function TeamProcessingByTaskCode(data) {
  return request({
    url: '/PRO/ProductionTask/TeamProcessingByTaskCode',
    method: 'post',
    data
  })
}

export function TeamTaskProcessing(data) {
  return request({
    url: '/PRO/ProductionTask/TeamTaskProcessing',
    method: 'post',
    data
  })
}

export function GetSchdulingCancelHistory(data) {
  return request({
    url: '/PRO/ProductionTask/GetSchdulingCancelHistory',
    method: 'post',
    data
  })
}

export function GetTeamTaskAllocationPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamTaskAllocationPageList',
    method: 'post',
    data
  })
}

export function GetTeamProcessAllocation(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamProcessAllocation',
    method: 'post',
    data
  })
}

export function AdjustTeamProcessAllocation(data) {
  return request({
    url: '/PRO/ProductionSchduling/AdjustCompTeamProcessAllocation',
    method: 'post',
    data
  })
}

export function AdjustSubAssemblyTeamProcessAllocation(data) {
  return request({
    url: '/PRO/ProductionSchduling/AdjustSubAssemblyTeamProcessAllocation',
    method: 'post',
    data
  })
}

export function GetTransferHistory(data) {
  return request({
    url: '/PRO/ProductionTask/GetTransferHistory',
    method: 'post',
    data
  })
}

export function GetTransferDetail(data) {
  return request({
    url: '/PRO/ProductionTask/GetTransferDetail',
    method: 'post',
    data
  })
}

// 导出转移单
export function ExportTransferCodeDetail(data) {
  return request({
    url: '/PRO/ProductionTask/ExportTransferCodeDetail',
    method: 'post',
    data
  })
}

export function GetPartSchdulingCancelHistory(data) {
  return request({
    url: '/PRO/ProductionTask/GetPartSchdulingCancelHistory',
    method: 'post',
    data
  })
}

export function BatchReceiveTransferTask(data) {
  return request({
    url: '/PRO/ProductionTask/BatchReceiveTransferTask',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function ReceiveTransferTask(data) {
  return request({
    url: '/PRO/ProductionTask/ReceiveTransferTask',
    method: 'post',
    data
  })
}

// 零件调整分配
export function AdjustPartTeamProcessAllocation(data) {
  return request({
    url: '/PRO/ProductionSchduling/AdjustPartTeamProcessAllocation',
    method: 'post',
    data
  })
}

export function CancelTransferTask(data) {
  return request({
    url: '/PRO/ProductionTask/CancelTransferTask',
    method: 'post',
    data
  })
}

export function GetTransferCancelDetails(data) {
  return request({
    url: '/PRO/ProductionTask/GetTransferCancelDetails',
    method: 'post',
    data
  })
}

export function CheckSchduling(data) {
  return request({
    url: '/PRO/ProductionTask/CheckSchduling',
    method: 'post',
    data: qs.stringify(data)
  })
}

export function GetSchdulingPartUsePageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetSchdulingPartUsePageList',
    method: 'post',
    data
  })
}

export function GetTeamPartUseList(data) {
  return request({
    url: '/PRO/ProductionTask/GetTeamPartUseList',
    method: 'post',
    data
  })
}

export function ApplyCheck(data) {
  return request({
    url: '/PRO/ProductionTask/ApplyCheck',
    method: 'post',
    data
  })
}

export function SaveCompSchdulingDraft(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveCompSchdulingDraft',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function SavePartSchdulingDraft(data) {
  return request({
    url: '/PRO/ProductionSchduling/SavePartSchdulingDraft',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function SavePartSchdulingDraftNew(data) {
  return request({
    url: '/PRO/ProductionSchduling/SavePartSchdulingDraftNew',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function SaveUnitSchdulingDraftNew(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveUnitSchdulingDraftNew',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function GetPartSchdulingInfoDetail(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetPartSchdulingInfoDetail',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function GetUnitSchdulingInfoDetail(data) {
  return request({
    url: '/PRO/ProductionSchduling/GetUnitSchdulingInfoDetail',
    method: 'post',
    data,
    timeout: 20 * 60 * 1000
  })
}
export function GetPartPrepareList(data) {
  return request({
    url: '/PRO/ProductionTask/GetPartPrepareList',
    method: 'post',
    data
  })
}

// 新版获取构件任务零件齐套详情
export function GetTaskPartPrepareList(data) {
  return request({
    url: '/pro/productiontask/GetTaskPartPrepareList',
    method: 'post',
    data
  })
}

export function WithdrawScheduling(data) {
  return request({
    url: '/PRO/ProductionSchduling/WithdrawScheduling',
    method: 'post',
    data
  })
}

export function CancelUnitSchduling(data) {
  return request({
    url: '/PRO/ProductionSchduling/CancelUnitSchduling',
    method: 'post',
    data
  })
}

export function SaveComponentSchedulingWorkshop(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveComponentSchedulingWorkshop',
    method: 'post',
    data
  })
}

export function SavePartSchedulingWorkshop(data) {
  return request({
    url: '/PRO/ProductionSchduling/SavePartSchedulingWorkshop',
    method: 'post',
    data
  })
}

export function SavePartSchedulingWorkshopNew(data) {
  return request({
    url: '/PRO/ProductionSchduling/SavePartSchedulingWorkshopNew',
    method: 'post',
    data
  })
}

export function SaveUnitSchedulingWorkshopNew(data) {
  return request({
    url: '/PRO/ProductionSchduling/SaveUnitSchedulingWorkshopNew',
    method: 'post',
    data
  })
}

export function GetWorkingTeamsPageList(data) {
  return request({
    url: '/PRO/ZeroComponentRecoil/GetWorkingTeamsPageList',
    method: 'post',
    data
  })
}

export function SaveChangeZeroComponentRecoil(data) {
  return request({
    url: '/PRO/ZeroComponentRecoil/SaveChangeZeroComponentRecoil',
    method: 'post',
    data
  })
}

export function GetBuildReturnRecordList(data) {
  return request({
    url: '/PRO/ZeroComponentRecoil/GetBuildReturnRecordList',
    method: 'post',
    data
  })
}

export function GetWorkingTeamLoadRealTime(data) {
  return request({
    url: '/PRO/ProductionTask/GetWorkingTeamLoadRealTime',
    method: 'post',
    data
  })
}

export function GetCompTaskPageList(data) {
  return request({
    url: '/PRO/ProductionTask/GetCompTaskPageList',
    method: 'post',
    data
  })
}

export function SimplifiedProcessing(data) {
  return request({
    url: '/PRO/ProductionTask/SimplifiedProcessing',
    method: 'post',
    data
  })
}

export function GetSimplifiedProcessingHistory(data) {
  return request({
    url: '/PRO/ProductionTask/GetSimplifiedProcessingHistory',
    method: 'post',
    data
  })
}

export function GetSimplifiedProcessingSummary(data) {
  return request({
    url: '/PRO/ProductionTask/GetSimplifiedProcessingSummary',
    method: 'post',
    data
  })
}

export function BatchWithdrawSimplifiedProcessingHistory(data) {
  return request({
    url: '/PRO/ProductionTask/BatchWithdrawSimplifiedProcessingHistory',
    method: 'post',
    data
  })
}

export function GetDwg(data) {
  return request({
    url: '/PRO/ProductionTask/GetDwg',
    method: 'post',
    data
  })
}

export function GetNestingResultPageList(data) {
  return request({
    url: '/PRO/nesting/GetNestingResultPageList',
    method: 'post',
    data
  })
}

export function GetNestingSurplusList(data) {
  return request({
    url: '/PRO/nesting/GetNestingSurplusList',
    method: 'post',
    data
  })
}

export function DeleteNestingResult(data) {
  return request({
    url: '/PRO/nesting/DeleteNestingResult',
    method: 'post',
    data
  })
}

export function GetNestingFiles(data) {
  return request({
    url: '/PRO/nesting/GetNestingFiles',
    method: 'post',
    data
  })
}

export function GetPlateNestingResultImportFile(data) {
  return request({
    url: '/Pro/Nesting/GetPlateNestingResultImportFile',
    method: 'post',
    data
  })
}

export function ImportNestingFiles(data) {
  return request({
    url: '/PRO/nesting/Import',
    method: 'post',
    data
  })
}
export function ImportPlateNestingResult(data) {
  return request({
    url: '/Pro/Nesting/ImportPlateNestingResult',
    method: 'post',
    data
  })
}

export function GetNestingPartList(data) {
  return request({
    url: '/PRO/nesting/GetNestingPartList',
    method: 'post',
    data
  })
}

export function GetSemiFinishedStock(data) {
  return request({
    url: '/PRO/productiontask/GetSemiFinishedStock',
    method: 'post',
    data
  })
}

export function GetSourceFinishedList(data) {
  return request({
    url: '/PRO/productiontask/GetSourceFinishedList',
    method: 'post',
    data
  })
}

export function GetTargetReceiveList(data) {
  return request({
    url: '/PRO/productiontask/GetTargetReceiveList',
    method: 'post',
    data
  })
}

export function GetToReceiveTaskList(data) {
  return request({
    url: '/PRO/productiontask/GetToReceiveTaskList',
    method: 'post',
    data
  })
}

export function GetToReceiveTaskDetailList(data) {
  return request({
    url: '/PRO/productiontask/GetToReceiveTaskDetailList',
    method: 'post',
    data
  })
}

export function ReceiveTaskFromStock(data) {
  return request({
    url: '/PRO/productiontask/ReceiveTaskFromStock',
    method: 'post',
    data
  })
}

export function BatchReceiveTaskFromStock(data) {
  return request({
    url: '/PRO/productiontask/BatchReceiveTaskFromStock',
    method: 'post',
    data
  })
}

export function GetYearlyFullCheckProducedData(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetYearlyFullCheckProducedData',
    method: 'post',
    data
  })
}

export function GetCheckUserRankList(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetCheckUserRankList',
    method: 'post',
    data
  })
}

export function GetWorkingTeamCheckingList(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetWorkingTeamCheckingList',
    method: 'post',
    data
  })
}

export function GetCheckingItemList(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetCheckingItemList',
    method: 'post',
    data
  })
}

export function GetCheckingQuestionList(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetCheckingQuestionList',
    method: 'post',
    data
  })
}

export function GetMonthlyFullCheckProducedData(data) {
  return request({
    url: '/PRO/InspectionAnalysis/GetMonthlyFullCheckProducedData',
    method: 'post',
    data
  })
}

export function ExportSimplifiedProcessingHistory(data) {
  return request({
    url: '/PRO/productiontask/ExportSimplifiedProcessingHistory',
    method: 'post',
    data
  })
}

export function GetCompTaskPartCompletionStock(data) {
  return request({
    url: '/PRO/productiontask/GetCompTaskPartCompletionStock',
    method: 'post',
    data
  })
}

export function GetNestingBillTreeList(data) {
  return request({
    url: '/Pro/NestingBill/GetTreeList',
    method: 'post',
    data
  })
}

export function GetNestingBillDetailList(data) {
  return request({
    url: '/Pro/NestingBill/GetDetailList',
    method: 'post',
    data
  })
}

export function GetDetailSummaryList(data) {
  return request({
    url: '/Pro/NestingBill/GetDetailSummaryList',
    method: 'post',
    data
  })
}

export function UpdateMachineName(data) {
  return request({
    url: '/Pro/NestingBill/UpdateMachineName',
    method: 'post',
    data
  })
}

export function SigmaWOLExport(data) {
  return request({
    url: '/PRO/CustomUssl/SigmaWOLExport',
    method: 'post',
    data
  })
}

export function LentakExport(data) {
  return request({
    url: '/PRO/CustomUssl/LentakExport',
    method: 'post',
    data
  })
}

export function ProfilesExport(data) {
  return request({
    url: '/PRO/CustomUssl/ProfilesExport',
    method: 'post',
    data
  })
}

export function BochuAddTask(data) {
  return request({
    url: '/PRO/CustomUssl/BochuAddTask',
    method: 'post',
    data
  })
}

export function ImportThumbnail(data) {
  return request({
    url: '/Pro/Nesting/ImportThumbnail',
    method: 'post',
    data
  })
}

export function WithdrawPicking(data) {
  return request({
    url: '/Pro/MaterielPicking/WithdrawPicking',
    method: 'post',
    data
  })
}

export function GetPartWithParentPageList(data) {
  return request({
    url: '/PRO/productiontask/GetPartWithParentPageList',
    method: 'post',
    data
  })
}

export function GetNestingMaterialWithPart(data) {
  return request({
    url: '/PRO/productiontask/GetNestingMaterialWithPart',
    method: 'post',
    data
  })
}

// 获取工序下拉框
export function GetProcessSelectList(data) {
  return request({
    url: '/Pro/TechnologyLib/GetProcessSelectList',
    method: 'post',
    data
  })
}

// 零件材料追溯表图纸
export function GetDrawingFileList(data) {
  return request({
    url: '/SYS/Sys_File/GetDrawingFileList',
    method: 'post',
    data
  })
}

export function GetStopList(data) {
  return request({
    url: '/PRO/MOC/GetStopList',
    method: 'post',
    data
  })
}

// 上道工序同步
export function GetPreStepTaskAllocation(data) {
  return request({
    url: '/PRO/ProductionTask/GetPreStepTaskAllocation',
    method: 'post',
    data
  })
}

// 批量上道工序同步
export function BatchAllocationWithPreStepTask(data) {
  return request({
    url: '/PRO/ProductionTask/BatchAllocationWithPreStepTask',
    method: 'post',
    data
  })
}


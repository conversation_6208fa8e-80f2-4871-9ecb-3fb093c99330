// 获取项目分页列表 (Auth)
import request from '@/utils/request'
import qs from 'qs'

export function GetProjectPageList(data) {
  return request({
    url: '/PRO/Project/GetProjectPageList',
    method: 'post',
    data
  })
}

export function GetProjectList(data) {
  return request({
    url: '/PRO/Project/GetProjectList',
    method: 'post',
    data
  })
}

// 获取项目实体 (Auth)
export function GetProjectEntity(data) {
  return request({
    url: '/PRO/Project/GetProjectEntity',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 获取项目实体 (Auth)
export function SaveProject(data) {
  return request({
    url: '/PRO/Project/SaveProject',
    method: 'post',
    data
  })
}

// 删除项目 (Auth)
export function DeleteProject(data) {
  return request({
    url: '/PRO/Project/DeleteProject',
    method: 'post',
    data: qs.stringify(data)
  })
}

//
export function IsEnableProjectMonomer(data) {
  return request({
    url: '/PRO/Project/IsEnableProjectMonomer',
    method: 'post',
    data
  })
}

export function BindBimProject(data) {
  return request({
    url: '/PRO/Project/BindBimProject',
    method: 'post',
    data
  })
}

//
export function CancelBindBimProject(data) {
  return request({
    url: '/PRO/Project/CancelBindBimProject',
    method: 'post',
    data
  })
}

//
export function GetNoBindProjectList(data) {
  return request({
    url: '/PRO/Project/GetNoBindProjectList',
    method: 'post',
    data
  })
}

//
export function GetProjectTemplate(data) {
  return request({
    url: '/PRO/Project/GetProjectTemplate',
    method: 'post',
    data
  })
}

export function UpdateProjectTemplateBase(data) {
  return request({
    url: '/PRO/Project/UpdateProjectTemplateBase',
    method: 'post',
    data
  })
}

export function UpdateProjectTemplateContacts(data) {
  return request({
    url: '/PRO/Project/UpdateProjectTemplateContacts',
    method: 'post',
    data
  })
}

export function UpdateProjectTemplateContract(data) {
  return request({
    url: '/PRO/Project/UpdateProjectTemplateContract',
    method: 'post',
    data
  })
}

export function UpdateProjectTemplateOther(data) {
  return request({
    url: '/PRO/Project/UpdateProjectTemplateOther',
    method: 'post',
    data
  })
}

export function GetPushProjectPageList(data) {
  return request({
    url: '/PRO/Project/GetPushProjectPageList',
    method: 'post',
    data
  })
}

export function GetProjectAreaTreeList(data) {
  return request({
    url: '/PRO/PartsList/GetProjectAreaTreeList',
    method: 'post',
    data
  })
}

export function GetInstallUnitIdNameList(data) {
  return request({
    url: '/PRO/InstallUnit/GetInstallUnitIdNameList',
    method: 'post',
    data
  })
}

export function GeAreaTrees(data) {
  return request({
    url: '/PRO/Project/GeAreaTrees',
    method: 'post',
    data
  })
}

// 零件深化资料查看
export function GetPartDeepenFileList(data) {
  return request({
    url: '/PRO/Component/GetPartDeepenFileList',
    method: 'post',
    data
  })
}

// 零件排产数量统计
export function GetSchedulingPartList(data) {
  return request({
    url: '/PRO/Part/GetSchedulingPartList',
    method: 'post',
    data
  })
}

// 同步文档
export function GetFileSync(data) {
  return request({
    url: 'SYS/Sys_FileType/GetFileSync',
    method: 'post',
    data
  })
}

// 计划跟踪的项目列表
export function GetProjectListForPlanTrace(data) {
  return request({
    url: '/PRO/Project/GetProjectListForPlanTrace',
    method: 'post',
    data
  })
}

// 获取单体
export function GetProjectMonomerListForPlanTrace(data) {
  return request({
    url: '/PRO/Project/GetProjectMonomerListForPlanTrace',
    method: 'post',
    data
  })
}

// 获取区域
export function GetProjectAreaListForPlanTrace(data) {
  return request({
    url: '/PRO/Project/GetProjectAreaListForPlanTrace',
    method: 'post',
    data
  })
}

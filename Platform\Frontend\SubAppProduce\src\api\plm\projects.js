import request from '@/utils/request'
import { promiseProjectApi } from '@/utils/baseurl'
// 获取文件类别内容
export function FileTypeGetEntities(data) {
  return request({
    url: '/SYS/Sys_FileType/GetEntities',
    method: 'post',
    data
  })
}

export function FileTypeGetEntity(data) {
  return request({
    url: '/SYS/Sys_FileType/GetEntity',
    method: 'post',
    data
  })
}

export function GetFileCatalog(data) {
  return request({
    url: '/SYS/Sys_FileType/GetFileCatalog',
    method: 'post',
    data
  })
}

export function GetEntitiesProject(data) {
  return request({
    url: '/SYS/Sys_FileType/GetEntitiesProject',
    method: 'post',
    data
  })
}

// 获取文件夹下的内容
export function GetFilesByType(data) {
  return request({
    url: '/SYS/Sys_File/GetPicEntities',
    method: 'post',
    data
  })
}

export function FileTypeDelete(data) {
  return request({
    url: '/SYS/Sys_FileType/Delete',
    method: 'post',
    data
  })
}

export function FileTypeAdd(data) {
  return request({
    url: '/SYS/Sys_FileType/Add',
    method: 'post',
    data
  })
}
export function FileAddType(data) {
  return request({
    url: '/SYS/Sys_FileType/AddType',
    method: 'post',
    data
  })
}
export function FileTypeEdit(data) {
  return request({
    url: '/SYS/Sys_FileType/Edit',
    method: 'post',
    data
  })
}
// 获取文件内容

export function FileGetEntity(data) {
  return request({
    url: '/SYS/Sys_File/GetEntity',
    method: 'post',
    data
  })
}

export function FileDelete(data) {
  return request({
    url: '/SYS/Sys_File/Delete',
    method: 'post',
    data
  })
}

export function FileAdd(data) {
  return request({
    url: '/SYS/Sys_File/Add',
    method: 'post',
    data
  })
}
export function FileEdit(data) {
  return request({
    url: '/SYS/Sys_File/Edit',
    method: 'post',
    data
  })
}
export function FileMove(data) {
  return request({
    url: '/SYS/Sys_File/Move',
    method: 'post',
    data
  })
}

// BIM文件更改【加载】状态
export function ChangeLoad(data) {
  return request({
    url: '/SYS/Sys_File/IsLoad',
    method: 'post',
    data
  })
}

export function FileHistory(data) {
  return request({
    url: '/SYS/Sys_File/OldFile',
    method: 'post',
    data
  })
}

export function GetLoadingFiles(data) {
  return request({
    url: '/SYS/Sys_File/GetBIMList',
    method: 'post',
    data
  })
}

// 联系人部门

export function DepartmentGetEntity(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/GetEntity',
    method: 'post',
    data
  })
}

export function DepartmentGetEntities(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/GetEntities',
    method: 'post',
    data
  })
}
export function DepartmentDelete(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/Delete',
    method: 'post',
    data
  })
}
export function DepartmentEdit(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/Edit',
    method: 'post',
    data
  })
}
export function DepartmentAdd(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/Add',
    method: 'post',
    data
  })
}
export function DepartmentGetList(data) {
  return request({
    url: '/SYS/Sys_Contacts_Department/GetList',
    method: 'post',
    data
  })
}
// 用户列表接口
export function SysuserGetUserList(data) {
  return request({
    url: '/SYS/User/GetUserList',
    method: 'post',
    data
  })
}
// 获取用户实体
export function SysuserGetUserEntity(data) {
  return request({
    url: '/SYS/User/GetUserEntity',
    method: 'post',
    data
  })
}
// 用户组树接口
export function UserGroupTree(data) {
  return request({
    url: '/SYS/UserGroup/GetChildGroupTree',
    method: 'post',
    data
  })
}
// 获取字典表内容
export function GetDictionaryDetailListByCode(data) {
  return request({
    url: '/SYS/Dictionary/GetDictionaryDetailListByCode',
    method: 'post',
    data
  })
}
// 联系人

export function ContactsGetEntity(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/GetEntity',
    method: 'post',
    data
  })
}

export function ContactsGetEntities(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/GetEntities',
    method: 'post',
    data
  })
}
export function ContactsGetTreeList(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/GetTreeList',
    method: 'post',
    data
  })
}
export function ContactsDelete(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/Delete',
    method: 'post',
    data
  })
}
export function ContactsEdit(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/Edit',
    method: 'post',
    data
  })
}
export function ContactsAdd(data) {
  return request({
    url: '/SYS/Sys_Project_Contacts/Add',
    method: 'post',
    data
  })
}
// 获取附件

export function AttachmentGetEntities(data) {
  return request({
    url: '/SYS/Sys_File/GetAttachmentEntities',
    method: 'post',
    data
  })
}
// 获取附件
export function GetEntitiesByRecordId(data) {
  return request({
    url: '/SYS/Sys_File/GetEntitiesByRecordId',
    method: 'post',
    data
  })
}
// 里程碑配置新增
export function GetProjectsflowmanagementAdd(data) {
  return request({
    url: '/SYS/Sys_Projectsflowmanagement/Add',
    method: 'post',
    data
  })
}
// 里程碑配置修改
export function GetProjectsflowmanagementEdit(data) {
  return request({
    url: '/SYS/Sys_Projectsflowmanagement/Edit',
    method: 'post',
    data
  })
}
// 里程碑配置加载
export function GetProjectsflowmanagementInfo(data) {
  return request({
    url: '/SYS/Sys_Projectsflowmanagement/GetEntity',
    method: 'post',
    data
  })
}
// 里程碑配置加载
export function GetShortUrl(data) {
  return request({
    url: '/PLM/XModel/GetShortUrl',
    method: 'post',
    data
  })
}
// 生化文件生成构件
export function ImportDeependToSteel(data) {
  return request({
    url: '/plm/component/ImportDeependToSteel',
    method: 'get',
    params: data
  })
}

// 获取单体列表
export function GetGetMonomerList(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/GetGetMonomerList',
    method: 'post',
    data
  })
}
// 获取单体
export function GetMonomerEntity(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/GetMonomerEntity',
    method: 'post',
    data
  })
}
// 获取单体
export function GetProMonomerList(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/GetProMonomerList',
    method: 'post',
    data
  })
}
// 新增单体
export function AddMonomer(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/AddMonomer',
    method: 'post',
    data
  })
}
// 修改单体
export function EditMonomer(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/EditMonomer',
    method: 'post',
    data
  })
}
// 删除单体
export function DeleteMonomer(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/DeleteMonomer',
    method: 'post',
    data
  })
}
// 根据深化清单生成构件
export function CommonImportDeependToComp(data) {
  return request({
    url: '/plm/MaterialInfo/CommonImportDeependToComp',
    method: 'get',
    params: data
  })
}

// 构件管理中添加深化清单
export function AddDeepFile(data) {
  return request({
    url: '/PLM/Component/AddDeepFile',
    method: 'post',
    data,
    timeout: 1000 * 60 * 30
  })
}

// 获取区域树
export function GetAreaTreeList(data) {
  return request({
    url: '/PLM/Plm_Project_Areas/GetAreaTreeList',
    method: 'post',
    data
  })
}

// 获取区域树
export function AreaGetEntity(data) {
  return request({
    url: '/PRO/Project/GetArea',
    method: 'post',
    data
  })
}

// 获取区域树
export function GeAreaTrees(data) {
  return request({
    url: '/PRO/Project/GeAreaTrees',
    method: 'post',
    data
  })
}

// 获取区域树
export function AddArea(data) {
  return request({
    url: '/PRO/Project/AddArea',
    method: 'post',
    data
  })
}

// 获取区域树
export function EditArea(data) {
  return request({
    url: '/PRO/Project/EditArea',
    method: 'post',
    data
  })
}

// 获取区域树
export function AreaDelete(data) {
  return request({
    url: '/PRO/Project/Delete',
    method: 'post',
    data
  })
}

// 获取区域树
export function GetProjectEntity(data) {
  return request({
    url: '/PRO/Project/GetProjectEntity',
    method: 'post',
    data
  })
}

// 零件覆盖导入
export function ImportPartList(data) {
  return request({
    url: '/PRO/Part/ImportPartList',
    method: 'post',
    data
  })
}

// 零件新增导入
export function AppendImportPartList(data) {
  return request({
    url: '/PRO/Part/AppendImportPartList',
    method: 'post',
    data
  })
}

// 获取当前公司下的项目
export function GetCurrCompanyProjectList(data) {
  return request({
    url: `${promiseProjectApi()}Plm/Plm_Projects/GetCurrCompanyProjectList`,
    method: 'post',
    data
  })
}
// 根据项目获取单体
export function GetMonomerListByProjectId(data) {
  return request({
    url: `${promiseProjectApi()}Plm/Component/GetMonomerListByProjectId`,
    method: 'post',
    data
  })
}

// 获取总控计划列表
export function GetTotalControlPlanList(data) {
  return request({
    url: `/PRO/ControlPlan/GetTotalControlPlanList`,
    method: 'post',
    data
  })
}
// 获取总控计划明细
export function GetTotalControlPlanEntity(data) {
  return request({
    url: `/PRO/ControlPlan/GetTotalControlPlanEntity`,
    method: 'post',
    data
  })
}
// 保存总控计划
export function SaveTotalControlPlanEntity(data) {
  return request({
    url: `/PRO/ControlPlan/SaveTotalControlPlanEntity`,
    method: 'post',
    data
  })
}
// 获取计划配置
export function GetConfigs(data) {
  return request({
    url: `/PRO/ControlPlan/GetConfigs`,
    method: 'post',
    data
  })
}

// 保存计划配置
export function SaveConfigs(data) {
  return request({
    url: `/PRO/ControlPlan/SaveConfigs`,
    method: 'post',
    data
  })
}

// 获取区域树
export function GetProjectAreaTree(data) {
  return request({
    url: `${promiseProjectApi()}PLM/ContractTrace/GetProjectAreaTree`,
    method: 'post',
    data
  })
}

// 导出总控计划
export function ExportTotalControlPlan(data) {
  return request({
    url: `/PRO/ControlPlan/ExportTotalControlPlan`,
    method: 'post',
    data
  })
}

// 更新计划数据
export function GenerateProjectPlanBusinessData(data) {
  return request({
    url: `/pro/ControlPlan/GenerateProjectPlanBusinessData`,
    method: 'post',
    data
  })
}


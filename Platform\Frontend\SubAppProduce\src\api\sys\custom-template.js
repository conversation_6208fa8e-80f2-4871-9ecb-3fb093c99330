import request from '@/utils/request'

// 获取模板配置分类
export function GetReportTemplateTypeList(data) {
  return request({
    url: '/PRO/ReportTemplate/GetReportTemplateTypeList',
    method: 'post',
    data: data
  })
}

// 获取模板配置列表
export function GetReportTemplateList(data) {
  return request({
    url: '/PRO/ReportTemplate/GetReportTemplateList',
    method: 'post',
    data: data
  })
}

// 获取模板配置信息
export function GetReportTemplateEntity(data) {
  return request({
    url: '/PRO/ReportTemplate/GetReportTemplateEntity',
    method: 'post',
    data: data
  })
}

// 保存模板配置
export function SaveReportTemplateEntity(data) {
  return request({
    url: '/PRO/ReportTemplate/SaveReportTemplateEntity',
    method: 'post',
    data: data
  })
}

// 删除模板配置
export function DeleteReportTemplateEntity(data) {
  return request({
    url: '/PRO/ReportTemplate/DeleteReportTemplateEntity',
    method: 'post',
    data: data
  })
}


// 外-导出模板配置报表
export function ExportReportByTemplate(data) {
  return request({
    url: '/PRO/ReportTemplate/ExportReportByTemplate',
    method: 'post',
    data: data
  })
}

<template>
  <!-- 当模板列表为空时 -->
  <el-button
    v-if="list.length === 0"
    type="primary"
    :loading="loading"
    :disabled="!ids.length"
    @click="handleEmptyList"
  >
    {{ name }}
  </el-button>

  <!-- 当模板列表只有一个时 -->
  <el-button
    v-else-if="list.length === 1"
    type="primary"
    :loading="loading"
    :disabled="!ids.length"
    @click="handleSingleTemplate"
  >
    {{ name }}
  </el-button>

  <!-- 当模板列表有多个时 -->
  <el-dropdown v-else trigger="click" @command="commandMenu">
    <el-button type="primary" :loading="loading" :disabled="!ids.length">
      {{ name }}<i class="el-icon-arrow-down el-icon--right" />
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item v-for="item in list" :key="item.Id" :command="item">{{ item.Template_Name }}</el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>
<script>
import { ExportReportByTemplate, GetReportTemplateList } from '@/api/sys/custom-template'
import { combineURL } from '@/utils'

export default {
  name: 'ExportCustomReport',
  props: {
    // 按钮名称
    name: {
      type: String,
      default: '导出自定义报表'
    },
    code: {
      type: String,
      required: true
    },
    // 业务id
    ids: {
      type: Array,
      default: function() {
        return []
      }
    },
    jsonData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      loading: false
    }
  },
  watch: {
    code: {
      handler() {
        this.fetchTemplates()
      },
      immediate: true
    }
  },
  methods: {
    fetchTemplates() {
      GetReportTemplateList({
        TypeCode: this.code
      }).then(res => {
        this.list = res.Data
      })
    },
    // 处理空模板列表的情况
    handleEmptyList() {
      this.$message.warning('未配置自定义模板，请先配置模板')
    },
    // 处理单个模板的情况
    handleSingleTemplate() {
      if (this.list.length === 1) {
        this.commandMenu(this.list[0])
      }
    },
    // 处理多个模板的情况
    commandMenu(item) {
      this.loading = true
      ExportReportByTemplate({
        TypeCode: this.code,
        TemplateId: item.Id,
        JsonData: this.jsonData,
        SelectIds: this.ids
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

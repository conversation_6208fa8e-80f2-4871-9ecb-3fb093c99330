<template>
  <div :class="'gantt-plan ' + (baseline ? 'hasbaseline' : '')">
    <div ref="gantthere" class="gantthere" />
    <el-dialog
      :title="dialogCfgs.title"
      :visible.sync="dialogShow"
      :width="dialogCfgs.width"
      destroy-on-close
    >
      <keep-alive>
        <component
          :is="dialogCfgs.component"
          v-if="dialogShow"
          :name="dialogCfgs.title"
          v-bind="dialogCfgs.props"
          @dialogCancel="dialogCancel"
          @dialogFormSubmitSuccess="dialogFormSubmitSuccess"
        />
      </keep-alive>
    </el-dialog>
    <el-drawer
      :class="{ gantdrawer: true, ismodal: drawerCfgs.modal }"
      :style="{
        height:
          (drawerCfgs.direction == 'btt' || drawerCfgs.direction == 'ttb') &&
          drawerCfgs.size
            ? drawerCfgs.size
            : '100%',
        width:
          (drawerCfgs.direction == 'ltr' || drawerCfgs.direction == 'rtl') &&
          drawerCfgs.size
            ? drawerCfgs.size
            : '100%'
      }"
      :title="drawerCfgs.title"
      :size="drawerCfgs.size"
      :visible.sync="drawerShow"
      :modal="drawerCfgs.modal"
      :wrapper-closable="drawerCfgs.wrapperClosable"
      :modal-append-to-body="drawerCfgs.modalAppendToBody"
      :direction="drawerCfgs.direction"
      :with-header="drawerCfgs.withHeader"
    >
      <keep-alive>
        <component
          :is="drawerCfgs.component"
          v-if="drawerShow"
          v-bind="drawerCfgs.props"
          @drawerCancel="drawerCancel"
          @taskDetailUpdate="taskDetailUpdate"
        />
      </keep-alive>
    </el-drawer>
  </div>
</template>
<script>
import { gantt, Gantt } from '@components/gantt'
import '@components/gantt/codebase/dhtmlxgantt.css'
import './index.scss'
import TaskDetail from './components/TaskDetail'
import PlanVersions from './components/PlanVersions'
import CalendarSet from './components/CalendarSet'
import PlanTargetSet from './components/PlanTargetSet'
import FillSet from './components/FillSet'
import PlanImport from './components/PlanImport'
import { CONSTRAINT_TYPES } from '@/api/plan/index'
import { formatDate } from 'element-ui/src/utils/date-util'

const gantdata = {
  data: [],
  links: []
}
export default {
  name: 'GanttPlan',
  components: {
    TaskDetail,
    PlanVersions,
    CalendarSet,
    PlanTargetSet,
    FillSet,
    PlanImport
  },
  props: {
    // 计划模式，0 为查看，1 为新增
    mode: {
      type: Number,
      default: 0
    },
    // wbs 模式，默认为 false，则为 task 作业模式
    wbs: {
      type: Boolean,
      default: false
    },
    // 是否显示项目基线
    baseline: {
      type: Boolean,
      default: false
    },
    // 计算基准时间
    basedate: {
      type: Date,
      default: null
    },
    plan: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogShow: false,
      dialogCfgs: {
        component: '',
        title: '',
        width: '360px'
      },
      drawerShow: false,
      drawerCfgs: {
        direction: 'btt',
        title: '',
        size: '260px',
        modal: false,
        wrapperClosable: false,
        modalAppendToBody: false,
        withHeader: true,
        component: 'TaskDetail'
      },
      deepLevel: 0, // 最深层级
      svgLayer: false, // 前锋线绘制层
      showFowardLine: false, // 是否显示前锋线
      keyword: '', // filter 任务关键字
      UNIQ_ID: 1, // 自行维护一个唯一ID，使作业在当前计划内唯一
      ganttElement: null, // 甘特图绑定元素
      currentTaskMode: '', // 当前目录层级模式
      config: {
        // 甘特图插件 services
        date_format: '%Y-%m-%d', // 解析数据以及像服务发送的格式
        task_date: '%Y-%m-%d',
        scale_height: 60 // 时间尺度的高度和grid高度
      },
      originData: null, // 原始甘特数据备份
      cachedSettings: null, // 缓存gantt services
      visibilityData: {
        data: [],
        links: []
      }, // 显示用甘特数据
      zoomConfig: {
        levels: [
          // day
          {
            name: 'day',
            scale_height: 60,
            scales: [
              { unit: 'month', format: '<strong>%Y年%m月</strong>' },
              {
                unit: 'day',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%d</span>'
              }
            ]
          },
          // week
          {
            name: 'week',
            scale_height: 60,
            scales: [
              {
                unit: 'week',
                format: function(date) {
                  var dateToStr = gantt.date.date_to_str('%m月%d')
                  var endDate = gantt.date.add(date, +6, 'day')
                  var weekNum = gantt.date.date_to_str('%W')(date)
                  return (
                    gantt.date.date_to_str('%Y年 ')(date) +
                    dateToStr(date) +
                    ' - ' +
                    dateToStr(endDate) +
                    ' 第' +
                    weekNum +
                    '周'
                  )
                }
              },
              {
                unit: 'day',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%D</span>'
              }
            ]
          },
          // month
          {
            name: 'month',
            scale_height: 60,
            scales: [
              { unit: 'year', format: '<strong>%Y年</strong>' },
              {
                unit: 'month',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%m月</span>'
              }
            ]
          },
          // quarter
          {
            name: 'quarter',
            scale_height: 60,
            scales: [
              { unit: 'year', format: '<strong>%Y年</strong>' },
              {
                unit: 'month',
                step: 3,
                format: date => {
                  const month = date.getMonth()
                  const sec = parseInt(month / 3) + 1
                  return `第${sec}季度`
                }
              }
            ]
          },
          // year
          {
            name: 'year',
            scale_height: 60,
            scales: [
              {
                unit: 'year',
                step: 5,
                format: date => {
                  const y = date.getFullYear()
                  var endy = gantt.date.add(date, +5, 'year')
                  return `<strong>${y} - ${gantt.date.date_to_str('%Y')(
                    endy
                  )}年</strong>`
                }
              },
              {
                unit: 'year',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%Y</span>'
              }
            ]
          }
        ],
        element: function() {
          return gantt.$root.querySelector('.gantt_task')
        }
      },
      /* GANTT 数据对象，用于 gantt.parse
      ● data - 定义甘特图中的任务
      　　○ id - (string, number)任务id
      　　○ start_date - (Date)任务开始日期
      　　○ text - (string)任务描述
      　　○ progress - (number) 任务完成度，0到1
      　　○ duration - (number) 在当前时间刻度下的任务持续周期
      　　○ parent - (number) 父任务的id
          ○ type - (string) 任务类型，project，task, milestone
      ● links - 定义甘特图中的任务关联线
      　　○ id - (string, number) 关联线id
      　　○ source - (number) 数据源任务的id
      　　○ target - (number) 目标源任务的id
      　　○ type - (number) 关联线类型：0 - “结束到开始”，1 - “开始到开始”，2 - “结束到结束”, 3 - “开始到结束”
          ○ lag - (number) 扩展属性，日期调节，加减单位为天
      */
      taskTemplate: {
        id: '',
        start_date: '',
        end_date: '',
        text: '',
        progress: 0,
        duration: 0,
        parent: '0',
        type: 'task'
      },
      selectedTasks: [], // 选中的任务
      Calendar: {
        // 项目日历
        week_calendar: [0, 1, 1, 1, 1, 1, 0], // 周末规则，1为上班，0为休息
        out_calendar: [
          {
            name: '五一假期',
            start_date: '2021-05-01',
            end_date: '2021-05-05',
            type: 0
          }
        ] // 例外对象数组 {name:'', start_date:'', end_date:'', type:1} type 1上班，0 休息
      }
    }
  },
  watch: {
    currentTaskMode(newMode, oldMode) {
      if (!oldMode) return
      // task或project
      console.log(newMode)
      this.foldTaskType(newMode)
    },
    Calendar() {
      console.log('reset calendar...')
      this.setGanttCalendar()
      gantt.render()
    }
  },
  created() {
    console.log('gantplan component created...')
    // props的wbs模式初始化currentTaskMode
    this.currentTaskMode = this.wbs ? 'project' : 'task'
    // 初始化配置
    for (const k in this.config) {
      console.log(k)
      gantt.config[k] = this.config[k]
    }
    gantt.config.show_task_cells = true
    // 多语言
    gantt.i18n.setLocale('cn')
    // 树图标
    gantt.templates.grid_folder = function(item) {
      return ''
    }
    // 叶节点图标
    gantt.templates.grid_file = function(item) {
      return '<i class="el-icon-link" style="margin-top:18px;margin-right:4px;color:#298DFF;"></i>'
    }
    gantt.config.columns = [
      {
        name: 'wbs',
        label:
          '<i class="el-icon-film" style="font-size:1.6em;cursor:pointer;" id="column-services"></i>',
        width: 60,
        template: gantt.getWBSCode
      },
      {
        name: 'text',
        label: `<div id="wbs2task"><span class="tag ${
          this.wbs ? 'wbs' : 'task'
        }"><span>${this.wbs ? 'W' : '作'}</span></span>作业名称</div>`,
        tree: true,
        width: 150,
        resize: true,
        template: task => {
          return task.text
        }
      },
      {
        name: 'start_date_display',
        label: '开始时间',
        align: 'center',
        width: 150,
        resize: true
      },
      {
        name: 'end_date_display',
        label: '结束时间',
        align: 'center',
        width: 150,
        resize: true
      },
      {
        name: 'duration_display',
        label: '工期(天)',
        align: 'center',
        width: 70,
        resize: true
      }
    ]
    console.log(gantt.config)
    gantt.plugins({
      auto_scheduling: true,
      marker: true,
      tooltip: true,
      fullscreen: true
    })
    // gantt.services.auto_types = true
    gantt.config.auto_scheduling = true
    gantt.config.auto_scheduling_compatibility = true
    // gantt.services.open_split_tasks = true
    // gantt.locale.labels.section_split = 'Display'
    gantt.config.layout = {
      css: 'gantt_container',
      cols: [
        {
          width: 500,
          min_width: 500,
          rows: [
            {
              view: 'grid',
              scrollX: 'gridScroll',
              scrollable: true,
              scrollY: 'scrollVer'
            },
            { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' }
          ]
        },
        { resizer: true, width: 1 },
        {
          rows: [
            { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
            { view: 'scrollbar', id: 'scrollHor', group: 'horizontal' }
          ]
        },
        { view: 'scrollbar', id: 'scrollVer' }
      ]
    }
    if (this.baseline) {
      gantt.config.row_height = 48
      gantt.config.bar_height = gantt.config.row_height / 2 - 4
      gantt.addTaskLayer({
        renderer: {
          render: function draw_planned(task) {
            if (task.Plan_Start_Date && task.Plan_End_Date) {
              var sizes = gantt.getTaskPosition(
                task,
                gantt.date.day_start(new Date(task.Plan_Start_Date)),
                gantt.date.day_start(new Date(task.Plan_End_Date))
              )
              var el = document.createElement('div')
              el.className = 'baseline'
              el.style.left = sizes.left + 'px'
              el.style.width = sizes.width + 'px'
              el.style.top = sizes.top + gantt.config.bar_height + 13 + 'px'
              return el
            }
            return false
          }
        }
      })
      // gantt.attachEvent('onTaskLoading', function(task) {
      //   task.start_date_plan = gantt.date.parseDate(
      //     task.start_date_plan,
      //     'xml_date'
      //   )
      //   task.end_date_plan = gantt.date.parseDate(
      //     task.end_date_plan,
      //     'xml_date'
      //   )
      //   return true
      // })
    }
    gantt.attachEvent('onGanttReady', () => {
      this.highlightScaleCell()

      var tooltips = gantt.ext.tooltips
      gantt.templates.tooltip_text = (start, end, task) => {
        return `<div class="custom-plan-tooltip"><header><h3>${
          task.text
        }</h3><p>${task.start_date_display} ~ ${
          task.end_date_display
        }</p></header><div>
					<b>工期：</b>${task.duration_display} 天<br/>
					<b>进度：</b>${(task.progress_display * 100).toFixed(1)}%<br/>
          <b>备注：</b>${task.Remark ?? ''}</div></div>`
      }
    })
    gantt.attachEvent('onBeforeTaskDisplay', (id, task) => {
      return this.hasKeywordInTask(this.keyword, task)
    })
    gantt.attachEvent('onAfterTaskDrag', (id, mode, e) => {
      console.log('...............')
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onGanttScroll', (left, top) => {
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onSmartRender', (left, top) => {
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onGanttRender', () => {
      this.drawSvg()
      this.highlightScaleCell()
    })
    // gantt 扩大为全屏
    gantt.attachEvent('onExpand', function() {
      const wrapper = gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 9999
    })

    // gantt 退出全屏时
    gantt.attachEvent('onCollapse', function() {
      const wrapper = gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 1
    })
    // 任务选中
    gantt.attachEvent('onTaskSelected', id => {
      this.selectedTasks = [gantt.getTask(id)]
    })
    // 取消选中
    gantt.attachEvent('onTaskUnselected', id => {
      this.selectedTasks = []
      //
      this.drawerCancel()
    })
    // 日期单元格
    gantt.templates.scale_cell_class = date => {
      if (
        gantt.date.date_to_str(gantt.config.task_date)(this.basedate) ==
        gantt.date.date_to_str(gantt.config.task_date)(date)
      ) {
        return 'basedate'
      }
    }
    // 时间线单元格
    gantt.templates.timeline_cell_class = (task, date) => {
      if (
        gantt.date.date_to_str(gantt.config.task_date)(this.basedate) ==
        gantt.date.date_to_str(gantt.config.task_date)(date)
      ) {
        return 'basedate'
      }
    }
    // 阻止默认的编辑弹出框
    gantt.attachEvent('onTaskDblClick', (id, e) => {
      // any custom logic here
      console.log(id, e)
      this.onTaskDetail()
      return false
    })
    // 阻止默认关系线弹出框
    gantt.attachEvent('onLinkDblClick', (id, e) => {
      // any custom logic here
      console.log(id, e)
      return false
    })
    // 选定状态单击取消选中
    gantt.attachEvent('onEmptyClick', e => {
      if (this.selectedTasks[0]) {
        gantt.unselectTask(this.selectedTasks[0].id)
      }
    })
    // 忽略时间
    gantt.config.work_time = true // 从计算中移除非工作时间
    gantt.config.skip_off_time = true // 隐藏图表中的非工作时间
    gantt.config.duration_unit = 'day'
    gantt.config.inherit_calendar = true
    // 取消默认周六日周末
    gantt.setWorkTime({ day: 6, hours: ['0:00-23:59'] })
    gantt.setWorkTime({ day: 0, hours: ['0:00-23:59'] })
    // 用 calendar 设置gantt
    this.setGanttCalendar()

    // 忽略日期
    gantt.ignore_time = date => {
      if (!gantt.isWorkTime(date)) return true
    }

    // 缩放配置
    gantt.ext.zoom.init(this.zoomConfig)
    gantt.ext.zoom.setLevel('day')
    gantt.$zoomToFit = false
  },
  mounted() {
    this.ganttElement = this.$refs.gantthere
    gantt.init(this.ganttElement)
    // 绑定列表头操作事件
    this.bindGanttEvents()
    //
    window.onresize = () => {
      this.drawSvg()
      this.highlightScaleCell()
    }
    // 异步模拟
    setTimeout(() => {
      this.originData = gantdata
      if (this.wbs) {
        this.visibilityData = JSON.parse(
          JSON.stringify(
            Object.assign(
              {},
              {
                data: gantdata.data.filter(
                  d => d.type === gantt.config.types.project
                ),
                links: []
              }
            )
          )
        )
      } else {
        this.visibilityData = JSON.parse(JSON.stringify(gantdata))
      }

      gantt.parse(this.visibilityData)
      this.computeLevelDeep()
      // 添加日期线
      gantt.addMarker({
        id: 'dujianguangTestMarker',
        start_date: this.basedate,
        css: 'today',
        text: '状态日期',
        title:
          '状态日期: ' +
          gantt.date.date_to_str(gantt.config.task_date)(this.basedate)
      })
    })
  },
  methods: {
    setGanttCalendar() {
      // gantt.setWorkTime({day:6, hours:['0:00-23:59']})
      // gantt.setWorkTime({date:'', hours:false})
      this.Calendar.week_calendar.forEach((d, i) => {
        if (d == 0) {
          gantt.setWorkTime({ day: i, hours: false })
        }
      })
      this.Calendar.out_calendar.forEach(ex => {
        let dateArr = []
        if (
          (!ex.start_date || !ex.end_date) &&
          (ex.start_date || ex.end_date)
        ) {
          dateArr = [ex.start_date ?? ex.end_date]
        }
        if (ex.start_date && ex.end_date) {
          const s = gantt.date.day_start(new Date(ex.start_date))
          const e = gantt.date.day_start(new Date(ex.end_date))
          const deltaDays = (e.getTime() - s.getTime()) / 1000 / 3600 / 24 + 1
          for (let i = 0; i < deltaDays; i++) {
            dateArr.push(
              formatDate(s.getTime() + i * 1000 * 3600 * 24, 'yyyy-MM-dd')
            )
          }
        }
        console.log(dateArr)
        let hours
        if (ex.type == 0) {
          hours = false
        } else {
          hours = ['0:00-23:59']
        }
        dateArr.forEach(d => {
          gantt.setWorkTime({
            date: gantt.date.day_start(new Date(d)),
            hours: hours
          })
        })
      })
    },
    zoom(step) {
      if (this.cachedSettings) {
        this.restoreConfig()
        gantt.render()
        this.cachedSettings = null
      }
      if (step > 0) {
        gantt.ext.zoom.zoomIn()
        gantt.$zoomToFit = false
      } else {
        gantt.ext.zoom.zoomOut()
        gantt.$zoomToFit = false
      }
    },
    fit() {
      gantt.$zoomToFit = !gantt.$zoomToFit
      if (!gantt.$zoomToFit) {
        this.restoreConfig()
        gantt.render()
        return
      }
      this.saveConfig()
      var project = gantt.getSubtaskDates()
      var areaWidth = gantt.$task.offsetWidth
      var scaleConfigs = this.zoomConfig.levels

      for (var i = 0; i < scaleConfigs.length; i++) {
        var columnCount = this.getUnitsBetween(
          project.start_date,
          project.end_date,
          scaleConfigs[i].scales[scaleConfigs[i].scales.length - 1].unit,
          scaleConfigs[i].scales[0].step ?? 1
        )
        if ((columnCount + 2) * gantt.config.min_column_width <= areaWidth) {
          break
        }
      }
      if (i == scaleConfigs.length) {
        i--
      }
      gantt.ext.zoom.setLevel(scaleConfigs[i].name)
      this.applyConfig(scaleConfigs[i], project)
    },
    applyConfig(config, dates) {
      gantt.config.scales = config.scales
      var lowest_scale = config.scales.reverse()[0]

      if (dates && dates.start_date && dates.end_date) {
        gantt.config.start_date = gantt.date.add(
          dates.start_date,
          -1,
          lowest_scale.unit
        )
        gantt.config.end_date = gantt.date.add(
          gantt.date[lowest_scale.unit + '_start'](dates.end_date),
          2,
          lowest_scale.unit
        )
      } else {
        gantt.config.start_date = gantt.config.end_date = null
      }

      // restore the previous scroll position
      if (config.scroll_position) {
        setTimeout(() => {
          gantt.scrollTo(config.scroll_position.x, config.scroll_position.y)
        }, 4)
      }
    },
    saveConfig() {
      var config = gantt.config
      this.cachedSettings = {}
      this.cachedSettings.scales = config.scales
      this.cachedSettings.start_date = config.start_date
      this.cachedSettings.end_date = config.end_date
      this.cachedSettings.scroll_position = gantt.getScrollState()
    },
    restoreConfig() {
      this.applyConfig(this.cachedSettings)
    },
    getUnitsBetween(from, to, unit, step) {
      var start = new Date(from)
      var end = new Date(to)
      var units = 0
      while (start.valueOf() < end.valueOf()) {
        units++
        start = gantt.date.add(start, step, unit)
      }
      return units
    },
    toggleScreen() {
      gantt.ext.fullscreen.toggle()
    },
    // 高亮多行缩放级别日期
    highlightScaleCell() {
      document.querySelectorAll('.gantt_scale_line').forEach(e => {
        e.querySelectorAll('span').forEach(s => {
          if (
            s.getAttribute('data-date') ===
            gantt.date.date_to_str(gantt.config.task_date)(this.basedate)
          ) {
            s.parentNode.classList.add('basedate')
          }
        })
      })
    },
    computeLevelDeep() {
      let max = 0
      gantt.eachTask(t => {
        if (t.$level > max) max = t.$level
      })
      this.deepLevel = max
      this.$parent.maxLevel = this.deepLevel
    },
    bindGanttEvents() {
      this.$el.onclick = evt => {
        console.log('$el clicked...', evt, evt.target, evt.target.tagName)
        // 列配置事件绑定
        if (evt.target.id === 'column-services') {
          this.dialogShow = true
        }
        // WBS 切换 task 事件
        if (
          evt.target.tagName.toLowerCase() === 'span' &&
          (evt.target.classList.contains('tag') ||
            (evt.path[1] && evt.path[1].classList.contains('tag')))
        ) {
          const lvl = this.currentTaskMode == 'task' ? 'project' : 'task'
          console.log(lvl)
          this.currentTaskMode = lvl
          this.$nextTick(() => {
            const toBtn = this.ganttElement.querySelector('#wbs2task>span.tag')
            if (lvl === 'task') {
              toBtn.classList.remove('wbs')
              toBtn.classList.add('task')
              toBtn.innerHTML = '<span>作</span>'
            } else {
              console.log(toBtn)
              toBtn.classList.remove('task')
              toBtn.classList.add('wbs')
              toBtn.innerHTML = '<span>W</span>'
            }
          })
        }
      }
    },

    // 任务关键字筛选
    filterKeyword(key) {
      this.keyword = key
      gantt.refreshData()
    },
    hasKeywordInTask(k, task) {
      const taskStr = JSON.stringify(task)
      return taskStr.indexOf(k) > -1
    },
    // 折叠树层级
    foldTaskLevel(level) {
      // gantt.eachTask(task => {
      //   gantt.open(task.id)
      //   if(level!==0){
      //     if(task.$level >= this.deepLevel - level) {
      //       console.log(task, this.deepLevel - level)
      //       gantt.close(task.id)
      //     }
      //   }
      // })

      // 批量更新效率较高
      gantt.batchUpdate(() => {
        var tasks = gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          const position = gantt.getTaskPosition(task)
          // console.log(position)
          task.$open = true
          if (level !== 0) {
            if (task.$level >= this.deepLevel - level) {
              task.$open = false
            }
          }
          gantt.updateTask(task.id)
        }
      })
    },
    // 折叠树类型
    foldTaskType(showType) {
      gantt.batchUpdate(() => {
        var tasks = gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          task.$open = true
          if (showType === 'project') {
            const childs = tasks.filter(t => t.parent === task.id)
            // 折叠 project 类,并且无project类子元素
            if (
              task.type == 'project' &&
              !childs.find(c => c.type == 'project')
            ) {
              task.$open = false
            }
          }
          gantt.updateTask(task.id)
        }
      })
    },
    onTaskAdd() {
      console.log('on task add')
      const parent = this.selectedTasks[0] ?? { id: '0' }
      if (parent.id !== '0' && parent.type != 'project') {
        return this.$message.warning('只能在WBS条目下新建任务')
      }
      gantt.addTask(
        this.createEmptyTask({
          parent: parent.id,
          type: 'task',
          start_date:
            parent.start_date ??
            gantt.date.date_to_str(gantt.config.task_date)(new Date()),
          Plan_Start_Date:
            parent.Plan_Start_Date ??
            gantt.date.date_to_str(gantt.config.task_date)(new Date())
        })
      )
      return
      this.openDrawer({
        title: '新建工作任务'
      })
    },
    createEmptyTask(initOpts) {
      const o = {
        ...this.taskTemplate,
        ...initOpts,
        id: this.UNIQ_ID.toString(),
        text: `新任务 ${this.UNIQ_ID}`,
        end_date: gantt.date.date_to_str(gantt.config.task_date)(
          gantt.date.add(new Date(initOpts.start_date), 1, 'day')
        ),
        duration: 1,
        progress: 0,
        Constraint_Type: 0,
        Constraint_Date: '',
        Plan_End_Date: gantt.date.date_to_str(gantt.config.task_date)(
          gantt.date.add(new Date(initOpts.Plan_Start_Date), 1, 'day')
        )
      }
      o.start_date_display = gantt.date.date_to_str(gantt.config.task_date)(
        new Date(o.start_date)
      )
      o.end_date_display = gantt.date.date_to_str(gantt.config.task_date)(
        gantt.date.add(new Date(o.end_date), -1, 'day')
      )
      o.duration_display = 1
      o.progress_display = 0
      o.Plan_Duration = 1
      console.log(o)
      this.UNIQ_ID++
      return o
    },
    onPlanImport() {
      this.openDialog({
        title: '导入',
        width: '450px',
        component: 'PlanImport',
        props: {
          action: `${this.$baseUrl}/Plan/Plan/ImportPlan`,
          exts: ['mpp'],
          filesize: 50
        }
      })
    },
    onTaskDelete() {
      if (this.selectedTasks.length <= 0) {
        return this.$message.warning('当前没有选中的任务')
      }
      const task = this.selectedTasks[0]
      this.$confirm(`确认要删除${task.text}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          gantt.deleteTask(task.id)
          this.selectedTasks = []
        })
        .catch(() => {})
    },
    onTaskDetail() {
      if (this.selectedTasks.length <= 0) {
        return this.$message.warning('先选择一个工作任务')
      }
      this.openDrawer({
        title: '查看工作任务',
        withHeader: false,
        component: 'TaskDetail',
        direction: 'btt',
        size: '260px',
        wrapperClosable: true,
        props: {
          tabname: 'profile',
          task: this.selectedTasks[0],
          constraints: CONSTRAINT_TYPES
        }
      })
    },
    onTaskVersionsOpen() {
      this.openDrawer({
        title: '历史版本查看',
        withHeader: true,
        direction: 'rtl',
        component: 'PlanVersions',
        size: '300px',
        wrapperClosable: false,
        props: {
          historys: [{ Name: 'A' }, { Name: 'B' }]
        }
      })
    },
    onTaskCalendarOpen() {
      this.openDialog({
        title: '日历设置',
        width: '950px',
        component: 'CalendarSet',
        props: {
          origin: this.Calendar
        }
      })
    },
    onPlanTargetOpen() {
      this.openDialog({
        title: '目标计划设置',
        width: '50%',
        component: 'PlanTargetSet',
        props: {
          plan: this.plan
        }
      })
    },
    onFillSetOpen() {
      console.log(333333)
      this.openDialog({
        title: '填报任务设置',
        width: '50%',
        component: 'FillSet',
        props: {
          plan: this.plan
        }
      })
    },
    drawerCancel() {
      this.drawerShow = false
    },
    dialogCancel() {
      this.dialogShow = false
    },
    dialogFormSubmitSuccess({ type, data }) {
      console.log(type, data)
      this.dialogCancel()
      switch (type) {
        case 'setCalendar':
          this.setCalendar(data)
          break
      }
    },
    setCalendar(cldr) {
      console.log(cldr)
      this.Calendar = cldr
    },
    openDialog(opts) {
      if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
        opts = {}
      }
      this.dialogCfgs = Object.assign({}, this.dialogCfgs, opts, {})
      this.dialogShow = true
    },
    openDrawer(opts) {
      if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
        opts = {}
      }
      this.drawerCfgs = Object.assign({}, this.drawerCfgs, opts, {})
      console.log(this.drawerCfgs)
      this.drawerShow = true
    },
    // 绘制前锋线
    drawProcessLine() {
      if (!this.basedate) return
      console.log(gantt)

      this.drawSvg()
    },
    serialize() {
      return gantt.serialize('json')
    },
    taskDetailUpdate({ task_id, field, value }) {
      console.log(task_id, field, value)
      const task = gantt.getTask(task_id)
      if (field === 'Plan_End_Date') {
        task[field] = gantt.date.date_to_str(gantt.config.task_date)(
          gantt.date.add(new Date(value), 1, 'day')
        )
      }
      gantt.updateTask(task_id)
      console.log(task)
    },
    drawSvg() {
      // this.svgLayer = document.createElementNS(
      //   'http://www.w3.org/2000/svg',
      //   'svg'
      // )
      // const wrapper = this.ganttElement.querySelector('.gantt_task_bg')
      // console.log(wrapper)
      // wrapper.appendChild(this.svgLayer)
      var strID = 'dujianguangtest'
      var strContainerCss = '.gantt_links_area'
      // "gantt_layout_cell  timeline_cell gantt_layout_outer_scroll gantt_layout_outer_scroll_vertical
      // gantt_layout_outer_scroll gantt_layout_outer_scroll_horizontal";
      var divContainer = document.querySelector(strContainerCss)
      if (!this.showFowardLine) {
        // 清除前锋线
        var ele = document.getElementById(strID)
        // console.log(ele)
        if (ele) {
          divContainer.removeChild(ele)
        }
        this.svgLayer = null
        return
      }
      var pathAll = ''
      var s1 = gantt.config.start_date
      if (s1 == null) s1 = gantt._min_date
      const s2 = this.basedate
      var date_status = new Date(s2.getFullYear(), s2.getMonth(), s2.getDate())
      // s2 = new Date(2018,9,9);
      var timeMilliSecond = date_status.getTime() - s1.getTime()
      var days = parseInt(timeMilliSecond / (1000 * 60 * 60 * 24))
      var divMarker = document.querySelector(
        "[data-marker-id='dujianguangTestMarker']"
      )
      // 状态日期的x坐标，天数*最小列宽度
      // var startLeft=days*gantt.services.min_column_width ;//divDataDate.offsetLeft;
      console.dir(divMarker)
      if (!divMarker) return
      console.log('continue drawing...')
      const startLeft = divMarker.offsetLeft
      let lefPre = startLeft
      let topPre = 4
      var colorLine = 'red' // 线条颜色
      var $divtasks = document.querySelectorAll('.gantt_bars_area')
      console.log($divtasks)
      // 杜建光 按照top排序 否则错位
      var arr = []
      $divtasks[0].querySelectorAll('.gantt_bar_task').forEach(n => {
        arr.push(n)
      })
      console.log(arr)
      arr.sort(function(a, b) {
        var ai = parseFloat(a.style.top, 10)
        var bi = parseFloat(b.style.top, 10)
        if (ai > bi) {
          return 1
        } else if (ai < bi) {
          return -1
        } else {
          return 0
        }
      })

      // 原计算路径函数
      function calcLineOrigin() {
        // var left = sizes.left + sizes.width * task.progress_clc;
        var left = sizes.left + sizes.width * task.progress
        var top = sizes.top + gantt.config.bar_height + 13
        var topMiddle = top
        console.log(task, sizes)
        // 如果计划不为里程碑计划和WBS计划，可以绘制
        if (
          task.type != gantt.config.types.milestone &&
          task.type != gantt.config.types.project
        ) {
          if (
            (left <= startLeft && task.progress >= 0 && task.progress < 1) ||
            (left >= startLeft && task.progress > 0 && task.progress <= 1)
          ) {
            pathAll =
              pathAll +
              '<path d="M' +
              lefPre +
              ',' +
              topPre +
              ' L' +
              left +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            console.log(topPre, topMiddle, topPre)
            var strCircle =
              '<circle cx="' +
              left +
              '" cy="' +
              (topPre + (topMiddle - topPre) / 2) +
              '" r="3" style="stroke:' +
              colorLine +
              '; fill:none;" />'
            if (left != startLeft) pathAll = pathAll + strCircle
            pathAll =
              pathAll +
              '<path d="M' +
              left +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              ' L' +
              startLeft +
              ',' +
              top +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            lefPre = startLeft
            topPre = top
          } else {
            pathAll =
              pathAll +
              '<path d="M' +
              lefPre +
              ',' +
              topPre +
              ' L' +
              startLeft +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            var strCircle =
              '<circle cx="' +
              startLeft +
              '" cy="' +
              (topPre + (topMiddle - topPre) / 2) +
              '" r="3" style="stroke:' +
              colorLine +
              '; fill:none;" />'
            // if (left != startLeft) pathAll = pathAll + strCircle;
            pathAll =
              pathAll +
              '<path d="M' +
              startLeft +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              ' L' +
              startLeft +
              ',' +
              top +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            lefPre = startLeft
            topPre = top
          }
        } else {
          pathAll =
            pathAll +
            '<path d="M' +
            lefPre +
            ',' +
            topPre +
            ' L' +
            startLeft +
            ',' +
            topMiddle +
            '"style="stroke:' +
            colorLine +
            '; fill:none;"/>'
          var strCircle =
            '<circle cx="' +
            startLeft +
            '" cy="' +
            topMiddle +
            '" r="3" style="stroke:' +
            colorLine +
            '; fill:none;" />'
          // if (left != startLeft) pathAll = pathAll + strCircle;
          pathAll =
            pathAll +
            '<path d="M' +
            startLeft +
            ',' +
            topMiddle +
            ' L' +
            startLeft +
            ',' +
            top +
            '"style="stroke:' +
            colorLine +
            '; fill:none;"/>'
          lefPre = startLeft
          topPre = top
        }
      }
      // 新计算路径函数
      function calcLineNew() {
        // console.log(sizes, task)
        let moveto, lineto
        moveto = [
          sizes.left + sizes.width * task.progress,
          sizes.top + topPre + gantt.config.bar_height / 2
        ]
        lineto = [startLeft, sizes.top + topPre]
        pathAll += `<path d="M${moveto[0]},${moveto[1]} L${lineto[0]}, ${lineto[1]} Z" style="stroke:${colorLine}; fill:none;"/>`
        pathAll += `<path d="M${moveto[0]}, ${moveto[1]} L${
          lineto[0]
        }, ${lineto[1] +
          gantt.config.bar_height} Z" style="stroke:${colorLine}; fill:none;"/>`
        let circlePad = 0
        if (moveto[0] < startLeft) {
          circlePad = -3
        } else if (moveto[0] > startLeft) {
          circlePad = 3
        }
        pathAll += `<circle cx="${moveto[0] + circlePad}" cy="${
          moveto[1]
        }" r="3" style="stroke:${colorLine}; fill:none;" />`
      }
      // for(var i=0;i<$divtasks[0].childElementCount;i++){
      for (var i = 0; i < arr.length; i++) {
        // var $ele=$divtasks[0].children[i];
        var ele = arr[i]

        var taskid = ele.getAttribute('task_id')
        // 根据当前任务画前锋线
        var task = gantt.getTask(taskid)
        // 根据对比计划画前锋线
        console.log(task.start_date_plan)
        task.start_date_contrast = task.start_date_plan // 先改造个数据
        // 如果有对比计划显示，可以绘制前锋线
        if (task.start_date_contrast) {
          var planned_start = gantt.date.add(
            new Date(task.start_date_contrast),
            0,
            'day'
          )
          var planned_end = gantt.date.add(
            gantt.calculateEndDate(
              new Date(task.start_date_contrast),
              task.duration
            ),
            0,
            'day'
          )
          console.log(planned_start, planned_end)
          var sizes = gantt.getTaskPosition(task, planned_start, planned_end)
          // 计算路径
          // calcLineOrigin()
          calcLineNew()
        }
      }
      // console.log(pathAll)
      // gantt_data_area
      var divDataArea = document.querySelector('.gantt_task_bg')
      console.log(divDataArea)
      var svgWidth = divDataArea.offsetWidth
      var svgHeight = divDataArea.offsetHeight
      var QFline =
        '<svg style="width:' +
        svgWidth +
        'px;height:' +
        svgHeight +
        'px;">' +
        pathAll +
        '</svg>'
      var ele = document.getElementById(strID)
      if (ele) {
        divContainer.removeChild(ele)
      }
      var linechild = document.createElement('div')
      linechild.id = 'dujianguangtest'
      linechild.className = 'divLine'
      // 把前锋线插入到图层中
      linechild.innerHTML = QFline
      divContainer.appendChild(linechild)
      this.svgLayer = document.querySelector('#' + strID).querySelector('svg')
      console.log(divContainer)
    }
  }
}
</script>

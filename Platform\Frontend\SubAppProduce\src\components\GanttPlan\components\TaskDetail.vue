<template>
  <div class="task-detail">
    <el-link
      :underline="false"
      icon="el-icon-close"
      class="closeme"
      @click="closeMe"
    />
    <el-tabs :value="tabname" type="border-card">
      <el-tab-pane label="作业状态" name="profile">
        <el-row :gutter="20">
          <el-col :span="9">
            <div class="col-block">
              <div class="head">基本信息</div>
              <el-form
                ref="form1"
                :model="form"
                label-width="90px"
                style="width:100%"
              >
                <el-row :gutter="16">
                  <el-col :span="24">
                    <el-form-item label="作业名称" size="mini">
                      <el-input v-model="form.text" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="责任人" size="mini">
                      <el-select
                        v-model="form.Responsible_User"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in [
                            { Name: 'A', Id: 'a' },
                            { Name: 'B', Id: 'b' }
                          ]"
                          :key="item.Id"
                          :label="item.Name"
                          :value="item.Id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="完成进度" size="mini">
                      <el-input v-model="form.Actual_Progress" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="24">
                    <el-form-item label="作业类型" size="mini">
                      <el-radio
                        v-model="form.type"
                        disabled
                        label="project"
                      >WBS</el-radio>
                      <el-radio
                        v-model="form.type"
                        disabled
                        label="task"
                      >作业</el-radio>
                      <el-radio
                        v-model="form.type"
                        disabled
                        label="milestone"
                      >里程碑</el-radio>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="限制条件" size="mini">
                      <el-select
                        v-model="form.Constraint_Type"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in constraints"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="限制日期" size="mini">
                      <el-date-picker
                        v-model="form.Constraint_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>
          <el-col :span="9">
            <div class="col-block">
              <div class="head">时间参数</div>
              <el-form
                ref="form2"
                :model="form"
                label-width="90px"
                style="width:100%"
              >
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划开始" size="mini">
                      <el-date-picker
                        v-model="form.Plan_Start_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际开始" size="mini">
                      <el-date-picker
                        v-model="form.Actual_Start_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划完成" size="mini">
                      <el-date-picker
                        v-model="form.Plan_End_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                        @change="formChange('Plan_End_Date', $event)"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际完成" size="mini">
                      <el-date-picker
                        v-model="form.Actual_End_Date"
                        class="simon-date-picker"
                        style="width:100%;"
                        value-format="yyyy-MM-dd"
                        type="date"
                        placeholder="选择日期"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划工期" size="mini">
                      <el-input v-model="form.Plan_Duration" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际工期" size="mini">
                      <el-input v-model="form.Actual_Duration" />
                    </el-form-item>
                  </el-col> </el-row><el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="计划人工" size="mini">
                      <el-input v-model="form.name" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="实际人工" size="mini">
                      <el-input v-model="form.name" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="col-block">
              <div class="head">作业备注</div>
              <el-input
                v-model="form.Remark"
                type="textarea"
                style="margin-left:20px;height:100%"
              />
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="逻辑关系" name="relation">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="col-block">
              <div class="head">前置作业</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="[{}, {}]"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column label="a" prop="a" align="center" />
                  <el-table-column label="b" prop="b" align="center" />
                  <el-table-column label="c" prop="c" align="center" />
                </el-table>
              </div>
              <div class="rfix">
                <el-button size="mini" type="primary">分配</el-button>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="col-block">
              <div class="head">后置作业</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="[{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column label="a" prop="a" align="center" />
                  <el-table-column label="b" prop="b" align="center" />
                  <el-table-column label="c" prop="c" align="center" />
                </el-table>
              </div>
              <div class="rfix">
                <el-button size="mini" type="primary">分配</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane label="填报历史" name="history">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="col-block">
              <div class="head">填报历史</div>
              <div class="rlist">
                <el-table
                  height="190"
                  stripe
                  :data="[{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]"
                  :cell-style="{ border: 'none' }"
                >
                  <el-table-column label="a" prop="a" align="center" />
                  <el-table-column label="b" prop="b" align="center" />
                  <el-table-column label="c" prop="c" align="center" />
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { gantt, Gantt } from '@components/gantt'
export default {
  name: 'TaskDetail',
  props: {
    tabname: {
      type: String,
      default: 'profile'
    },
    task: {
      type: Object,
      default: () => ({})
    },
    constraints: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {}
    }
  },
  watch:{
    form(nv){
      console.log(nv)
    }
  },
  created() {
    console.log('drawer content created...')
    this.form = {
      ...this.task,
      Actual_Progress: this.task.Actual_Progress ?? 0,
      Plan_End_Date: gantt.date.add(new Date(this.task.Plan_End_Date), -1, 'day')
    }
    console.log(this.form)
  },
  methods: {
    closeMe() {
      this.$emit('drawerCancel')
    },
    formChange(k,v){
      console.log(k,v)
      this.$emit('taskDetailUpdate', {
        task_id: this.task.id,
        field:k,
        value:v
      })
    }
  }
}
</script>
<style lang="scss">
.simon-date-picker {
  .item.el-input__inner {
    padding-left: 36px !important;
  }
}
</style>

<template>
  <el-tree-select
    ref="treeSelectArea"
    v-model="selectedValue"
    :disabled="(!projectId && !nullIsFetch) || disabled"
    :select-params="{
      clearable: true,
      filterable:true,
      placeholder: '请输入',
      multiple:true,
      'collapse-tags':true
    }"
    class="cs-tree-x"
    :tree-params="treeParamsArea"
    @searchFun="searchFun"
  />
</template>
<script>
/**
 * 选择区域
 */
import { deepClone } from '@/utils'
import { GetProjectAreaTree } from '@/api/plm/projects'

export default {
  name: 'SelectArea',
  props: {
    value: {
      type: [Array, Number, String],
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    projectId: {
      type: String,
      default: ''
    },
    /**
     * projectId为空时，是否获取数据,如果为否，projectId为空时则禁用
     */
    nullIsFetch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      selectedValue: Array.isArray(this.value) ? deepClone(this.value) : this.value,
      treeParamsArea: {
        'default-expand-all': true,
        filterable: true,
        clickParent: false,
        'check-strictly': false,
        leafOnly: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      }
    }
  },
  watch: {
    projectId() {
      this.getList()
    },
    value: {
      handler(newVal) {
        // 避免循环触发，只有当值真正不同时才更新
        const newValue = Array.isArray(newVal) ? deepClone(newVal) : newVal
        if (JSON.stringify(this.selectedValue) !== JSON.stringify(newValue)) {
          this.selectedValue = newValue
        }
      },
      immediate: true
    },
    selectedValue: {
      handler(newVal) {
        // 避免循环触发，只有当值真正不同时才emit
        if (JSON.stringify(this.value) !== JSON.stringify(newVal)) {
          this.handleChange()
        }
      },
      deep: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange() {
      // 过滤出只有叶子节点的值
      const leafValues = this.filterLeafNodes(this.selectedValue)
      this.$emit('input', leafValues)
      this.$emit('change', leafValues)
    },
    // 过滤出叶子节点
    filterLeafNodes(values) {
      if (!Array.isArray(values) || !this.treeParamsArea.data.length) {
        return values
      }

      const allNodes = this.getAllNodes(this.treeParamsArea.data)
      const leafNodeIds = allNodes
        .filter(node => !node.Children || node.Children.length === 0)
        .map(node => node.Id)

      return values.filter(value => leafNodeIds.includes(value))
    },
    // 获取所有节点的扁平化数组
    getAllNodes(nodes) {
      let result = []
      nodes.forEach(node => {
        result.push(node)
        if (node.Children && node.Children.length > 0) {
          result = result.concat(this.getAllNodes(node.Children))
        }
      })
      return result
    },
    async getList() {
      if (!this.projectId && !this.nullIsFetch) return
      GetProjectAreaTree({
        projectId: this.projectId
      }).then(res => {
        this.$nextTick((_) => {
          this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
        })
      })
    },
    searchFun(val) {
      this.$refs.treeSelectArea.$refs.tree.filter(val)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep{
  .el-select{
    width: 100%!important;
  }
}
</style>

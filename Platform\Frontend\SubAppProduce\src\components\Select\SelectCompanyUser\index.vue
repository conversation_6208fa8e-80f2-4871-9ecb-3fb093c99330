<template>
  <el-select
    v-model="value"
    placeholder="请选择"
    style="width: 100%"
    clearable
    filterable
    multiple
    :disabled="(!departmentId && !nullIsFetch) || disabled"
    @change="handleChange"
  >
    <el-option
      v-for="item in list"
      :key="item.Id"
      :label="item.Display_Name"
      :value="item.Id"
    />
  </el-select>
</template>
<script>
/**
 * 选择公司下面的鹅人员
 */
import { GetUserPage } from '@/api/sys'

export default {
  name: 'SelectCompanyUser',
  props: {
    value: {
      type: String,
      default: ''
    },
    departmentId: {
      type: String,
      default: ''
    },
    /**
     * departmentId为空时，是否获取数据,如果为否，departmentId为空时则禁用
     */
    nullIsFetch: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: []
    }
  },
  watch: {
    departmentId() {
      this.getList()
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleChange(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    },
    async getList() {
      if (!this.departmentId && !this.nullIsFetch) return
      await GetUserPage({
        DepartmentId: this.departmentId,
        PageSize: 10000
      }).then(res => {
        this.list = res.Data.Data.filter(item => item.UserStatusName === '正常')
        if (!this.list.some(user => user.Id === this.value)) {
          this.handleChange('')
        }
      })
    }
  }
}
</script>

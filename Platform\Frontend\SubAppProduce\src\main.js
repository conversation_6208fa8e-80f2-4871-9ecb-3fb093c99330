import './public-path'
import Vue from 'vue'

// import { handleAddRouterPage } from '@/utils'
// import componentMap from '@/utils/componentMap'
import { transferRoutesWithParent, getRoutesWithParent } from '@/utils/tempRouterMap'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './styles/element-variables.scss'
import 'element-ui/lib/theme-chalk/icon.css'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router, { resetRouter, constantRoutes } from './router'

import './icons' // icon
import './icons/icons/circleFont/iconfont.css'

import './permission' // permission control
import './utils/error-log' // error log

import * as filters from './filters' // global filters
import ElTreeSelect from 'el-tree-select'

import VueContextMenu from 'vue-contextmenu'

import BimtkUI from '@components/BimtkUI'

import Print from '@/utils/print/print-label'
import '@/utils/languagechange'
import '@/utils/math'

import XEUtils from 'xe-utils'
import VXETable from 'vxe-table'

import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'

import 'vxe-table/lib/style.css'
import '@/utils/directives.js'

// VxeUI.setConfig({
//   emptyCell: '88'
// })
import PortalVue from 'portal-vue'
Vue.use(BimtkUI, {
  baseURL: `${platformUrl()}Platform`, // 设置表格组件的API基础URL(注意，这里实际要写到baseURL的下一级，有些系统里面用的是SYS，有些用的是Platform)
  tablePageSizes: tablePageSize, // 设置表格组件的分页大小选项（默认[10, 20, 50, 100]）
  tablePageSize: tablePageSize[0] // 设置表格组件的默认分页大小（默认20）
})
Vue.use(PortalVue)
VXETable.setup({
  version: 0,
  zIndex: 9999,
  emptyCell: '-',
  icon: {
    TABLE_TREE_OPEN: 'cs-vxe-tree-up',
    TABLE_TREE_CLOSE: 'cs-vxe-tree-down'
  },
  table: {
    border: true,
    showHeaderOverflow: true,
    autoResize: true,
    stripe: true,
    keepSource: true,
    scrollY: {
      enabled: true, // 是否默认开启纵向虚拟滚动
      gt: 50 // 当数据大于指定数量时自动触发启用虚拟滚动
    }
  }
})
VXETable.renderer.add('NotData', {
  // 空内容模板
  renderEmpty(h, renderOpts) {
    return (
      <div style={{
        textAlign: 'center',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center'
      }}>
        <div style={{
          width: '100%',
          height: '70%',
          maxWidth: '400px',
          maxHeight: '266px',
          backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' width='400' height='266' viewBox='0 0 400 266'%3E%3Cdefs%3E%3Cstyle%3E.a%7Bfill:none%7D.f%7Bfill:url(%23e)%7D.g%7Bfill:url(%23f)%7D.l%7Bfill:url(%23m)%7D%3C/style%3E%3CclipPath id='a'%3E%3Cpath class='a' transform='translate(-.424 -.313)' d='M0 0h400v133H0z'/%3E%3C/clipPath%3E%3ClinearGradient id='b' x1='.5' y1='1.351' x2='.5' y2='-.755' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.39' stop-color='%23fff' stop-opacity='0'/%3E%3Cstop offset='.91' stop-color='%239bcaff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='.499' y1='1.028' x2='.5' y2='.039' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23bfd4f3'/%3E%3Cstop offset='1' stop-color='%23e7edf8'/%3E%3C/linearGradient%3E%3ClinearGradient id='d' x1='.507' y1='1.397' x2='.486' y2='-.106' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23c9daf4'/%3E%3C/linearGradient%3E%3ClinearGradient id='e' x1='.5' y1='1.033' x2='.5' y2='.035' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e7edf8'/%3E%3Cstop offset='1' stop-color='%23fefefe'/%3E%3C/linearGradient%3E%3ClinearGradient id='f' x1='.5' y1='1.039' x2='.5' y2='.029' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23cbd8ee'/%3E%3Cstop offset='1' stop-color='%23cfdcef'/%3E%3C/linearGradient%3E%3ClinearGradient id='i' y1='1.032' y2='.038' xlink:href='%23e'/%3E%3ClinearGradient id='j' y1='1.036' y2='.036' xlink:href='%23f'/%3E%3ClinearGradient id='k' x1='.507' y1='2.589' x2='.496' y2='-.667' gradientUnits='objectBoundingBox'%3E%3Cstop offset='.52' stop-color='%23c9daf4'/%3E%3Cstop offset='.92' stop-color='%23fff'/%3E%3C/linearGradient%3E%3ClinearGradient id='l' y1='.5' x2='1' y2='.5' gradientUnits='objectBoundingBox'%3E%3Cstop offset='0' stop-color='%23e0e9f7'/%3E%3Cstop offset='1' stop-color='%23e9eef9'/%3E%3C/linearGradient%3E%3ClinearGradient id='m' y1='.5' x2='1' y2='.5' xlink:href='%23c'/%3E%3ClinearGradient id='n' x2='.999' xlink:href='%23l'/%3E%3ClinearGradient id='o' x1='-.001' y1='.502' x2='1' y2='.502' xlink:href='%23c'/%3E%3ClinearGradient id='p' x2='1.001' xlink:href='%23l'/%3E%3ClinearGradient id='r' x1='.5' y1='1.351' y2='-.755' xlink:href='%23c'/%3E%3C/defs%3E%3Cpath class='a' d='M0 0h400v266H0z'/%3E%3Cg transform='translate(.424 133.313)' clip-path='url(%23a)'%3E%3Cpath d='M182.733 0c.807 0 1.614 0 2.543.013 99.682.94 180.191 60.074 180.191 132.887 0 73.4-81.813 132.9-182.733 132.9S0 206.294 0 132.9 81.813 0 182.733 0z' transform='translate(17.01 -.434)' fill='url(%23b)'/%3E%3C/g%3E%3Cg transform='translate(136.21 67.996)'%3E%3Cpath d='M386.493 135.75h-91.046a9.811 9.811 0 0 0-9.787 9.787v1.963h19.575v80.264a16.891 16.891 0 0 0 16.843 16.836h63.648a7.625 7.625 0 0 0 7.607-7.6v-94.4a6.876 6.876 0 0 0-6.84-6.85z' transform='translate(-285.648 -135.75)' fill='url(%23c)'/%3E%3Cpath d='M295.391 135.75h.048a9.751 9.751 0 0 1 9.775 9.733v2H285.64v-2a9.751 9.751 0 0 1 9.751-9.733z' transform='translate(-285.64 -135.75)' fill='url(%23d)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 18.586)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 23.725)'/%3E%3Crect class='f' width='52.471' height='5.091' rx='2.546' transform='translate(37.394 34.705)'/%3E%3Crect class='g' width='50.153' height='1.845' rx='.922' transform='translate(38.557 39.844)'/%3E%3Crect width='32.099' height='5.091' rx='2.546' transform='translate(37.394 51.77)' fill='url(%23i)'/%3E%3Crect width='30.68' height='1.845' rx='.922' transform='translate(38.107 56.909)' fill='url(%23j)'/%3E%3Cpath d='M353.011 294.44s1.605 14.118-9.751 13.807h82.959s9.871-.1 9.757-13.807z' transform='translate(-308.747 -199.387)' fill='url(%23k)'/%3E%3C/g%3E%3Cg transform='translate(284.941 175.523)'%3E%3Cellipse cx='6.199' cy='1.875' rx='6.199' ry='1.875' transform='translate(2.713 16.199)' fill='url(%23l)'/%3E%3Cpath class='l' d='M12.099 6.081a6.05 6.05 0 1 0-10.38 4.193 2.4 2.4 0 0 0-.377 1.288 2.438 2.438 0 0 0 2.438 2.432h1.743v3.528a.533.533 0 1 0 1.06 0v-3.528h1.749a2.438 2.438 0 0 0 2.432-2.432 2.433 2.433 0 0 0-.371-1.288 6.032 6.032 0 0 0 1.706-4.193z'/%3E%3C/g%3E%3Cg transform='translate(123.952 163.037)'%3E%3Cellipse cx='4.283' cy='1.294' rx='4.283' ry='1.294' transform='translate(1.893 11.237)' fill='url(%23n)'/%3E%3Cpath d='M280.266 298.631a4.193 4.193 0 1 0-7.188 2.917 1.689 1.689 0 0 0-.257.892 1.7 1.7 0 0 0 1.689 1.689h1.2v2.45a.371.371 0 0 0 .737 0v-2.45h1.2a1.7 1.7 0 0 0 1.689-1.689 1.69 1.69 0 0 0-.258-.893 4.193 4.193 0 0 0 1.192-2.917z' transform='translate(-271.88 -294.421)' fill='url(%23o)'/%3E%3C/g%3E%3Cg transform='translate(100.28 180.825)'%3E%3Cellipse cx='5.205' cy='1.575' rx='5.205' ry='1.575' transform='translate(2.276 13.586)' fill='url(%23p)'/%3E%3Cpath class='l' d='M10.159 5.082a5.079 5.079 0 1 0-8.721 3.534A2.048 2.048 0 0 0 1.121 9.7a2.055 2.055 0 0 0 2.049 2.049h1.47v2.959a.444.444 0 0 0 .887 0v-2.959h1.45A2.055 2.055 0 0 0 9.02 9.7a2.044 2.044 0 0 0-.311-1.084 5.056 5.056 0 0 0 1.456-3.534z'/%3E%3C/g%3E%3Cpath d='M524.958 133.034c0-5.744-7.966-10.4-17.8-10.4s-17.8 4.654-17.8 10.4c0 4.744 5.511 8.518 12.854 10a3.139 3.139 0 0 1 2.21 4.6v.036a.18.18 0 0 0 .228.246c7.739-3.282 11.944-5.553 14.232-7.044 3.718-1.914 6.076-4.717 6.076-7.838zm-9.41-2.174a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.386 0a2.186 2.186 0 1 1-2.4 2.174 2.288 2.288 0 0 1 2.4-2.18zm-8.3 4.355a2.186 2.186 0 1 1 2.4-2.18 2.294 2.294 0 0 1-2.4 2.174z' transform='translate(-230.157 -67.285)' fill='url(%23r)'/%3E%3C/svg%3E")`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          margin: '0 auto'
        }}/>
        <p>暂无内容</p>
      </div>
    )
  }
})

Vue.use(VXETable)
VXETable.use(VXETablePluginExportXLSX)

// 把XEUTils绑定到this中，方便在组件中通过 this 访问使用
Vue.XEUtils = Vue.prototype.$XEUtils = XEUtils
// 自定义全局的格式化处理函数
VXETable.formats.mixin({
  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss
  formatDate({ cellValue }, format) {
    return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd')
  },
  // 格式化日期，默认 yyyy-MM-dd HH:mm:ss
  formatDateTime({ cellValue }, format) {
    return XEUtils.toDateString(cellValue, format || 'yyyy-MM-dd HH:mm:ss')
  },
  // 四舍五入金额，每隔3位逗号分隔，默认2位数
  formatAmount({ cellValue }, digits = 2) {
    if (Math.abs(cellValue) < 0.0001) return 0
    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
  },
  formatNum({ cellValue }, digits = 2) {
    if (Math.abs(cellValue) < 0.0001) return 0
    const n = '0'.repeat(digits)
    return numeral(cellValue).format(`0,0.[${n}]`)
  },
  formatCustom({ cellValue }, format) {
    return format(cellValue)
  }
})

import VueClipboards from 'vue-clipboard2'
import { baseUrl, platformUrl } from '@/utils/baseurl'
import numeral from 'numeral'
import { tablePageSize } from '@/views/PRO/setting'

Vue.prototype.$baseUrl = baseUrl()
Vue.use(VueClipboards)

Vue.use(ElTreeSelect)
Vue.use(VueContextMenu)
Vue.use(Print)

Vue.use(Element, {
  size: 'small'
})

const $message = options => {
  if (typeof options === 'string') {
    options = {
      message: options
    }
  }
  return Element.Message({
    ...options,
    showClose: true,
    duration: 4000
  })
}

['success', 'warning', 'info', 'error'].forEach(type => {
  $message[type] = options => {
    console.log('options', options, $message)
    if (typeof options === 'string') {
      options = {
        message: options
      }
    }
    options.type = type
    return $message(options)
  }
})

Vue.prototype.$message = $message
Vue.prototype.$message.closeAll = Element.Message.closeAll

// 注册一个全局防抖指令 `v-throttle`,指令值为防抖时间，默认 2000ms
Vue.directive('throttle', {
  // 当被绑定的元素插入到 DOM 中时……
  inserted: (el, binding, vnode, oldVnode) => {
    const throttle_seconds = Number(binding.value) || 2000 // 防抖延迟时间,毫秒
    const isDisabled = el.classList.contains('is-disabled') // 原始是否disabled
    el.addEventListener('click', () => {
      el.style.pointerEvents = 'none'
      if (!isDisabled) el.classList.add('is-disabled')
      setTimeout(() => {
        el.style.pointerEvents = 'initial'
        if (!isDisabled) el.classList.remove('is-disabled')
      }, throttle_seconds)
    })
  }
})
// 全局按钮权限控制指令 'v-authed'
// 页面通过 mixins 入 @/mixins/auth-buttons 提供了 data 中的 AuthButtons 观察对象(格式: {buttons:[]} )，
// 并将其提供为 provide 对象 AuthButtons，注入给可能的嵌套后代组件使用。
// 后代组件通过 mixins 入 @/mixins/auth-buttons/child-inject，将 AuthButtons 增加到了组件实例的 inject 中。
// 用法： <el v-authed="AuthButtons"></el>
Vue.directive('authed', {
  inserted: (el, binding, vnode, oldVnode) => {
    if (binding.value && binding.value.buttons) {
      vnode.componentInstance.$el.style.display = 'none' // 先隐藏避免闪现
    }
  },
  componentUpdated: (el, binding, vnode, oldVnode) => {
    if (vnode.componentInstance.$el.style.display !== '') {
      if (binding.value && binding.value.buttons) {
        if (
          binding.value.buttons.find(
            b => b.Code === vnode.componentInstance.$attrs.code
          )
        ) {
          vnode.componentInstance.$el.style.display = ''
        }
      } else {
        vnode.componentInstance.$el.style.display = ''
      }
    }
  }
})
// register global utility filters
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

let instance = null
function render(props = {}) {
  const { container } = props
  // router = new VueRouter({
  //   base: window.__POWERED_BY_QIANKUN__ ? '/app-vue/' : '/',
  //   mode: 'history',
  //   routes,
  // });
  // router.base = window.__POWERED_BY_QIANKUN__ ? '/' + name + '/' : '/'
  // router.mode = 'hash'

  instance = new Vue({
    router,
    store,
    render: (h) => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app')
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  render()
}

function addCachedViews(children) {
  children.forEach((child) => {
    store.dispatch('tagsView/addCachedView', child)
    // Recursively check if the current child has nested children
    if (child.children && child.children.length > 0) {
      addCachedViews(child.children) // Recursively call addCachedViews for nested children
    }
  })
}

let globalStateChangeListener = null
function storeOption(props) {
  globalStateChangeListener = props.onGlobalStateChange &&
    props.onGlobalStateChange(async(value, prev) => {
      // console.log(`[onGlobalStateChange - ${props.name}]:`, value, prev) value.CurPlatform !== '' && value.CurPlatform === prev.CurPlatform && value.ModuleId !== prev.ModuleId
      // console.log('produceValue', value)
      // console.log('producePrev', prev)
      if (value.CurPlatform !== '' && value.CurPlatform !== prev.CurPlatform) {
        const accessRoutes = await store.dispatch('permission/generateRoutes') || []
        accessRoutes.forEach((element, idx) => {
          const existingRoutes = router.getRoutes()
          // 检查主路由
          const isMainExist = existingRoutes.some(r =>
            r.name === element.name
          )
          // 检查子路由
          const isChildrenExist = element.children?.some(child =>
            existingRoutes.some(r => r.name === child.name)
          )
          if (!isMainExist && !isChildrenExist) {
            router.addRoute(element)
            console.log('路由添加成功')
          } else {
            console.log('路由已存在，跳过添加')
          }
          // router.addRoute(element)
          element.children && addCachedViews(element.children)
        })

        router.addRoute({ path: '*', redirect: '/404', hidden: true })
      }
    },
    true
    )
}

export async function bootstrap() {
  console.log('[vue] vue app bootstraped')
}
export async function mount(props) {
  console.log('[vue] props from main framework', props)

  const accessRoutes = await store.dispatch('permission/generateRoutes') || []
  accessRoutes.forEach((element, idx) => {
    router.addRoute(element)
    element.children && addCachedViews(element.children)
  })

  router.addRoute({ path: '*', redirect: '/404', hidden: true })

  // 处理从另外的子应用打开的临时路由，默认加载需要打开的临时路由
  props.switchMicroAppTempRoute(transferRoutesWithParent())
  getRoutesWithParent()

  // 把子应用的基础默认路由传给主应用
  props.switchMicroAppBaseRoute(constantRoutes, props.name)

  // 动态路由赋值给主应用
  // if (localStorage.getItem('RouterPageArray')) {
  //   const routerPageArray = JSON.parse(localStorage.getItem('RouterPageArray'))
  //   routerPageArray.forEach(element => {
  //     if (element.platform === props.name) {
  //       element.component = componentMap[element.name] // 从映射中获取组件
  //       handleAddRouterPage([element], element.parentName, element.parentPath)
  //       props.switchMicroAppTempRoute([element], props.name)
  //     }
  //   })
  // }

  Vue.prototype.$qiankun = props
  storeOption(props)
  render(props)
}
export async function unmount() {
  resetRouter()
  instance.$destroy()
  instance.$el.innerHTML = ''
  instance = null
  if (typeof globalStateChangeListener === 'function') {
    globalStateChangeListener() // 调用返回的函数取消监听
  }
  // router = null
}

// 重写toFixed，解决由于银行家舍法导致的问题，同时，解决js计算精度丢失的问题
// eslint-disable-next-line no-extend-native
Number.prototype.toFixed = function(digits) {
  // 参数验证
  if (digits < 0 || digits > 100) {
    throw new RangeError('toFixed() digits argument must be between 0 and 100')
  }

  // 处理特殊值
  if (isNaN(this) || !isFinite(this)) {
    return this.toString()
  }

  const num = Number(this)
  const isNegative = num < 0
  let absNum = Math.abs(num)

  // 修正浮点数精度误差
  // 检测是否存在由于浮点运算导致的微小误差（如 937.334999999999 应该是 937.335）
  const numStr = absNum.toString()
  if (numStr.indexOf('.') !== -1) {
    const decimalPart = numStr.split('.')[1]
    // 检查是否有连续的9或0，这通常表示精度误差
    if (decimalPart.length > 10) {
      // 查找连续的9（向上舍入误差）
      const ninesMatch = decimalPart.match(/(9{6,})$/)
      if (ninesMatch) {
        // 找到连续的9，进行修正
        const precision = decimalPart.length - ninesMatch[1].length
        const corrected = Math.round(absNum * Math.pow(10, precision)) / Math.pow(10, precision)
        absNum = corrected
      }
      // 查找连续的0（向下舍入误差）
      const zerosMatch = decimalPart.match(/(0{6,})$/)
      if (zerosMatch) {
        // 找到连续的0，进行修正
        const precision = decimalPart.length - zerosMatch[1].length
        const corrected = Math.round(absNum * Math.pow(10, precision)) / Math.pow(10, precision)
        absNum = corrected
      }
    }
  }

  // 如果小数位数为0，直接四舍五入到整数
  if (digits === 0) {
    const rounded = Math.floor(absNum + 0.5)
    return (isNegative ? -rounded : rounded).toString()
  }

  // 转换为字符串进行精确计算
  let correctedNumStr = absNum.toString()

  // 处理科学计数法
  if (correctedNumStr.indexOf('e') !== -1) {
    const parts = correctedNumStr.split('e')
    const base = parseFloat(parts[0])
    const exp = parseInt(parts[1])
    correctedNumStr = base.toFixed(Math.max(0, digits - exp))
  }

  // 分离整数部分和小数部分
  const dotIndex = correctedNumStr.indexOf('.')
  let integerPart = dotIndex === -1 ? correctedNumStr : correctedNumStr.substring(0, dotIndex)
  let decimalPart = dotIndex === -1 ? '' : correctedNumStr.substring(dotIndex + 1)

  // 如果小数部分长度小于等于指定位数，直接补零返回
  if (decimalPart.length <= digits) {
    while (decimalPart.length < digits) {
      decimalPart += '0'
    }
    const result = digits === 0 ? integerPart : integerPart + '.' + decimalPart
    return isNegative ? '-' + result : result
  }

  // 需要进行四舍五入
  const targetDigit = parseInt(decimalPart.charAt(digits))
  const roundUp = targetDigit >= 5

  // 截取到指定位数
  decimalPart = decimalPart.substring(0, digits)

  if (roundUp) {
    // 进位处理
    let carry = 1
    let newDecimalPart = ''

    // 从右到左处理小数部分的进位
    for (let i = decimalPart.length - 1; i >= 0; i--) {
      const digit = parseInt(decimalPart.charAt(i)) + carry
      if (digit >= 10) {
        newDecimalPart = '0' + newDecimalPart
        carry = 1
      } else {
        newDecimalPart = digit.toString() + newDecimalPart
        carry = 0
        // 将剩余的数字直接拼接
        newDecimalPart = decimalPart.substring(0, i) + newDecimalPart
        break
      }
    }

    decimalPart = newDecimalPart

    // 如果小数部分还有进位，需要处理整数部分
    if (carry === 1) {
      if (digits === 0) {
        // 如果保留0位小数，直接给整数部分加1
        const intValue = parseInt(integerPart) + 1
        const result = intValue.toString()
        return isNegative ? '-' + result : result
      } else {
        // 整数部分加1，小数部分重置
        const intValue = parseInt(integerPart) + 1
        integerPart = intValue.toString()
        decimalPart = '0'.repeat(digits)
      }
    }
  }

  // 组装结果
  const result = digits === 0 ? integerPart : integerPart + '.' + decimalPart
  return isNegative ? '-' + result : result
}

// new Vue({
//   el: '#app',
//   router,
//   store,
//   render: h => h(App)
// })

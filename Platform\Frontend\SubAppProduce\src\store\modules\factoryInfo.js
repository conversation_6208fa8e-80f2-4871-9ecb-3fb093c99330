
import { GetCurFactory } from '@/api/PRO/factory'

const state = {
  // 车间
  Is_Part_Prepare: false,
  workShopEnabled: false,
  Component_Shipping_Approval: false,
  Is_Skip_Warehousing_Operation: false,
  Shipping_Approval_LowerLimit: undefined,
  checkDuplicate: false,
  Shipping_Order_Number_Auto_Generate: true,
  Shipping_Weigh_Enabled: true,
  Nested_Must_Before_Processing: false
}

const mutations = {
  SET_WORKSHOP_ENABLED(state, isOpen) {
    state.workShopEnabled = isOpen
  },
  SET_Audit(state, data) {
    state.Component_Shipping_Approval = data.Component_Shipping_Approval
    state.Shipping_Approval_LowerLimit = data.Shipping_Approval_LowerLimit
    state.checkDuplicate = data.Is_Mat_Duplicate
    state.Is_Skip_Warehousing_Operation = data.Is_Skip_Warehousing_Operation
    state.Is_Part_Prepare = data.Is_Part_Prepare
    state.Shipping_Weigh_Enabled = data.Shipping_Weigh_Enabled
    state.Shipping_Order_Number_Auto_Generate = data.Shipping_Order_Number_Auto_Generate
    state.Nested_Must_Before_Processing = data.Nested_Must_Before_Processing
  }
}

const actions = {
  getWorkshop({ commit }) {
    return new Promise((resolve, reject) => {
      GetCurFactory({}).then(res => {
        if (res.IsSucceed) {
          if (res?.Data.length) {
            const enabled = res.Data[0]?.Is_Workshop_Enabled
            const {
              Component_Shipping_Approval,
              Is_Mat_Duplicate,
              Is_Skip_Warehousing_Operation,
              Is_Part_Prepare,
              Shipping_Weigh_Enabled,
              Shipping_Order_Number_Auto_Generate,
              Nested_Must_Before_Processing,
              Shipping_Approval_LowerLimit
            } = res.Data[0]
            commit('SET_WORKSHOP_ENABLED', enabled)
            commit('SET_Audit', {
              Is_Part_Prepare,
              Component_Shipping_Approval,
              Is_Mat_Duplicate,
              Shipping_Order_Number_Auto_Generate,
              Is_Skip_Warehousing_Operation,
              Shipping_Approval_LowerLimit,
              Nested_Must_Before_Processing,
              Shipping_Weigh_Enabled
            })
            resolve()
          }
        } else {
          this.$message({
            message: '检查工厂信息失败',
            type: 'error'
          })
        }
      }).catch(err => {
        reject(err)
      })
    })
  }
}

const getters = {
  workshopEnabled: state => state.workShopEnabled,
  Component_Shipping_Approval: state => state.Component_Shipping_Approval,
  Shipping_Weigh_Enabled: state => state.Shipping_Weigh_Enabled,
  Shipping_Approval_LowerLimit: state => state.Shipping_Approval_LowerLimit,
  Is_Skip_Warehousing_Operation: state => state.Is_Skip_Warehousing_Operation,
  checkDuplicate: state => state.checkDuplicate,
  autoGenerate: state => state.Shipping_Order_Number_Auto_Generate,
  getIsPartPrepare: state => state.Is_Part_Prepare,
  Is_Part_Prepare: state => state.Is_Part_Prepare,
  Nested_Must_Before_Processing: state => state.Nested_Must_Before_Processing
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}

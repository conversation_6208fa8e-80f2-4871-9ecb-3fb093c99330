export const baseUrl = () => {
  const { promiseProduceBaseUrl } = document.querySelector('html').dataset
  if (promiseProduceBaseUrl === undefined) {
    // return window.URLGLOBAL.URL
    return process.env.VUE_APP_BASE_API
  }
  if (promiseProduceBaseUrl !== null && promiseProduceBaseUrl.indexOf('http') === 0) {
    return `${promiseProduceBaseUrl}`
  }
  // return window.URLGLOBAL.URL
  return process.env.VUE_APP_BASE_API
}

export const platformUrl = () => {
  const { promiseProducePlatformUrl } = document.querySelector('html').dataset
  if (promiseProducePlatformUrl === undefined) {
    // return window.URLGLOBAL.URL
    return process.env.VUE_APP_PLATFORM_API
  }
  if (promiseProducePlatformUrl !== null && promiseProducePlatformUrl.indexOf('http') === 0) {
    return `${promiseProducePlatformUrl}`
  }
  // return window.URLGLOBAL.URL
  return process.env.VUE_APP_PLATFORM_API
}

export const promiseProjectWebUrl = () => {
  const { promiseProjectWebUrl } = document.querySelector('html').dataset
  if (promiseProjectWebUrl === undefined) {
    return process.env.VUE_APP_WEB_PROJ_API
  }
  if (promiseProjectWebUrl !== null && promiseProjectWebUrl.indexOf('http') === 0) {
    return `${promiseProjectWebUrl}`
  }
  return process.env.VUE_APP_WEB_PROJ_API
}

export const promiseProjectApi = () => {
  // return 'http://localhost:35000/'
  const { promiseProjectBaseUrl } = document.querySelector('html').dataset
  if (promiseProjectBaseUrl === undefined) {
    return process.env.VUE_APP_WEB_PROJ_API
  }
  if (promiseProjectBaseUrl !== null && promiseProjectBaseUrl.indexOf('http') === 0) {
    return `${promiseProjectBaseUrl}`
  }
  return process.env.VUE_APP_WEB_PROJ_API
}

export const webUrl = () => {
  const { promiseProduceDomainWebUrl } = document.querySelector('html').dataset
  if (promiseProduceDomainWebUrl === undefined) {
    // return window.URLGLOBAL.URL
    return process.env.VUE_APP_WEB_URL
  }
  if (promiseProduceDomainWebUrl !== null && promiseProduceDomainWebUrl.indexOf('http') === 0) {
    return `${promiseProduceDomainWebUrl}`
  }
  // return window.URLGLOBAL.URL
  return process.env.VUE_APP_WEB_URL
}

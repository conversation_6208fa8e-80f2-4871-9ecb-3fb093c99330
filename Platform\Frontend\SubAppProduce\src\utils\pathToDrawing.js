export function pathToDrawing(obj, returnUrl = false) {
  const drawingConfig = JSON.stringify({
    ...obj,
    File_Url: obj.File_Url, // 图纸地址
    File_Name: obj.File_Name, // 图纸名称
    canSave: obj.canSave, // 是否可以编辑
    sheetId: obj.sheetId, // 质检单id
    canvasId: obj.canvasId || '', // 图纸id
    'Last-Working-Object-Id': localStorage.getItem('Last_Working_Object_Id'),
    Authorization: localStorage.getItem('Token')
  })

  let currentUrl = ''
  if (window.location.origin.includes('localhost')) {
    currentUrl = `https://localhost:8000`
  } else if (window.location.origin.includes('-dev')) {
    currentUrl = `https://drawing-h5-dev.bimtk.tech`
  } else if (window.location.origin.includes('-test')) {
    currentUrl = `https://drawing-h5-test.bimtk.com`
  } else if (window.location.origin.includes('-release')) {
    currentUrl = `https://drawing-h5-release.bimtk.com`
  } else {
    currentUrl = 'https://drawing-h5.bimtk.com'
  }
  if (returnUrl || obj.exportMode) {
    return `${currentUrl}/drawing?drawingConfig=${encodeURIComponent(drawingConfig)}`
  } else {
    window.open(`${currentUrl}/drawing?drawingConfig=${encodeURIComponent(drawingConfig)}`,'_blank')
  }
}

import router from '@/router'
function checkRouteNameExists(routes, targetName) {
  for (const route of routes) {
    // 检查当前路由的 name 是否匹配
    if (route.name === targetName) {
      return true
    }
    // 如果有嵌套路由，递归检查 children
    if (route.children && route.children.length) {
      const exists = checkRouteNameExists(route.children, targetName)
      if (exists) return true
    }
  }
  return false
}

// 调用检查方法（对外暴露）
export const isRouteNameExists = (targetName) => {
  const allRoutes = router.getRoutes() // 获取所有路由配置
  return checkRouteNameExists(allRoutes, targetName)
}


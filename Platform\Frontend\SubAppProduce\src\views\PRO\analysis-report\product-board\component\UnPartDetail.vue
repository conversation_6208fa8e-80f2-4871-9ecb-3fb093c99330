<template>
  <div class="un-part-detail">
    <!-- 搜索表单 -->
    <el-form :inline="true" :model="searchForm" class="search-form">
      <el-form-item label="部件/零件名称:">
        <el-input
          v-model="names"
          clearable
          style="width: 100%"
          class="input-with-select"
          placeholder="请输入内容"
          size="small"
        >
          <el-select
            slot="prepend"
            v-model="nameMode"
            placeholder="请选择"
            style="width: 100px"
          >
            <el-option label="模糊搜索" :value="1" />
            <el-option label="精确搜索" :value="2" />
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <div class="tb-wrapper">
      <vxe-table
        :data="tableData"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        class="cs-vxe-table"
        :row-config="{isCurrent: true, isHover: true}"
        align="center"
        show-overflow
        stripe
        height="100%"
        :loading="loading"
        resizable
        empty-text="暂无数据"
        show-footer
        size="medium"
        :tooltip-config="{ enterable: true }"
      >
        <vxe-column field="Code" title="部件/零件名称">
          <template #default="{ row }">
            <el-tag v-if="row.Part_Grade===1">部</el-tag>
            <el-tag v-else type="success">零</el-tag>
            {{ row.Code }}
          </template>
        </vxe-column>
        <vxe-column field="Need_Count" title="需求量" />
        <vxe-column field="Stock_Count" title="库存数" />
        <vxe-column field="Lack_Count" title="缺件量" />
      </vxe-table>
    </div>
    <Pagination
      class="cs-table-pagination"
      :total="total"
      :page-sizes="tablePageSize"
      :page.sync="currentPage"
      :limit.sync="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="handlePageChange"
    />
  </div>
</template>

<script>
import { GetUnPreparedList } from '@/api/PRO/component'
import Pagination from '@/components/Pagination'
import { tablePageSize } from '@/views/PRO/setting'

export default {
  name: 'UnPartDetail',
  components: { Pagination },
  data() {
    return {
      names: '',
      nameMode: 1,
      searchForm: {
        Working_Team_Id: '',
        Comp_Id: '',
        Working_Process_Id: ''
      },
      tableData: [],
      originData: [],
      loading: false,
      currentPage: 1,
      pageSize: tablePageSize[0],
      total: 0,
      tablePageSize: tablePageSize
    }
  },
  methods: {
    handleOpen(data, workingTeamId) {
      this.originData = []
      this.searchForm.Working_Process_Id = data.Working_Process_Id
      this.searchForm.Working_Team_Id = workingTeamId || ''
      this.searchForm.Comp_Id = data.Comp_Id
      this.fetchData()
    },
    async fetchData() {
      this.loading = true
      const params = {
        Working_Team_Id: this.searchForm.Working_Team_Id,
        Comp_Id: this.searchForm.Comp_Id,
        Working_Process_Id: this.searchForm.Working_Process_Id
      }
      GetUnPreparedList(params).then(res => {
        if (res.IsSucceed) {
          this.originData = res.Data || []
          this.currentPage = 1
          this.filterTableData()
        } else {
          this.originData = []
          this.tableData = []
          this.total = 0
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
        .finally(() => {
          this.loading = false
        })
    },
    filterTableData() {
      let filtered = []
      if (!this.names) {
        filtered = this.originData
      } else if (this.nameMode === 1) {
        filtered = this.originData.filter(item => item.Code && item.Code.includes(this.names))
      } else {
        filtered = this.originData.filter(item => item.Code && item.Code === this.names)
      }
      // 计算缺件量
      filtered = filtered.map(item => ({
        ...item,
        Lack_Count: (item.Need_Count || 0) - (item.Stock_Count || 0)
      }))
      // 过滤掉缺件量小于等于0的数据
      filtered = filtered.filter(item => item.Lack_Count > 0)
      this.total = filtered.length
      // 前端分页
      const start = (this.currentPage - 1) * this.pageSize
      this.tableData = filtered.slice(start, start + this.pageSize)
    },
    handleSearch() {
      this.currentPage = 1
      this.filterTableData()
    },
    handleReset() {
      this.names = ''
      this.nameMode = 1
      this.currentPage = 1
      this.filterTableData()
    },
    handlePageChange({ page, limit }) {
      this.currentPage = page
      this.pageSize = limit
      this.filterTableData()
    }
  }
}
</script>

<style scoped lang="scss">
.un-part-detail {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .tb-wrapper {
    flex: 1;
    height: 0;
  }
  .search-form {
    margin-bottom: 16px;
  }
  .pagination-container {
          text-align: right;
          margin-top: 10px;
          padding: 0;
        }

}
</style>

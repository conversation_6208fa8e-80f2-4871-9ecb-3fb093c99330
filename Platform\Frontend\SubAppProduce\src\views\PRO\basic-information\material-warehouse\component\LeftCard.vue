<template>
  <div class="left-card">
    <div class="left-card-title">
      <div class="card-title">{{ typeName }}</div>
      <div v-if="!btnDisabled" class="cs-btn-x">
        <el-button type="primary" @click="add">新增</el-button>
      </div>
    </div>
    <div class="tree-x">
      <el-tree
        ref="tree"
        v-loading="treeLoading"
        element-loading-text="加载中..."
        :props="{
          label:'Label',
          children:'Children'
        }"
        :data="treeData"
        highlight-current
        default-expand-all
        :current-node-key="currentKey"
        empty-text="暂无数据"
        node-key="Id"
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <svg-icon
            :icon-class="
              node.expanded ? 'icon-folder-open' : 'icon-folder'
            "
            class-name="class-icon"
          />
          <span :class="['cs-label',{ 'is-active':currentKey===data.Id }]" :title="node.label">{{ node.label }}</span>
          <span v-if="currentKey===data.Id&&!btnDisabled" class="tree-btn-x">
            <i v-if="node.level===1 && node.label!=='全部'" title="新增" class="cs-btn el-icon-plus" @click="handleAdd(data)" />
            <i title="编辑" style="color:#298DFF" class="cs-btn el-icon-edit" @click="handleEdit(data,node)" />
            <!-- <i v-if="data.Label!=='板材' && data.Label!=='型材' && data.Label!=='钢卷' && data.Label!=='结构辅料' && data.Label!=='维护辅料'" title="删除" style="color: red" class="cs-btn el-icon-delete" @click="handleDelete(data)" /> -->
            <i v-if="!Boolean(['板材', '型材', '钢卷', '花纹板', '结构辅料', '维护辅料'].includes(data.Label))" title="删除" style="color: red" class="cs-btn el-icon-delete" @click="handleDelete(data)" />
          </span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>

import {
  GetCategoryTreeList,
  DelCategoryEntity,
  GetAuxCategoryTreeList,
  DelAuxCategoryEntity
} from '@/api/PRO/material-warehouse'

export default {
  props: {
    typeCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      filterText: '',
      treeLoading: false,
      treeData: [],
      currentKey: ''
    }
  },

  computed: {
    typeName() {
      return this.isRawMaterial ? '原料分类' : '辅料分类'
    },
    // 是否原料
    isRawMaterial() {
      return this.typeCode === '0'
    },
    btnDisabled() {
      return parseInt(this.$route.query.status) === 1
    }
  },
  watch: {
    typeCode() {
      this.getTreeList()
    }
  },
  async created() {
    await this.getTreeList()
    // this.getFirstTreeData()
  },
  methods: {
    async getTreeList() {
      this.treeLoading = true
      let _fun
      if (this.isRawMaterial) {
        _fun = GetCategoryTreeList
      } else {
        _fun = GetAuxCategoryTreeList
      }
      await _fun({}).then(res => {
        if (res.IsSucceed) {
          this.treeData = [{
            Label: '全部',
            Id: 'all',
            Data: {}
          }].concat(res.Data)
          if (this.treeData.length) {
            this.currentKey = this.treeData[0].Id
            this.$nextTick(_ => {
              this.$refs['tree'].setCurrentKey(this.currentKey)
              const node = this.$refs['tree'].getNode(this.currentKey)
              this.$emit('nodeClick', { data: this.treeData[0].Data.Category || this.treeData[0].Data, node })
              this.$emit('getFirstTreeInitData', this.getFirstTreeData())
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.treeLoading = false
      })
    },
    add() {
      this.$emit('add', {})
    },
    handleEdit(data, node) {
      console.log(data, 'data======')
      console.log(node, 'node======')
      this.$emit('add', { data, isEdit: true, node })
    },
    handleAdd(data) {
      this.$emit('add', { data, isFromTree: true })
    },
    handleDelete(data) {
      this.$confirm('此操作将删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let _fun
        if (this.isRawMaterial) {
          _fun = DelCategoryEntity
        } else {
          _fun = DelAuxCategoryEntity
        }
        _fun({
          Id: data.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.$emit('refresh')
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleNodeButtonDelete() {

    },
    handleOpenAddEdit() {

    },
    handleNodeClick(data, node) {
      // console.log('data, node', data, node)
      const Category = data.Data.Category || data.Data
      this.currentKey = data.Id
      console.log(Category, 'Category=====22222222')
      this.$emit('nodeClick', { data: Category, node })
    },
    getFirstTreeData() {
      return this.treeData.map(item => {
        const { Children, ...element } = item
        return element
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/mixin.scss';

.left-card {
  display: flex;
  flex-direction: column;
  height: 100%;

  .left-card-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 16px;
    border-bottom: 1px solid #EEEEEE;
    .card-title {
      color: #222834;
      font-weight: 600;
    }
    .cs-btn-x{
      text-align: center;
    }
  }

  .cs-input-x{
    padding: 16px;
    border-bottom: 1px solid #EEEEEE;
  }

  .tree-x{
    flex: 1;
    padding: 0 0 16px;
    height: 0;

    .el-tree{
      @include scrollBar;
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .custom-tree-node{
      display: flex;
      align-items: center;
      flex: 1;
      .cs-label{
        font-size: 14px;
        width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .is-active{
        max-width: 140px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
        display: inline-block;
      }
      .tree-btn-x{
        display: inline-block;
        margin-left: 4px;
      }
      .class-icon{
        margin-right: 4px;
      }
      .cs-btn{
        margin-right: 8px;
      }
    }

  }

}

</style>

import { getCurPlatform } from '@/utils/tenant'
// import { GetPlmProjectGetEntity } from '@/api/plm/projectmanagement'
// import { GetEpcEntity } from '@/api/EPC/basic-info'
import { getConfigure } from '@/api/user'
import { baseUrl } from '@/utils/baseurl'
// import { GetProfessionalType } from '@/api/plm/material'

import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { GetProjectPageList } from '@/api/PRO/pro-schedules'

export default {
  data() {
    return {
      iframeUrl: '',
      bimType: '',
      typeCode: '',
      preUrl: '',

      projectList: [],
      projectId: ''

    }
  },
  async mounted() {
    const res = await GetProjectPageList({ PageSize: -1 })
    this.projectList = res.Data.Data
    this.projectId = this.projectList[0].Sys_Project_Id
    // console.log(this.projectList, 'projectList2222')

    this.getModelApp()
    window.addEventListener('message', this.setListener)
  },
  beforeD<PERSON>roy() {
    window.removeEventListener('message', this.setListener)
  },
  methods: {
    // 切换项目
    changeProject() {
      this.getModelApp()
    },
    // 判断用什么模型
    async getModelApp() {
      // let res
      // // 1 epc 2, pro 3 plm
      // if (getCurPlatform() === '3') {
      //   res = await GetPlmProjectGetEntity({
      //     id: localStorage.getItem('CurReferenceId')
      //   })
      // }
      // if (getCurPlatform() === '1') {
      //   res = await GetEpcEntity({
      //     id: localStorage.getItem('CurReferenceId')
      //   })
      // }
      // if (res.Data.Bim_Viz_Id) {
      //   this.bimvizInit()
      // } else {
      //   const res = await getConfigure({ code: 'modelApp' })
      //   if (res.Data === 'glendale') {
      //     this.glendaleInit()
      //   } else {
      //     this.bimvizInit()
      //   }
      // }
      if (getCurPlatform() === '2') {
        this.glendaleInit()
      }
    },
    async glendaleInit() {
      this.bimType = 'glendale'
      let url
      if (process.env.NODE_ENV === 'development') {
        // url = 'http://localhost:9529'
        url = 'http://glendale-model.bimtk.com'
      } else {
        url = (await getConfigure({ code: 'glendale_url' })).Data
      }
      this.preUrl = url
      await this.fetchCategoryList()
      this.getIframeUrl()
    },
    getIframeUrl() {
      const url = `${this.preUrl}?projectId=${this.projectId}&token=${localStorage.getItem('Token')}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}&baseUrl=${baseUrl()}&mode=${this.mode}&factoryMode=1&tenantCode=${localStorage.getItem('tenant')}&unify=1`
      if (this.mode === 'dynamic') {
        this.iframeUrl = url + `&typeId=${this.cateId}`
      } else {
        this.iframeUrl = url
      }
      // console.log(this.iframeUrl, 'iframeUrl')
    },
    bimvizInit() {
      this.bimType = 'bimviz'
      this.iframeUrl = '/static/bimviz/index.html'
    },
    setListener({ data }) {
      if (data.type === 'qsSheet') {
        this.$refs.qsDialog.handleOpen(true, { id: data.data }, 'view')
      }
      if (data.type === 'track') {
        this.$refs.track.handleOpen(data.data, this.curCate.Code)
      }
      if (data.type === 'loaded') {
        try {
          this.renderColor()
        } catch (e) {
          console.log(e)
        }
      }
      if (data.type === 'init') {
        this.fetchCategoryList()
      }
      if (data.type === 'addProblem') {
        this.openAddProblemDialog(data.data)
      }
      if (data.type === 'rectifyProblem') {
        const row = data.data.data
        const type = data.data.type
        row.id = row.Id
        this.$refs.rectification.handleOpen(true, row, type)
      }
    },
    async fetchCategoryList() {
      const res = await GetFactoryProfessionalByCode({
        // is_System: false,
        factoryId: localStorage.getItem('CurReferenceId'),
        sysProjectId: this.projectId
      })
      this.categoryList = res.Data
      if (this.categoryList.length !== 0) {
        this.cateId = this.categoryList[0].Id
        this.typeCode = this.categoryList[0].Code
      } else {
        this.$message({
          message: '请配置项目专业类型',
          type: 'error'
        })
      }
      try {
        this.getCategoryNodes()
      } catch (e) {
        console.log(e)
      }
    },
    openAddProblemDialog(val) {
      const { img, port, models, featureIds } = val
      if (featureIds instanceof Array) {
        this.$refs.addProblem.handleOpen(true, '', '', '', featureIds, '', models, port)
      } else {
        this.$refs.addProblem.handleOpen(true, '', '', '', '', img, models, port)
      }
    }

  }
}

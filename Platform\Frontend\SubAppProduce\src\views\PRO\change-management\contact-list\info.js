import { deepClone } from '@/utils'
import {
  allCodes,
  changeType,
  getCoreFieldsCode,
  getFieldDisplayName
} from '@/views/PRO/change-management/contact-list/utils'

export default {
  data() {
    return {
      processingUpdate: false // 防止递归更新
    }
  },

  methods: {

    // 获取变更类型文本
    getChangeTypeText(changeTypeCode) {
      return changeType[changeTypeCode] || '无变更'
    },

    // 判断是否为核心字段
    isCoreField(fieldCode) {
      return getCoreFieldsCode().includes(fieldCode)
    },

    // 获取字段类型
    getFieldType(fieldCode) {
      const field = allCodes.find(f => f.Code === fieldCode)
      return field ? field.Field_Type : 'string'
    },
    getChangeTypePriority() {
      return {
        'isDelete': 5, // delete
        'isAdjust': 4, // 信息调整
        'isIncrease': 3, // 数量增加
        'isDecrease': 2, // 数量减少
        'isNoChange': 1 // 无变更

      }
    },
    getFinalChangeType(types) {
      const priority = this.getChangeTypePriority()
      return types.reduce((final, current) => {
        return priority[current] > priority[final] ? current : final
      }, 'isNoChange')
    },

    // 确定变更优先级（根据getChangeTypePriority优先级返回）
    determineChangeType(changes) {
      const typeList = []
      for (const change of changes) {
        // 数量字段特殊处理
        if (change.Code === 'SteelAmount') {
          const oldValue = parseFloat(change.Value) || 0
          const newValue = parseFloat(change.NewValue) || 0
          if (newValue > oldValue) {
            typeList.push('isIncrease')
          } else if (newValue < oldValue) {
            typeList.push('isDecrease')
          }
        } else if (this.isCoreField(change.Code)) {
          typeList.push('isAdjust')
        }
      }
      if (typeList.length === 0) {
        typeList.push('isNoChange')
      }
      return this.getFinalChangeType(typeList)
    },

    isSameParent(item1, item2) {
      if (item1.Type !== 3) return false
      const parent1 = this.findParentItem(item1)
      const parent2 = this.findParentItem(item2)
      return parent1.MocAggregateIdBefore === parent2.MocAggregateIdBefore
    },

    // 查找相同项目（基于MocAggregateIdBefore）
    findSimilarItems(targetItem) {
      return this.tbData.filter(item => {
        if (item.uuid === targetItem.uuid) return false

        // 构件：基于MocIdBefore匹配
        if (targetItem.Type === 0) {
          return item.MocIdBefore === targetItem.MocIdBefore && item.Type === 0
        }
        // 部件和零件：基于MocAggregateIdBefore匹配
        else if (targetItem.Type === 1 || targetItem.Type === 2 || targetItem.Type === 3) {
          return item.MocAggregateIdBefore === targetItem.MocAggregateIdBefore &&
                        item.Type === targetItem.Type
        }
        return false
      })
    },

    // 查找子项目
    findChildItems(parentItem, tbData = null) {
      if (!tbData) {
        tbData = this.tbData
      }
      return tbData.filter(item => item.ParentId === parentItem.parentChildrenId)
    },

    // 查找父项目
    findParentItem(childItem, tbData = null) {
      if (!tbData) {
        tbData = this.tbData
      }
      return tbData.find(item => item.parentChildrenId === childItem.ParentId)
    },

    // 判断零件是否直接属于构件
    isDirectChildOfStructure(item) {
      return item.Type === 2
    },

    // 判断零件是否属于部件
    isChildOfComponent(item) {
      return item.Type === 3
    },

    // 获取原始值
    getOriginalValue(uuid, fieldCode) {
      const originalItem = this.defaultTbData.find(item => item.uuid === uuid) || this.tbData.find(item => item.uuid === uuid)
      return originalItem ? (originalItem[fieldCode] || '') : ''
    },

    // 更新变更记录
    updateChangeRecord(uuid, fieldCode, newValue, targetItem) {
      const originalValue = this.getOriginalValue(uuid, fieldCode)
      const changeCode = [...(this.$store.state.contactList.changeCode[uuid] || [])]

      // 查找现有变更记录
      const existingChangeIndex = changeCode.findIndex(change => change.Code === fieldCode)

      if (newValue === originalValue || (newValue === '' && originalValue === '')) {
        // 新值等于原始值，移除变更记录
        if (existingChangeIndex !== -1) {
          changeCode.splice(existingChangeIndex, 1)
        }
      } else {
        // 创建或更新变更记录
        const changeRecord = {
          Field_Type: this.getFieldType(fieldCode),
          IsCoreField: this.isCoreField(fieldCode),
          Code: fieldCode,
          Name: getFieldDisplayName(allCodes, fieldCode, targetItem.Type),
          Value: originalValue,
          NewValue: newValue
        }

        if (existingChangeIndex === -1) {
          changeCode.push(changeRecord)
        } else {
          changeCode[existingChangeIndex] = changeRecord
        }
      }

      // 更新Vuex
      if (changeCode.length > 0) {
        this.$store.dispatch('contactList/addChangeCode', { uuid, list: changeCode })
      } else {
        this.resetTableItemDate(uuid)
      }

      return changeCode
    },

    // 更新项目的变更类型和内容
    updateItemChangeStatus(item, changeCode) {
      const changeTypeResult = changeCode.length > 0 ? this.determineChangeType(changeCode) : this.checkParentQuantityChangeType(item)
      this.$set(item, 'changeType', changeTypeResult)
      this.$set(item, 'changeContent', this.getChangeTypeText(changeTypeResult))
      return changeTypeResult
    },

    // 批量更新表格项目
    batchUpdateTableItem(uuid, fieldUpdates) {
      const targetItem = this.tbData.find(item => item.uuid === uuid)
      if (!targetItem) return
      // this.resetDeletedContent(uuid)
      const allChangeCode = [...(this.$store.state.contactList.changeCode[uuid] || [])]
      console.log('allChangeCode1', allChangeCode)
      // 处理每个字段更新
      Object.entries(fieldUpdates).forEach(([fieldCode, newValue]) => {
        // 更新表格数据
        this.$set(targetItem, fieldCode, newValue)
        // 更新变更记录

        const originalValue = this.getOriginalValue(uuid, fieldCode)
        const existingChangeIndex = allChangeCode.findIndex(change => change.Code === fieldCode)

        if (newValue === originalValue || (newValue === '' && originalValue === '')) {
          // 移除变更记录
          if (existingChangeIndex !== -1) {
            allChangeCode.splice(existingChangeIndex, 1)
          }
        } else {
          // 创建或更新变更记录
          const changeRecord = {
            Field_Type: this.getFieldType(fieldCode),
            IsCoreField: this.isCoreField(fieldCode),
            Code: fieldCode,
            Name: getFieldDisplayName(allCodes, fieldCode, targetItem.Type),
            Value: originalValue,
            NewValue: newValue
          }

          if (existingChangeIndex === -1) {
            allChangeCode.push(changeRecord)
          } else {
            allChangeCode[existingChangeIndex] = changeRecord
          }
        }
      })
      console.log('allChangeCode2', allChangeCode)
      // 更新Vuex
      if (allChangeCode.length > 0) {
        this.$store.dispatch('contactList/addChangeCode', { uuid, list: allChangeCode })
      } else {
        this.resetTableItemDate(uuid)
      }

      // 确定变更类型
      const changeTypeResult = this.updateItemChangeStatus(targetItem, allChangeCode)

      // 处理关联更新
      this.handleBatchRelatedUpdates(targetItem, fieldUpdates, changeTypeResult, allChangeCode)
      console.log('targetItem', JSON.parse(JSON.stringify(targetItem)))
      return targetItem
    },

    // 处理批量关联更新逻辑
    handleBatchRelatedUpdates(targetItem, fieldUpdates, changeType, allChangeCode) {
      if (this.processingUpdate) return
      this.processingUpdate = true
      try {
        const SteelAmountInfo = allChangeCode.find(item => item.Code === 'SteelAmount')
        // 1. 构件数量变化影响所有子项
        if (targetItem.Type === 0) {
          if (SteelAmountInfo) {
            const __type = SteelAmountInfo.Value > SteelAmountInfo.NewValue ? 'isDecrease' : 'isIncrease'
            this.updateChildrenForParentQuantityChange(targetItem, __type)
          } else {
            // 恢复子项
            this.updateChildrenForParentQuantityChange(targetItem, changeType, true)
          }
        }

        // 2. 部件变化
        else if (targetItem.Type === 1) {
          if (SteelAmountInfo) {
            const __type = SteelAmountInfo.Value > SteelAmountInfo.NewValue ? 'isDecrease' : 'isIncrease'
            this.updateChildrenForParentQuantityChange(targetItem, __type)
            this.updateSimilarUnitPartItems(targetItem, allChangeCode, changeType, true)
          } else {
            this.updateChildrenForParentQuantityChange(targetItem, changeType, true)
            this.updateSimilarUnitPartItems(targetItem, allChangeCode, changeType, false)
          }
        }

        // 3. 零件变化
        else if (targetItem.Type === 2 || targetItem.Type === 3) {
          this.handlePartItemBatchUpdate(targetItem, allChangeCode, changeType)
        }
      } finally {
        this.processingUpdate = false
      }
    },

    // 更新子项目（当父级数量变化时）
    updateChildrenForParentQuantityChange(parentItem, childChangeType, isReset = false) {
      const children = this.findChildItems(parentItem)
      children.forEach(child => {
        if (child.changeType !== 'isDelete') {
          if (isReset) {
            const childChangeCode = [...(this.$store.state.contactList.changeCode[child.uuid] || [])]
            this.updateItemChangeStatus(child, childChangeCode)
          } else {
            const _childChangeType = this.getItemChangeType(child, childChangeType)
            this.$set(child, 'changeType', _childChangeType)
            this.$set(child, 'changeContent', this.getChangeTypeText(child.changeType))
          }
        }
        if (childChangeType === 'isDelete') {
          this.$set(child, 'isDisabled', true)
        }
        // 递归更新子项的子项
        this.updateChildrenForParentQuantityChange(child, childChangeType, isReset)
      })
    },

    getCodeChangeCodes(allChangeCode, fieldUpdates, row) {
      const resultCodes = {}
      const fieldKeys = Object.keys(fieldUpdates)

      const removedChangeCodes = allChangeCode.filter(item => !fieldKeys.some(code => item.Code === code))

      let flag = true

      // 清空
      if (fieldKeys.length === 0) {
        if (allChangeCode.some(change => change.Code === 'SteelAmount')) {
          resultCodes['SteelAmount'] = allChangeCode.find(change => change.Code === 'SteelAmount')?.NewValue
        }
        flag = false
        return resultCodes
      }

      if (removedChangeCodes.length) {
        const defaultRow = this.defaultTbData.find(item => item.uuid === row.uuid)
        removedChangeCodes.forEach(item => {
          console.log(`similarItem 重置字段 ${item.Code} 为原始值:`, defaultRow[item.Code])
          resultCodes[item.Code] = defaultRow[item.Code]
        })
      }

      if (!flag) {
        return resultCodes
      }

      fieldKeys.forEach(field => {
        if (
          allChangeCode.every(change => change.Code !== field) ||
          allChangeCode.some(change => change.Code === field)
        ) {
          resultCodes[field] = fieldUpdates[field]
        }
      })

      const hasSteelAmountInAllChangeCode = allChangeCode.some(change => change.Code === 'SteelAmount')
      const hasSteelAmountInFieldUpdates = fieldKeys.includes('SteelAmount')
      if ((hasSteelAmountInAllChangeCode || hasSteelAmountInFieldUpdates) && !('SteelAmount' in resultCodes)) {
        resultCodes['SteelAmount'] = fieldUpdates['SteelAmount'] || allChangeCode.find(change => change.Code === 'SteelAmount')?.NewValue
      }

      return resultCodes
    },

    // 批量更新相同项目
    updateSimilarUnitPartItems(targetItem, updateList, changeType, updateChildren = true) {
      const similarItems = this.findSimilarItems(targetItem)
      similarItems.forEach(similarItem => {
        const fieldsToUpdate = {}
        updateList.forEach(item => {
          fieldsToUpdate[item.Code] = item.NewValue
        })

        delete fieldsToUpdate.SteelAmount

        const _allChangeCode = [...(this.$store.state.contactList.changeCode[similarItem.uuid] || [])]

        const SteelAmountInfo = _allChangeCode.find(item => item.Code === 'SteelAmount')
        const fieldKeys = Object.keys(fieldsToUpdate)
        const removedChangeCodes = _allChangeCode.filter(item => item.Code !== 'SteelAmount' && !fieldKeys.some(code => item.Code === code))
        if (removedChangeCodes.length) {
          const defaultRow = this.defaultTbData.find(item => item.uuid === similarItem.uuid)
          removedChangeCodes.forEach(item => {
            console.log(`similarItem 重置字段 ${item.Code} 为原始值:`, defaultRow[item.Code])
            fieldsToUpdate[item.Code] = defaultRow[item.Code]
          })
        }

        // 批量更新字段值
        Object.entries(fieldsToUpdate).forEach(([fieldCode, newValue]) => {
          this.$set(similarItem, fieldCode, newValue)
        })

        // 批量更新变更记录
        const allChangeCode = []
        Object.entries(fieldsToUpdate).forEach(([fieldCode, newValue]) => {
          const originalValue = this.getOriginalValue(similarItem.uuid, fieldCode)
          const existingChangeIndex = allChangeCode.findIndex(change => change.Code === fieldCode)

          if (newValue === originalValue || (newValue === '' && originalValue === '')) {
            if (existingChangeIndex !== -1) {
              allChangeCode.splice(existingChangeIndex, 1)
            }
          } else {
            const changeRecord = {
              Field_Type: this.getFieldType(fieldCode),
              IsCoreField: this.isCoreField(fieldCode),
              Code: fieldCode,
              Name: getFieldDisplayName(allCodes, fieldCode, similarItem.Type),
              Value: originalValue,
              NewValue: newValue
            }

            if (existingChangeIndex === -1) {
              allChangeCode.push(changeRecord)
            } else {
              allChangeCode[existingChangeIndex] = changeRecord
            }
          }
        })
        if (SteelAmountInfo) {
          allChangeCode.push(SteelAmountInfo)
        }
        // 更新Vuex
        if (allChangeCode.length > 0) { // 部件属性清空变更不同步
          this.$store.dispatch('contactList/addChangeCode', { uuid: similarItem.uuid, list: allChangeCode })
        } else {
          this.resetTableItemDate(similarItem.uuid)
        }

        // 更新变更类型
        if (similarItem.changeType !== 'isDelete') {
          this.updateItemChangeStatus(similarItem, allChangeCode)
        }
      })
    },

    // 处理零件批量更新
    handlePartItemBatchUpdate(targetItem, updateList, changeType) {
      const similarItems = this.findSimilarItems(targetItem)
      // const SteelAmountInfo = updateList.find(item => item.Code === 'SteelAmount')
      if (similarItems.length) {
        similarItems.forEach(similarItem => {
          const fieldsToUpdate = {}
          updateList.forEach(item => {
            fieldsToUpdate[item.Code] = item.NewValue
          })

          // // 如果是数量变化，则不同步数量
          // if (SteelAmountInfo) {
          //   if (!this.isSameParent(similarItem, targetItem)) {
          //     delete fieldsToUpdate.SteelAmount
          //   }
          // }
          const _isSameParent = this.isSameParent(similarItem, targetItem)
          if (!_isSameParent) {
            delete fieldsToUpdate.SteelAmount
          }

          const _allChangeCode = [...(this.$store.state.contactList.changeCode[similarItem.uuid] || [])]

          const SteelAmountInfo = _allChangeCode.find(item => item.Code === 'SteelAmount')
          const fieldKeys = Object.keys(fieldsToUpdate)
          const removedChangeCodes = _allChangeCode.filter(item => {
            if (_isSameParent) {
              return !fieldKeys.some(code => item.Code === code)
            } else {
              return item.Code !== 'SteelAmount' && !fieldKeys.some(code => item.Code === code)
            }
          })

          if (removedChangeCodes.length) {
            const defaultRow = this.defaultTbData.find(item => item.uuid === similarItem.uuid)
            removedChangeCodes.forEach(item => {
              console.log(`similarItem 重置字段 ${item.Code} 为原始值:`, defaultRow[item.Code])
              fieldsToUpdate[item.Code] = defaultRow[item.Code]
            })
          }

          // const allChangeCode = [...(this.$store.state.contactList.changeCode[similarItem.uuid] || [])]

          // fieldsToUpdate = this.getCodeChangeCodes(allChangeCode, fieldsToUpdate, similarItem)

          // if (!SteelAmountInfo && this.isSameParent(similarItem, targetItem)) {
          //   delete fieldsToUpdate.SteelAmount
          // }

          // 批量更新字段
          Object.entries(fieldsToUpdate).forEach(([fieldCode, newValue]) => {
            this.$set(similarItem, fieldCode, newValue)
          })

          // 批量更新变更记录
          // this.batchUpdateChangeRecord(similarItem, fieldsToUpdate)
          const allChangeCode = []
          Object.entries(fieldsToUpdate).forEach(([fieldCode, newValue]) => {
            const originalValue = this.getOriginalValue(similarItem.uuid, fieldCode)
            const existingChangeIndex = allChangeCode.findIndex(change => change.Code === fieldCode)
            if (newValue === originalValue || (newValue === '' && originalValue === '')) {
              if (existingChangeIndex !== -1) {
                allChangeCode.splice(existingChangeIndex, 1)
              }
            } else {
              const changeRecord = {
                Field_Type: this.getFieldType(fieldCode),
                IsCoreField: this.isCoreField(fieldCode),
                Code: fieldCode,
                Name: getFieldDisplayName(allCodes, fieldCode, similarItem.Type),
                Value: originalValue,
                NewValue: newValue
              }

              if (existingChangeIndex === -1) {
                allChangeCode.push(changeRecord)
              } else {
                allChangeCode[existingChangeIndex] = changeRecord
              }
            }
          })
          if (SteelAmountInfo && !fieldsToUpdate.SteelAmount) {
            allChangeCode.push(SteelAmountInfo)
          }

          // 更新Vuex
          let _changecode = {}
          if (allChangeCode.length > 0) {
            _changecode = allChangeCode
            this.$store.dispatch('contactList/addChangeCode', { uuid: similarItem.uuid, list: allChangeCode })
          } else {
            this.resetTableItemDate(similarItem.uuid)
          }
          if (similarItem.changeType === 'isDelete') {
            return
          }
          // 更新变更类型
          this.updateItemChangeStatus(similarItem, _changecode)
        })
      } else {
        console.log('info: 没有找到相似的零件项目')
        const hasParentQuantityChange = this.checkParentQuantityChange(targetItem)
        if (hasParentQuantityChange) {
          this.$set(targetItem, 'changeType', changeType)
          this.$set(targetItem, 'changeContent', this.getChangeTypeText(targetItem.changeType))
        }
      }
    },

    // 删除表格项目
    deleteTableItem(uuid) {
      const targetItem = this.tbData.find(item => item.uuid === uuid)
      if (!targetItem) return

      this.$set(targetItem, 'changeType', 'isDelete')
      this.$set(targetItem, 'changeContent', this.getChangeTypeText('isDelete'))
      this.$set(targetItem, 'isDisabled', false)
      this.updateChildrenForParentQuantityChange(targetItem, 'isDelete', false)

      if (targetItem.Type === 3) {
        const similarItems = this.findSimilarItems(targetItem)
        similarItems.forEach(similarItem => {
          const isSameParent = this.isSameParent(similarItem, targetItem)
          if (isSameParent) {
            this.$set(similarItem, 'changeType', 'isDelete')
            this.$set(similarItem, 'changeContent', this.getChangeTypeText('isDelete'))
            const parent = this.findParentItem(similarItem)
            this.$set(similarItem, 'isDisabled', parent.changeType === 'isDelete' || false)
          }
        })
      }
    },

    getItemChangeType(item, changeType = '') {
      const changeCode = [...(this.$store.state.contactList.changeCode[item.uuid] || [])]
      const type = this.determineChangeType(changeCode)
      return this.getFinalChangeType([changeType, type])
    },

    // 检查父级数量变化
    checkParentQuantityChange(item) {
      const parent = this.findParentItem(item)
      if (!parent) return false

      const parentChanges = this.$store.state.contactList.changeCode[parent.uuid] || []
      const result = parentChanges.some(change => change.Code === 'SteelAmount')
      if (result) return true
      return this.checkParentQuantityChange(parent)
    },

    // 检查父级数量变化类型（增加/减少/无变化）
    checkParentQuantityChangeType(item) {
      const parent = this.findParentItem(item)
      if (!parent) return 'isNoChange'

      // if (parent.changeType === 'isDelete') {
      //   item.changeType = 'isDelete'
      //   return this.checkParentQuantityChangeType(parent)
      // }

      const parentChanges = this.$store.state.contactList.changeCode[parent.uuid] || []
      const steelAmountChange = parentChanges.find(change => change.Code === 'SteelAmount')
      if (!steelAmountChange) return this.checkParentQuantityChangeType(parent)
      const oldValue = parseFloat(steelAmountChange.Value) || 0
      const newValue = parseFloat(steelAmountChange.NewValue) || 0
      if (newValue > oldValue) return 'isIncrease'
      if (newValue < oldValue) return 'isDecrease'
      return this.checkParentQuantityChangeType(parent)
    },

    restoreTableItem(uuid, visited = new Set()) {
      if (visited.has(uuid)) return
      visited.add(uuid)

      const targetItem = this.tbData.find(item => item.uuid === uuid)
      if (!targetItem) return

      // 检查是否有父级影响
      const hasParentQuantityChange = this.checkParentQuantityChange(targetItem)

      let newChangeType = 'isNoChange'

      let pType = ''
      if (hasParentQuantityChange) {
        pType = this.checkParentQuantityChangeType(targetItem)
      }
      newChangeType = this.getItemChangeType(targetItem, pType)

      this.$set(targetItem, 'changeType', newChangeType)
      this.$set(targetItem, 'changeContent', this.getChangeTypeText(newChangeType))
      this.$set(targetItem, 'isDisabled', false)

      if (targetItem.Type === 3) {
        const similarItems = this.findSimilarItems(targetItem)
        similarItems.forEach(similarItem => {
          if (this.isSameParent(similarItem, targetItem)) {
            (!similarItem.isDisabled) && this.restoreTableItem(similarItem.uuid, visited)
          }
        })
      }
      // 递归恢复子项
      if (targetItem.children) {
        targetItem.children.forEach(child => {
          this.restoreTableItem(child.uuid, visited)
        })
      }
    },
    resetTableItemDate(targetUuid) {
      const targetItem = this.tbData.find(item => item.uuid === targetUuid)
      if (!targetItem) return
      const defaultItem = this.defaultTbData.find(item => item.uuid === targetUuid)
      if (!defaultItem) return
      const { changeType, changeContent, isDisabled, ...rest } = defaultItem
      Object.assign(targetItem, { ...rest })
      this.$store.dispatch('contactList/delChangeCode', targetUuid)
    }
    // resetDeletedContent(uuid) {
    //   const targetContent = this.$store.state.contactList.changeCode[uuid] || []
    //   const defaultItem = this.defaultTbData.find(item => item.uuid === uuid)
    //   if (!defaultItem) return
    //   const defaultContent = JSON.parse(defaultItem.MocContent) || []
    //   const targetItem = this.tbData.find(item => item.uuid === uuid)
    //   if (!targetItem) return

    //   console.log('defaultContent', defaultContent)
    //   console.log('targetContent', targetContent)
    //   // 遍历defaultContent，找出在targetContent中不存在的字段
    //   defaultContent.forEach(def => {
    //     if (!targetContent.some(item => item.Code === def.ChangeFieldCode)) {
    //       // 如果targetContent中不存在该字段，则重置targetItem的该字段值
    //       console.log('def', def)
    //       targetItem[def.ChangeFieldCode] = defaultItem[def.ChangeFieldCode]
    //     }
    //   })
    //   // 不处理targetContent中比defaultContent多的字段
    //   return
    // }
  }

}

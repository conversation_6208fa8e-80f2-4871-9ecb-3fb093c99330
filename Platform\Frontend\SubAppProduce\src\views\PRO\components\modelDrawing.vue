<template>
  <div>
    <el-drawer
      :visible.sync="drawer"
      direction="btt"
      size="60%"
      destroy-on-close
      :before-close="handleCloseDrawer"
      @opened="renderIframe"
    >
      <div v-if="type==='构件'" style="width: 100%; display: flex; align-items: center">
        <div
          style="
            width: 50%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 30px;
          "
        >
          <div style="margin-left: 10px">
            <span style="display: inline-block; width: 100px">{{
              extensionName ? "构件模型" : "构件图纸"
            }}</span>
          </div>
          <el-button v-if="fullscreenid" @click="fullscreen(0)">全屏</el-button>
          <el-button v-if="fullbimid" @click="fullscreen(1)">全屏</el-button>
        </div>
        <div style="width: 50%">
          <div v-if="extensionName" style="margin-left: 10px">
            <span style="display: inline-block; width: 100px">构件图纸</span>
          </div>
        </div>
      </div>
      <div v-else style="width: 100%; display: flex">
        <div style="margin-left: 20px">
          <span style="display: inline-block; width: 100px">{{ type }}图纸</span>
        </div>
        <el-button
          v-if="fileBim"
          style="margin-left: 42%"
          @click="fullscreen(2)"
        >全屏</el-button>
      </div>
      <iframe
        id="frame"
        :key="iframeKey"
        :src="iframeUrl"
        style="width: 100%; border: 0px; margin: 0; height: 60vh"
      />
    </el-drawer>
    <el-drawer
      :visible.sync="drawersull"
      direction="btt"
      size="100%"
      destroy-on-close
    >
      <iframe
        v-if="templateUrl"
        id="fullFrame"
        :src="templateUrl"
        frameborder="0"
        style="width: 96%; margin-left: 2%; height: 70vh; margin-top: 2%"
      />
    </el-drawer>
  </div>
</template>

<script>
import { v4 as uuidv4 } from 'uuid'
import { getConfigure } from '@/api/user'
import { baseUrl } from '@/utils/baseurl'
export default {
  props: {
    type: {
      type: String,
      default: '' // 构件 、零件、部件
    }
  },
  data() {
    return {
      baseCadUrl: '',
      drawer: false,
      drawersull: false,
      iframeKey: '',
      fullscreenid: '',
      iframeUrl: '',
      fullbimid: '',
      fileBim: '',
      IsUploadCad: false,
      cadRowCode: '',
      cadRowProjectId: '',
      extensionName: '',
      templateUrl: ''
    }
  },
  computed: {

  },
  async created() {
    // await this.getBaseCadUrl()
  },
  mounted() {
    window.addEventListener('message', this.frameListener)
    this.$once('hook:beforeDestroy', () => {
      console.log('deactivated')
      window.removeEventListener('message', this.frameListener)
    })
  },
  activated() {
    window.addEventListener('message', this.frameListener)
    this.$once('hook:deactivated', () => {
      window.removeEventListener('message', this.frameListener)
    })
  },
  methods: {
    async getBaseCadUrl() {
      if (process.env.NODE_ENV === 'development') {
        // this.baseCadUrl = 'http://localhost:9529'
        // this.baseCadUrl = 'http://glendale-model.bimtk.com'
        this.baseCadUrl = 'http://glendale-model-dev.bimtk.tech'
      } else {
        const res = await getConfigure({ code: 'glendale_url' })
        this.baseCadUrl = res.Data
      }
    },
    // 查看图纸
    async dwgInit(data) {
      await this.getBaseCadUrl()
      this.extensionName = ''
      this.extensionName = data.extensionName
      this.fileBim = data.fileBim
      this.IsUploadCad = data.IsUpload
      this.cadRowCode = data.Code
      this.cadRowProjectId = data.Sys_Project_Id
      this.fileView()
    },
    handleCloseDrawer() {
      this.drawer = false
    },
    async fileView() {
      this.drawer = true // 先打开 drawer
      await this.$nextTick() // 等待 drawer 渲染完成
      this.iframeKey = uuidv4()
      const iframeId = this.type === '构件' ? 1 : 11
      // 添加随机参数防止缓存
      const cacheBuster = Date.now()
      this.iframeUrl = `${
        this.baseCadUrl
      }?router=1&iframeId=${iframeId}&baseUrl=${baseUrl()}&token=${localStorage.getItem(
        'Token'
      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}&_=${cacheBuster}`
    },
    renderIframe() {
      const iframeId = this.type === '构件' ? 1 : 11
      // 添加随机参数防止缓存
      const cacheBuster = Date.now()
      this.iframeUrl = `${
        this.baseCadUrl
      }?router=1&iframeId=${iframeId}&baseUrl=${baseUrl()}&token=${localStorage.getItem(
        'Token'
      )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}&_=${cacheBuster}`
      this.fullscreenid = this.extensionName
      this.fullbimid = this.fileBim
    },
    fullscreen(v) {
      let iframeId = null
      if (this.type === '构件') {
        iframeId = v === 0 ? 2 : 3
      } else {
        iframeId = 13
      }
      this.templateUrl = ''
      if (v === 0) {
        this.templateUrl = `${
          this.baseCadUrl
        }?router=1&iframeId=${iframeId}&baseUrl=${baseUrl()}&token=${localStorage.getItem(
          'Token'
        )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`
      } else if (v === 1) {
        this.templateUrl = `${
          this.baseCadUrl
        }?router=1&iframeId=${iframeId}&baseUrl=${baseUrl()}&token=${localStorage.getItem(
          'Token'
        )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`
      } else {
        this.templateUrl = `${
          this.baseCadUrl
        }?router=1&iframeId=${iframeId}&baseUrl=${baseUrl()}&token=${localStorage.getItem(
          'Token'
        )}&auth_id=${localStorage.getItem('Last_Working_Object_Id')}`
      }
      this.drawersull = true
    },
    frameListener({ data }) {
      if (data.type === 'loaded') {
        if (data.data.iframeId === '1') {
          document.getElementById('frame').contentWindow.postMessage(
            {
              type: 'router',
              path: '/modelCad',
              query: {
                // baseUrl: baseUrl(),
                featureId: this.fullscreenid,
                cadId: this.fileBim,
                projectId: this.cadRowProjectId,
                steelName: this.cadRowCode,
                showModel: !!this.fullscreenid,
                showCad: this.IsUploadCad
                // token: localStorage.getItem('Token'),
                // auth_id: localStorage.getItem('Last_Working_Object_Id')
              }
            },
            '*'
          )
        } else if (data.data.iframeId === '2') {
          document.getElementById('fullFrame').contentWindow.postMessage(
            {
              type: 'router',
              path: '/modelCad',
              query: {
                // baseUrl: baseUrl(),
                featureId: this.fullscreenid,
                projectId: this.cadRowProjectId,
                steelName: this.cadRowCode,
                showModel: !!this.fullscreenid
                // token: localStorage.getItem('Token'),
                // auth_id: localStorage.getItem('Last_Working_Object_Id')
              }
            },
            '*'
          )
        } else if (data.data.iframeId === '3') {
          document.getElementById('fullFrame').contentWindow.postMessage(
            {
              type: 'router',
              path: '/modelCad',
              query: {
                // baseUrl: baseUrl(),
                cadId: this.fileBim,
                projectId: this.cadRowProjectId,
                steelName: this.cadRowCode,
                showCad: this.IsUploadCad
                // token: localStorage.getItem('Token'),
                // auth_id: localStorage.getItem('Last_Working_Object_Id')
              }
            },
            '*'
          )
        } else if (data.data.iframeId === '11') {
          document.getElementById('frame').contentWindow.postMessage(
            {
              type: 'router',
              path: '/modelCad',
              query: {
                // baseUrl: baseUrl(),
                cadId: this.fileBim,
                projectId: this.cadRowProjectId,
                steelName: this.cadRowCode,
                showCad: this.IsUploadCad,
                isSubAssembly: this.type === '部件',
                isPart: true
                // cadId: this.fileBim
                // token: localStorage.getItem('Token'),
                // auth_id: localStorage.getItem('Last_Working_Object_Id')
              }
            },
            '*'
          )
        } else if (data.data.iframeId === '13') {
          document.getElementById('fullFrame').contentWindow.postMessage(
            {
              type: 'router',
              path: '/modelCad',
              query: {
                // baseUrl: baseUrl(),
                cadId: this.fileBim,
                projectId: this.cadRowProjectId,
                steelName: this.cadRowCode,
                showCad: this.IsUploadCad,
                isSubAssembly: this.type === '部件',
                isPart: true
                // token: localStorage.getItem('Token'),
                // auth_id: localStorage.getItem('Last_Working_Object_Id')
              }
            },
            '*'
          )
        }
      }
    }
  }
}
</script>
<style lang="scss">

</style>

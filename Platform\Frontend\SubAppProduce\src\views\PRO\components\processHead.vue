<template>
  <div>
    <div v-if="noStyle">
      <el-button type="primary" @click="opendialog('approve')">{{ approve }}</el-button>
      <el-button v-if="showReject" type="danger" @click="opendialog('reject')">{{ reject }}</el-button>
      <el-button v-if="showrefuse" type="danger" @click="opendialog('refuse')">{{ refuse }}</el-button>
    </div>
    <div v-else class="processhead">
      <div class="title"><span class="span" />{{ title }}</div>
      <div>
        <el-button type="primary" @click="opendialog('approve')">{{ approve }}</el-button>
        <el-button v-if="showReject" type="danger" @click="opendialog('reject')">{{ reject }}</el-button>
        <el-button v-if="showrefuse" type="danger" @click="opendialog('refuse')">{{ refuse }}</el-button>
      </div>
    </div>
    <bimdialog
      dialog-title="审批意见"
      dialog-width="660px"
      :visible.sync="showaudit"
      :hidebtn="false"
      append-to-body
      @submitbtn="audit"
      @cancelbtn="closeaudit"
      @handleClose="closeaudit"
    >
      <el-form ref="form">
        <template v-if="webId==='MocOrderForm' && type==='approve'">
          <el-form-item label="变更费用:" :required="required" label-width="80">
            <el-input v-model="fee" type="number" />
          </el-form-item>
          <el-form-item label="费用说明:" label-width="80">
            <el-input v-model="desc" />
          </el-form-item>
        </template>
        <el-form-item label="审批意见:" :required="required" label-width="80">
          <el-input v-model="VerificationOpinion" type="textarea" />
        </el-form-item>
      </el-form>
    </bimdialog>
  </div>
</template>
<script>
import bimdialog from '@/views/plm/components/dialog'
// import { Verification } from '@/api/plm/processmanagement'
import { Verification } from '@/api/sys/flow'
import { closeTagView } from '@/utils'
import { baseUrl } from '@/utils/baseurl'
export default {
  components: {
    bimdialog
  },
  props: {
    title: {
      type: String,
      default: '流程计划审批'
    },
    // 流程Id
    processId: {
      type: String,
      default: ''
    },
    approve: {
      type: String,
      default: '通 过'
    },
    reject: {
      type: String,
      default: '驳 回'
    },
    nodeRejectType: {
      type: String,
      default: '1'
    },
    nodeRejectStep: {
      type: String,
      default: ''
    },
    refuse: {
      type: String,
      default: '不通过'
    },
    showrefuse: {
      type: Boolean,
      default: false
    },
    beforeapprove: {
      type: Function,
      default: () => { return new Promise((resolve, reject) => { resolve() }) }
    },
    // 审批意见必填
    required: {
      type: Boolean,
      default: true
    },
    noStyle: {
      type: Boolean,
      default: false
    },
    showReject: {
      type: Boolean,
      default: true
    },
    webId: {
      type: String,
      default: ''
    },
    businessId: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      userId: localStorage.getItem('UserId'),
      userName: localStorage.getItem('UserName'),
      FormDataJson: '',
      showaudit: false,
      type: '',
      VerificationOpinion: '',
      // 变更审批 通过的时候需要的
      fee: '',
      desc: ''
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (this.webId === 'MocOrderForm') {
          this.fee = val.fee || ''
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    opendialog(type) {
      this.showaudit = true
      this.type = type
    },
    closeaudit() {
      this.VerificationOpinion = ''
      this.showaudit = false
    },
    audit() {
      if (this.type === 'approve') {
        this.approveIt()
      } else if (this.type === 'reject') {
        this.rejectIt()
      } else if (this.type === 'refuse') {
        this.refuseIt()
      }
    },
    // flowInstanceId:流程Id
    // VerificationFinally:1:同意；2：不同意；3：驳回
    // VerificationOpinion:审核意见
    // nodeRejectType:驳回至0:前一步/1:第一步/2：指定节点
    // nodeRejectStep:当驳回类型为2时，驳回结点code
    // 同意
    async approveIt() {
      // 如果变更审批 判断变更费用是否填写了
      if (this.webId === 'MocOrderForm' && !this.fee) {
        this.$message.warning('请填写变更费用')
        return
      }
      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {
        this.$message.warning('请填写审核意见')
        return
      }
      this.FormDataJson = JSON.stringify({
        Fee: this.fee,
        Fee_Remark: this.desc,
        Fee_UserId: this.userId,
        Fee_UserName: this.userName
      })
      this.beforeapprove().then(() => {
        var param = {
          flowInstanceId: this.processId,
          VerificationFinally: 1,
          VerificationOpinion: this.VerificationOpinion,
          NodeRejectType: this.nodeRejectType,
          NodeRejectStep: this.nodeRejectStep,
          WebId: this.webId,
          BusinessDateld: this.businessId,
          FormDataJson: this.FormDataJson,
          PlateForm_Url: baseUrl()
        }
        Verification(param).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '提交成功',
              type: 'success'
            })
            this.$emit('afterapproval', 'approve')
            this.showaudit = false
            // this.cancel()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    // 驳回
    async rejectIt() {
      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {
        this.$message.warning('请填写审核意见')
        return
      }
      this.FormDataJson = JSON.stringify({
        Fee: '0',
        Fee_Remark: '',
        Fee_UserId: this.userId,
        Fee_UserName: this.userName
      })
      var param = {
        flowInstanceId: this.processId,
        VerificationFinally: 2,
        VerificationOpinion: this.VerificationOpinion,
        NodeRejectType: this.nodeRejectType,
        NodeRejectStep: this.nodeRejectStep,
        WebId: this.webId,
        BusinessDateld: this.businessId,
        FormDataJson: this.FormDataJson,
        PlateForm_Url: baseUrl()
      }
      Verification(param).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '提交成功',
            type: 'success'
          })
          this.$emit('afterapproval', 'reject')
          this.showaudit = false
          // this.cancel()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 不同意
    async refuseIt() {
      if (this.required && !this.VerificationOpinion && this.VerificationOpinion !== 0) {
        this.$message.warning('请填写审核意见')
        return
      }
      var param = {
        flowInstanceId: this.processId,
        VerificationFinally: 2,
        VerificationOpinion: this.VerificationOpinion,
        NodeRejectType: this.nodeRejectType,
        NodeRejectStep: this.nodeRejectStep,
        WebId: this.webId,
        BusinessDateld: this.businessId,
        FormDataJson: this.FormDataJson,
        PlateForm_Url: baseUrl()
      }
      Verification(param).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '提交成功',
            type: 'success'
          })
          this.$emit('afterapproval', 'refuse')
          this.showaudit = false
          // this.cancel()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 关闭当前页
    cancel() {
      closeTagView(this.$store, this.$route)
    }
  }
}
</script>
<style scope lang="scss">
.processhead{
    width:100%;
    height:64px;
    background:#fff;
    line-height:24px;
    display:flex;
    justify-content:space-between;
    box-shadow: 0px 1px 3px 1px rgba(20,35,78,0.08);
    margin-bottom:15px;
    // border:1px solid #000;
    padding:20px 16px;
    .title{
        font-size: 18px;
        font-weight: bold;
        color: rgba(34,40,52,0.85);
    }
    .span{
        background-image: linear-gradient(180deg, #71B3FF 0%, #298DFF 100%);
        width:4px;
        height:14px;
        display:inline-block;
        border-radius:3px;
        margin-right:8px;
    }
}
</style>

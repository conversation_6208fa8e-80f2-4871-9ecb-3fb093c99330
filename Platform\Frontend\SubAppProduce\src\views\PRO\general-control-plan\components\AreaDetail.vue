<template>
  <div class="content">
    <div class="cs-search">
      <el-form
        ref="searchForm"
        :model="searchForm"
        :inline="true"
        label-width="80px"
        class="demo-form-inline"
      >
        <el-form-item
          class="mb0"
          label="构件名称"
          prop="SteelName"
        >
          <el-input
            v-model="searchForm.SteelName"
            placeholder="请输入"
            clearable
            style="width: 300px"
          />
        </el-form-item>
        <el-form-item class="mb0" label-width="16px">
          <el-button
            type="primary"
            @click="handleSearch()"
          >搜索
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tb-container">
      <vxe-table
        v-loading="tbLoading"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        element-loading-spinner="el-icon-loading"
        element-loading-text="拼命加载中"
        empty-text="暂无数据"
        class="cs-vxe-table"
        height="500"
        align="left"
        stripe
        :data="tbData"
        resizable
        :tooltip-config="{ enterable: true }"
      >
        <template v-for="item in columns">
          <vxe-column
            :key="item.Code"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            :width="item.Width ? item.Width : 0"
            :min-width="item.Width"
          >
            <template #default="{ row }">
              <span>
                {{ row[item.Code] | displayValue }}
              </span>
            </template>
          </vxe-column>
        </template>
      </vxe-table>
    </div>
    <div class="cs-bottom">
      <Pagination
        class="cs-table-pagination"
        :total="total"
        max-height="100%"
        :page-sizes="tablePageSize"
        :page.sync="queryInfo.Page"
        :limit.sync="queryInfo.PageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="changePage"
      />
    </div>
  </div>
</template>

<script>
import { GetAreaPlanBusinessData } from '@/api/PRO/control-plan'
import getTableInfo from '@/mixins/PRO/get-table-info'
import Pagination from '@/components/Pagination'
import { tablePageSize } from '@/views/PRO/setting'
import { timeFormat } from '@/filters'
export default {
  components: {
    Pagination
  },
  mixins: [getTableInfo],
  data() {
    return {
      searchForm: {
        SteelName: ''
      },
      tbLoading: false,
      columns: [],
      tbData: [],
      tbConfig: {},
      customParams: {},
      tablePageSize: tablePageSize,
      total: 0,
      queryInfo: {
        Page: 1,
        PageSize: 20
      }
    }
  },
  methods: {
    async init(rowData) {
      this.customParams = rowData
      await this.getTableConfig('PROPlanTrackAreaDetail')
      await this.fetchData()
    },
    handleSearch() {
      this.fetchData()
    },
    async fetchData() {
      this.tbLoading = true
      try {
        const res = await GetAreaPlanBusinessData({
          SteelName: this.searchForm.SteelName,
          Project_Id: this.customParams.Sys_Project_Id,
          Area_Id: this.customParams.Area_Id,
          Factory_Id: this.customParams.Factory_Id,
          ...this.queryInfo
        })
        if (res.IsSucceed) {
          const resData = res.Data.Data || []
          this.total = res.Data.TotalCount
          this.tbData = resData.map((v) => {
            v.Inventory_Upload_Date = timeFormat(v.Inventory_Upload_Date, '{y}-{m}-{d}')
            v.Drawing_Send_Date = timeFormat(v.Drawing_Send_Date, '{y}-{m}-{d}')
            v.Machining_Finish_Date = timeFormat(v.Machining_Finish_Date, '{y}-{m}-{d}')
            v.Latest_Install_Date = timeFormat(v.Latest_Install_Date, '{y}-{m}-{d}')
            return v
          })
        } else {
          this.tbData = []
          this.total = 0
          this.$message.error(res.Message)
        }
      } catch (e) {
        this.tbData = []
        this.$message.error('获取数据失败')
      } finally {
        this.tbLoading = false
      }
    },
    async changePage() {
      this.tbLoading = true
      if (
        typeof this.queryInfo.PageSize !== 'number' ||
        this.queryInfo.PageSize < 1
      ) {
        this.queryInfo.PageSize = 20
      }
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  .cs-search {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    .mb0 {
      margin-bottom: 0;

      ::v-deep {
        .el-form-item {
          margin-bottom: 0
        }
      }
    }
  }
  .tb-container {
    margin-top: 20px;
    flex: 1;
  }
}
.cs-bottom {
  padding: 0;
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;

  .data-info {
    .info-x {
      margin-right: 20px;
    }
  }
  .pg-input {
    width: 100px;
    margin-right: 20px;
  }
}
.pagination-container {
  text-align: right;
  margin-top: 16px;
  padding: 0;
  ::v-deep .el-input--small .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}
</style>

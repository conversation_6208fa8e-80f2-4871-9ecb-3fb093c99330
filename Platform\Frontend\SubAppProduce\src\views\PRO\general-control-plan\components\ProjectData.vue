<template>
  <div>
    <ExpandableSection v-model="showExpand" :width="300" class="cs-left fff">
      <div class="inner-wrapper">
        <div class="tree-search">
          <el-input
            v-model.trim="projectName"
            placeholder="关键词搜索"
            size="small"
            clearable
            suffix-icon="el-icon-search"
            @blur="fetchTreeDataLocal"
            @clear="fetchTreeDataLocal"
            @keydown.enter.native="fetchTreeDataLocal"
          />
        </div>
        <el-divider class="cs-divider" />
        <div class="tree-x cs-scroll">
          <div v-for="item in treeData" :key="item.Sys_Project_Id" class="project-list" :class="{ active: item.Sys_Project_Id === Active_Sys_Project_Id }" @click="handleNodeClick(item)">
            <el-tooltip class="item" effect="dark" :content="item.Short_Name" :open-delay="200" placement="top">
              <div class="project-inner">
                <div>
                  <svg-icon
                    icon-class="icon-folder"
                    class-name="class-icon"
                  />
                </div>
                <span class="code">({{ item.Code }})</span>
                <span class="name">{{ item.Short_Name }}</span>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </ExpandableSection>
  </div>
</template>

<script>
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import {
  GetProjectListForPlanTrace
} from '@/api/PRO/project'
export default {
  components: {
    ExpandableSection
  },
  data() {
    return {
      Active_Sys_Project_Id: '',
      showExpand: true,
      treeLoading: true,
      projectName: '',
      treeData: [],
      originalTreeData: [] // 保存原始数据
    }
  },
  created() {
    this.fetchTreeData()
  },
  methods: {
    // 项目数据集
    fetchTreeData() {
      GetProjectListForPlanTrace({ }).then((res) => {
        if (!res.IsSucceed) {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.treeLoading = false
          this.treeData = []
          return
        }
        if (res.Data.length === 0) {
          this.treeLoading = false
          return
        }
        const resData = res.Data
        this.originalTreeData = [...resData] // 保存原始数据
        this.treeData = [...resData]
        this.treeLoading = false
        if (this.treeData.length > 0) {
          this.handleNodeClick(resData[0])
        }
      })
    },
    // 选中左侧项目节点
    handleNodeClick(data) {
      this.Active_Sys_Project_Id = data.Sys_Project_Id
      this.$emit('setProjectData', data)
    },
    fetchTreeDataLocal() {
      // 如果搜索关键词为空，恢复原始数据
      if (!this.projectName || this.projectName.trim() === '') {
        this.treeData = [...this.originalTreeData]
        // 恢复原始数据后，如果有数据则选中第一个
        if (this.treeData.length > 0) {
          this.handleNodeClick(this.treeData[0])
        }
        return
      }

      // 从原始数据中过滤，支持大小写不敏感搜索
      const searchTerm = this.projectName.trim().toLowerCase()
      this.treeData = this.originalTreeData.filter((item) => {
        return item.Short_Name &&
               item.Short_Name.toLowerCase().includes(searchTerm)
      })

      // 搜索后如果有数据，自动选中第一个项目
      // if (this.treeData.length > 0) {
      //   this.handleNodeClick(this.treeData[0])
      // }
    }
  }

}
</script>
<style lang="scss" scoped>
  @import "~@/styles/mixin.scss";
  @import "~@/styles/tabs.scss";
  .cs-left {
    position: relative;
    margin-right: 20px;

  }
  .cs-left-contract {
    padding-left: 0;
    position: relative;
    width: 20px;
    margin-right: 26px;
  }
  .inner-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 10px 16px 16px;
    border-radius: 4px;
    overflow: hidden;

    .tree-search {
      display: flex;

      .search-select {
        margin-right: 8px;
      }
    }

    .tree-x {
      overflow: auto;
      margin-top: 16px;
      flex: 1;
      .project-list {
        height: 32px;
        padding-left: 16px;
        font-size: 14px;
        .project-inner {
          height: 100%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: start;
          white-space: nowrap; /* 禁止换行 */
          overflow: hidden; /* 隐藏溢出内容 */
          text-overflow: ellipsis; /* 溢出显示省略号 */
          .code {
            color: #5ac8fa;
            margin-left: 5px;
            margin-right: 5px;
            flex-shrink: 0; /* 禁止压缩 */
          }
          .name {
            color: rgba(34, 40, 52, .65);
            flex-shrink: 1; /* 允许适当压缩 */
            min-width: 0; /* 允许文本截断 */
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .project-list:hover {
        background-color: rgba(41, 141, 255, .04);
      }
      .project-list.active {
        background-color: #eef6ff;
      }
    }
    .cs-scroll {
      overflow-y: auto;
      @include scrollBar;
    }
  }
  .cs-divider {
    margin: 16px 0 0 0;
  }
</style>

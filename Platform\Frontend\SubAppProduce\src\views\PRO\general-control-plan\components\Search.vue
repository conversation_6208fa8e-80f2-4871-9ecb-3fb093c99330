<template>
  <div class="cs-search-form">
    <div class="cs-search">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="80px"
        class="demo-form-inline"
      >
        <el-row>
          <el-col v-if="title !== '采购'" :span="6">
            <el-form-item
              class="mb0"
              label="加工工厂"
              prop="Factory_Id"
            >
              <el-select
                v-model="searchForm.Factory_Id"
                filterable
                clearable
                placeholder="请选择"
                style="width: 100%"
                @change="handleFactoryChange"
              >
                <el-option
                  v-for="item in FactoryOption"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              class="mb0"
              label="单体"
              prop="Monomer_Id"
            >
              <el-select
                v-model="searchForm.Monomer_Id"
                filterable
                clearable
                placeholder="请选择"
                :disabled="!HasMonomer"
                style="width: 100%"
                @change="handleMonomerChange"
              >
                <el-option
                  v-for="item in MonomerOption"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              class="mb0"
              label="区域"
              prop="Area_Id"
            >
              <el-select
                v-model="searchForm.Area_Id"
                filterable
                clearable
                placeholder="请选择"
                :disabled="HasMonomer && !searchForm.Monomer_Id"
                style="width: 100%"
              >
                <el-option
                  v-for="item in AreaOption"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="mb0" label-width="16px">
              <el-button
                type="primary"
                @click="handleSearch()"
              >搜索
              </el-button>
              <el-button @click="handleSearch('reset')">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import {
  GetProjectMonomerListForPlanTrace,
  GetProjectAreaListForPlanTrace
} from '@/api/PRO/project'
import { GetFactoryPageList } from '@/api/PRO/factory'
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchForm: {
        Factory_Id: '',
        Monomer_Id: '',
        Sys_Project_Id: '',
        Area_Id: ''
      },

      CurPlatform: localStorage.getItem('CurPlatform'), // 0公司 2工厂 3项目
      CurReferenceId: localStorage.getItem('CurReferenceId'),

      HasMonomer: false,
      FactoryOption: [], // 工厂数组
      MonomerOption: [], // 单体数组
      AreaOption: [] // 区域数组
    }
  },
  created() {

  },
  methods: {
    // 获取工厂
    async getCurrentFactory(Sys_Project_Id = '', hasMonomer = false) {
      this.searchForm.Monomer_Id = ''
      this.searchForm.Sys_Project_Id = Sys_Project_Id
      this.HasMonomer = hasMonomer

      const PermittedFactoryList = JSON.parse(localStorage.getItem('PermittedFactoryList'))
      if (this.CurPlatform === '0') { // 公司下取所有工厂
        await this.getFactoryPageList()
      } else if (this.CurPlatform === '2') { // 工厂下取当前工厂
        this.FactoryOption = PermittedFactoryList.filter((item) => item.Reference_Id === this.CurReferenceId).map(item => ({
          Name: item.Display_Name,
          Id: item.Reference_Id
        }))
        this.searchForm.Factory_Id = this.CurReferenceId
      } else { // 项目下取所有工厂
        await this.getFactoryPageList(true)
      }
      await this.getProjectMonomerListForPlanTrace()
      // 如果没有单体 直接获取区域
      if (!this.HasMonomer) {
        await this.getProjectAreaListForPlanTrace()
      }
    },

    // 项目级/公司级 获取工厂
    async getFactoryPageList(Is_Query_Project) {
      const res = await GetFactoryPageList({
        Page: 1,
        PageSize: -1,
        Sys_Project_Id: this.searchForm.Sys_Project_Id,
        Is_Query_Project: Is_Query_Project || false
      })
      if (res.IsSucceed) {
        const resData = res.Data.Data
        if (resData.length) {
          this.FactoryOption = resData
          this.searchForm.Factory_Id = resData[0].Id
        }
      }
    },

    // 获取单体
    getProjectMonomerListForPlanTrace() {
      GetProjectMonomerListForPlanTrace({
        Sys_Project_Id: this.searchForm.Sys_Project_Id,
        Factory_Id: this.searchForm.Factory_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.MonomerOption = res.Data || []
        }
      })
    },

    // 获取区域
    getProjectAreaListForPlanTrace() {
      GetProjectAreaListForPlanTrace({
        Sys_Project_Id: this.searchForm.Sys_Project_Id,
        Factory_Id: this.searchForm.Factory_Id,
        Monomer_Id: this.searchForm.Monomer_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.AreaOption = res.Data || []
        }
      })
    },

    // 选择工厂
    async handleFactoryChange() {
      this.searchForm.Monomer_Id = ''
      this.searchForm.Area_Id = ''
      await this.getProjectMonomerListForPlanTrace()
      // 如果没有单体 直接获取区域
      if (!this.HasMonomer) {
        await this.getProjectAreaListForPlanTrace()
      }
    },

    // 选择单体
    async handleMonomerChange() {
      this.searchForm.Area_Id = ''
      if (this.searchForm.Monomer_Id) {
        await this.getProjectAreaListForPlanTrace()
      }
    },

    // 点击搜索
    handleSearch(reset) {
      const resetMappings = {
        reset: { Factory_Id: '', Monomer_Id: '', Area_Id: '' },
        project: { Monomer_Id: '', Area_Id: '' }
      }
      if (reset && resetMappings[reset]) {
        this.searchForm = { ...this.searchForm, ...resetMappings[reset] }
      }
      this.$emit('search', this.searchForm)
    }
  }

}
</script>
<style lang="scss" scoped>
  .cs-search-form {
    width: 100%;
    .cs-search {
      width: 100%;
      .cs-from-title {
        flex: 1;
      }

      .mb0 {
        margin-bottom: 0;

        ::v-deep {
          .el-form-item {
            margin-bottom: 0
          }
        }
      }
    }
  }
  .cs-tree-x {
    ::v-deep {
      .el-select {
        width: 100%;
      }
    }
  }
</style>

<template>
  <div class="app-container abs100">
    <div
      v-loading="pgLoading"
      style="display: flex"
      class="h100"
      element-loading-text="加载中"
    >
      <ProjectData @setProjectData="setProjectDataFn" />
      <div class="cs-right" style="padding-right: 0">
        <div class="container">
          <div ref="searchDom" class="cs-from">
            <Search ref="searchRef" title="采购" @search="searchFn" />
          </div>
          <div class="fff cs-z-tb-wrapper">
            <div class="tb-container">
              <vxe-table
                ref="tableRef"
                :key="tbKey"
                v-loading="tbLoading"
                :empty-render="{name: 'NotData'}"
                show-header-overflow
                element-loading-spinner="el-icon-loading"
                element-loading-text="拼命加载中"
                empty-text="暂无数据"
                class="cs-vxe-table"
                height="100%"
                align="left"
                stripe
                :data="tbData"
                resizable
                :tooltip-config="{ enterable: true }"
                :row-config="{ isHover: true }"
                :edit-config="{
                  trigger: 'click',
                  mode: 'cell',
                  showIcon: true
                }"
                @edit-closed="editClosedEvent"
              >
                <template v-for="(item) in columns">
                  <vxe-column
                    :key="item.Code"
                    :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                    show-overflow="tooltip"
                    sortable
                    :align="item.Align"
                    :field="item.Code"
                    :title="item.Display_Name"
                    :width="item.Width ? item.Width : 0"
                    :min-width="item.minWidth"
                    :edit-render="item.Is_Edit ? {} : null"
                  >
                    <template v-if="item.Is_Edit" #edit="{ row }">
                      <template v-if="['Actual_Begin_Date'].includes(item.Code)">
                        <vxe-input
                          v-model="row[item.Code]"
                          type="date"
                          placeholder="请选择日期"
                        />
                      </template>
                      <template v-else-if="['Actual_End_Date'].includes(item.Code)">
                        <vxe-input
                          v-model="row[item.Code]"
                          type="date"
                          placeholder="请选择日期"
                        />
                      </template>
                      <template v-else>
                        <vxe-input
                          v-model="row[item.Code]"
                          type="number"
                          placeholder="请输入"
                          min="0"
                        />
                      </template>
                    </template>
                    <template #default="{ row }">
                      <div v-if="item.Code === 'Full_Area_Name'">
                        <span>{{ row[item.Code] }}</span>
                      </div>
                      <div v-else-if="item.Code === 'Status'">
                        <el-tag v-if="row[item.Code]==='延期'" size="mini" effect="dark" type="warning">延期</el-tag>
                        <el-tag v-else size="mini" effect="dark">正常</el-tag>
                      </div>
                      <div v-else-if="item.Code === 'Is_Finish'">
                        <span v-if="row[item.Code] === '已完成'" class="by-dot by-dot-success">
                          {{ row[item.Code] }}
                        </span>
                        <span v-else class="by-dot by-dot-info">
                          {{ row[item.Code] }}
                        </span>
                      </div>
                      <div v-else>
                        <span>{{ row[item.Code] | displayValue }}</span>
                      </div>
                    </template>
                  </vxe-column>
                </template>
              </vxe-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card" />
  </div>
</template>

<script>
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { GetPurchaseControlPlan, SavePurchaseControlPlan } from '@/api/PRO/control-plan'

import ProjectData from '../components/ProjectData'
import Search from '../components/Search'

import elDragDialog from '@/directive/el-drag-dialog'
import { timeFormat } from '@/filters'

import AuthButtons from '@/mixins/auth-buttons'
import getTableInfo from '@/mixins/PRO/get-table-info'
import sysUseType from '@/directive/sys-use-type/index.js'
export default {
  name: 'PROPurchasePlanTrack',
  directives: { elDragDialog, sysUseType },
  components: {
    ProjectData,
    Search
  },
  mixins: [AuthButtons, getTableInfo],
  data() {
    return {
      Actual_Begin_Date: '',
      searchHeight: 0,
      tbKey: 100,
      tbData: [],
      tbLoading: false,
      pgLoading: false,
      tbConfig: {},

      queryInfo: {
        ParameterJson: []
      },

      HasMonomer: false,
      FactoryOption: [], // 工厂数组
      MonomerOption: [], // 单体数组
      AreaOption: [], // 区域数组

      customParams: {
        Factory_Id: '',
        Monomer_Id: '',
        Area_Id: '',
        Sys_Project_Id: '',
        Project_Name: '',
        TypeId: '',
        TypeName: ''
      },

      typeOption: [],
      columns: [],
      title: '',
      width: '60%',
      tipLabel: '',
      monomerList: [],

      sysUseType: undefined,

      Unit: '',
      Proportion: 0 // 专业的单位换算
    }
  },
  computed: {
    typeEntity() {
      return this.typeOption.find((i) => i.Id === this.customParams.TypeId)
    }
  },
  watch: {
  },
  async created() {
    await this.getTypeList()
  },
  mounted() {
    this.pgLoading = true
    this.searchHeight = this.$refs.searchDom.offsetHeight + 327
  },
  methods: {
    searchFn(data) {
      this.customParams = Object.assign({}, this.customParams, data)
      this.fetchData()
    },
    async setProjectDataFn(data) {
      this.customParams.Sys_Project_Id = data.Sys_Project_Id
      this.customParams.Project_Name = data.Short_Name
      this.HasMonomer = data.HasMonomer
      this.pgLoading = true

      await this.$refs.searchRef.getCurrentFactory(this.customParams.Sys_Project_Id, this.HasMonomer)
      await this.$refs.searchRef.handleSearch('project', false)
    },

    async fetchList() {
      await GetPurchaseControlPlan({
        Sys_Project_Id: this.customParams.Sys_Project_Id,
        Monomer_Id: this.customParams.Monomer_Id,
        Area_Id: this.customParams.Area_Id
      })
        .then((res) => {
          if (res.IsSucceed) {
            this.tbData = res.Data.map((v) => {
              v.Actual_Begin_Date = timeFormat(v.Actual_Begin_Date, '{y}-{m}-{d}')
              v.Actual_End_Date = timeFormat(v.Actual_End_Date, '{y}-{m}-{d}')
              v.Plan_Begin_Date = timeFormat(v.Plan_Begin_Date, '{y}-{m}-{d}')
              v.Plan_End_Date = timeFormat(v.Plan_End_Date, '{y}-{m}-{d}')
              v.Finish_Rate = this.finishRate(v.Plan_Finish_Quantity, v.Actual_Finish_Quantity).display
              v.Status = this.calculateStatus(v.Plan_End_Date, this.finishRate(v.Plan_Finish_Quantity, v.Actual_Finish_Quantity).isBelowTarget)
              v.Is_Finish = this.finishRate(v.Plan_Finish_Quantity, v.Actual_Finish_Quantity).isBelowTarget ? '未完成' : '已完成'
              return v
            })
            // this.selectList = []
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
        .finally(() => {
          this.tbLoading = false
          this.pgLoading = false
        })
    },

    // 计算完成率
    finishRate(Plan_Finish_Quantity, Actual_Finish_Quantity) {
      // 处理特殊情况
      if (!Plan_Finish_Quantity && !Actual_Finish_Quantity) {
        return {
          display: '-',
          value: 0,
          isBelowTarget: true
        }
      }
      if (!Plan_Finish_Quantity) {
        return {
          display: '100%',
          value: 100,
          isBelowTarget: false
        }
      }
      if (!Actual_Finish_Quantity) {
        return {
          display: '0%',
          value: 0,
          isBelowTarget: true
        }
      }
      // 计算完成率
      const rate = (Actual_Finish_Quantity / Plan_Finish_Quantity) * 100
      // 处理非数字情况
      if (isNaN(rate)) {
        return {
          display: '0%',
          value: 0,
          isBelowTarget: true
        }
      }
      // 判断是否为整数
      const isInteger = Number.isInteger(rate)
      return {
        display: isInteger ? `${rate}%` : `${rate.toFixed(2)}%`,
        value: rate,
        isBelowTarget: rate < 100
      }
    },

    // 判断是否延期
    calculateStatus(planEndDate, isBelowTarget) {
      const currentDate = new Date() // 当前时间
      const endDate = planEndDate ? new Date(planEndDate) : new Date('2000-01-01') // 计划结束时间
      // 如果当前时间超过计划结束时间，并且完成率低于100%
      if (currentDate > endDate && isBelowTarget) {
        return '延期'
      } else {
        return '正常'
      }
    },

    async fetchData() {
      await this.getTableConfig('PROPurchasePlanTrackList')
      this.columns.map((item) => {
        if (item.Code === 'Factory_Name') {
          item.Width = ''
          item.minWidth = 200
        }
        return item
      })
      this.tbLoading = true
      this.fetchList().then((res) => {
        this.tbLoading = false
      })
    },

    async getTypeList() {
      let res = null
      let data = null
      res = await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      })
      data = res.Data
      if (res.IsSucceed) {
        this.typeOption = Object.freeze(data)
        if (this.typeOption.length > 0) {
          this.Proportion = data[0].Proportion
          this.Unit = data[0].Unit
          this.customParams.TypeId = this.typeOption[0]?.Id
          this.customParams.TypeName = this.typeOption[0]?.Name
        }
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    async savePurchaseControlPlan(row, field, cellValue) {
      const res = await SavePurchaseControlPlan({
        Id: row.Id,
        Actual_Begin_Date: row.Actual_Begin_Date,
        Actual_End_Date: row.Actual_End_Date,
        Actual_Finish_Quantity: row.Actual_Finish_Quantity
      })
      if (res.IsSucceed) {
        this.$message({
          message: '保存成功',
          type: 'success'
        })
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    async editClosedEvent({ row, column }) {
      const $table = this.$refs.tableRef
      if ($table) {
        const field = column.field
        const cellValue = row[field]
        if ($table.isUpdateByRow(row, field)) {
          await this.savePurchaseControlPlan(row, field, cellValue)
          setTimeout(() => {
            row.Finish_Rate = this.finishRate(row.Plan_Finish_Quantity, row.Actual_Finish_Quantity).display
            row.Status = this.calculateStatus(row.Plan_End_Date, this.finishRate(row.Plan_Finish_Quantity, row.Actual_Finish_Quantity).isBelowTarget)
            row.Is_Finish = this.finishRate(row.Plan_Finish_Quantity, row.Actual_Finish_Quantity).isBelowTarget ? '未完成' : '已完成'
            // this.$message({
            //   message: `局部保存成功！ ${field}=${cellValue}`,
            //   type: 'success'
            // })
            $table.reloadRow(row, null, field)
          }, 300)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/tabs.scss";
.min900 {
  min-width: 900px;
  overflow: auto;
}
.z-dialog {
  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      // max-height: 750px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}

.container {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tb-container {
  padding: 0 16px 0 16px;
  flex: 1;
  height: 0; //解决溢出问题
  // .vxe-table {
  //   height: calc(100%);
  // }
}

.cs-z-tb-wrapper {
  display: flex;
  flex-direction: column;
  height: 0; //解决溢出问题
  padding-top: 16px;
}

.cs-bottom {
  padding: 8px 16px 8px 16px;
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;

  .data-info {
    .info-x {
      margin-right: 20px;
    }
  }
  .pg-input {
    width: 100px;
    margin-right: 20px;
  }
}

.pagination-container {
  text-align: right;
  margin: 0;
  padding: 0;
  ::v-deep .el-input--small .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}

.cs-from {
  background-color: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 16px 16px 16px 16px;
  display: flex;
  font-size: 14px;
  color: rgba(34, 40, 52, 0.65);
}

.input-with-select {
  width: 250px;
}

.cs-button-box {
  padding: 16px 16px 6px 16px;
  position: relative;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ::v-deep .el-button {
    margin-left: 0 !important;
    margin-right: 10px !important;
    margin-bottom: 10px !important;
  }
}
.info-box {
  margin: 0 16px 16px 16px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  height: 64px;
  background: rgba(41, 141, 255, 0.05);

  .cs-col {
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 64px;
  }

  .info-label {
    color: #999999;
  }

  i {
    color: #00c361;
    font-style: normal;
    font-weight: 600;
    margin-left: 10px;
  }
}

.stretch-btn {
  position: absolute;
  width: 20px;
  height: 130px;
  top: calc((100% - 130px) / 2);

  display: flex;
  align-items: center;
  background: #eff1f3;
  cursor: pointer;
  .center-btn {
    width: 14px;
    height: 100px;
    border-radius: 0 9px 9px 0;
    background-color: #8c95a8;
    > i {
      line-height: 100px;
      text-align: center;
      color: #fff;
    }
  }
}

.cs-right {
  padding-right: 0;
  flex: 1;
  width: 0;
}
* {
  box-sizing: border-box;
}
.fourGreen {
  color: #00c361;
  font-style: normal;
}

.fourOrange {
  color: #ff9400;
  font-style: normal;
}

.fourRed {
  color: #ff0000;
  font-style: normal;
}

.cs-blue {
  color: #5ac8fa;
}

.orangeBg {
  background: rgba(255, 148, 0, 0.1);
}

.redBg {
  background: rgba(252, 107, 127, 0.1);
}
.greenBg {
  background: rgba(0, 195, 97, 0.1);
}

.cs-tag {
  margin-left: 8px;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 1px;
}

.by-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #f56c6c;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.by-dot-success {
  color: #67c23a;
  &:before {
    background: #67c23a;
  }
}
.by-dot-primary {
  color: #409eff;
  &:before {
    background: #409eff;
  }
}

.by-dot-info {
  &:before {
    background: #909399;
  }
}
</style>

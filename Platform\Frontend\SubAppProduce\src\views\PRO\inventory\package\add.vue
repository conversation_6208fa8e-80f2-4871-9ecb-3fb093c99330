<template>
  <div class="abs100" style="padding:16px;">
    <el-button style="margin-bottom: 16px" @click="tagBack">返回</el-button>
    <div class="sch-detail">
      <div class="cs-custom-header">
        <div class="header-text">打包件信息</div>
        <div class="header-btns">
          <el-button
            :loading="btnLoading"
            type="primary"
            :disabled="isClicked || form.Stock_Status > 3"
            @click="save"
          >保存</el-button>
          <el-button @click="tagBack">取消</el-button>
        </div>
      </div>
      <div class="form-search">
        <el-form ref="form" :inline="true" :model="form" label-width="105px" class="demo-form-inline">
          <el-form-item label="打包件类型:">
            <el-select
              ref="StockStatusRef"
              v-model="form.From_Stock_Status"
              placeholder="请选择"
              filterable
              :disabled="true"
            >
              <el-option
                v-for="item in Stock_Status_Data"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="包名称:">
            <el-input v-model="form.PkgNO" :disabled="mode!=1" />
          </el-form-item>
          <template v-if="form.Stock_Status>=3">
            <el-form-item label="仓库:">
              <el-input v-model="form.Warehouse_Name" :disabled="true" />
            </el-form-item>
            <el-form-item label="库位:">
              <el-input v-model="form.Location_Name" :disabled="true" />
            </el-form-item>
          </template>
          <el-form-item label="项目:">
            <el-input v-model="form.ProjectName" :disabled="true" />
          </el-form-item>
          <template v-if="form.Type_Name || mode==1">
            <el-form-item label="构件类型:">
              <el-input v-model="form.Type_Name" :disabled="true" />
            </el-form-item>
          </template>
          <template v-if="form.Volume || mode==1">
            <el-form-item label="体积:">
              <el-input v-model="form.Volume" />
            </el-form-item>
          </template>
          <template v-if="form.DIM || mode==1">
            <el-form-item label="尺寸:">
              <el-input v-model="form.DIM" />
            </el-form-item>
          </template>
          <el-form-item label="毛重系数:">
            <el-input v-model="form.Gross" :disabled="mode!=1" />
          </el-form-item>
          <template v-if="form.Departure || mode==1">
            <el-form-item label="起运港:">
              <el-input v-model="form.Departure" :disabled="mode!=1" />
            </el-form-item>
          </template>
          <template v-if="form.ContractNo || mode==1">
            <el-form-item label="项目合同编号:">
              <el-input v-model="form.ContractNo" :disabled="mode!=1" />
            </el-form-item>
          </template>
          <el-form-item label="收件人:">
            <el-input v-model="form.Addressee" />
          </el-form-item>
          <template v-if="form.Departure || mode==1">
            <el-form-item label="备注:">
              <el-input v-model="form.Remark" :disabled="mode!=1" />
            </el-form-item>
          </template>
          <template v-if="mode==1">
            <el-form-item label="包编号:">
              <el-input v-model="form.PackageSN" :disabled="true" />
            </el-form-item>
          </template>
          <el-form-item label="集装箱编号:">
            <el-input v-model="form.Container_Code" />
          </el-form-item>
          <el-form-item label="封子号:">
            <el-input v-model="form.Fengzi_Code" />
          </el-form-item>
        </el-form>
      </div>
      <div class="assistant-wrapper">
        <div>
          <div class="btn-wrapper">
            <el-button
              :disabled="form.Stock_Status > 3 || (form.Stock_Status == 3 && form.From_Stock_Status === 0)"
              type="primary"
              @click="
                openDialog({
                  title: '添加',
                  component: 'PackingAddDetail',
                  width: '80%',
                  props: {
                    formParams: form,
                    componentIds: tbData.map(v => v.Component_Id).toString()
                  }
                })
              "
            >添加构件</el-button>
            <el-button v-if="mode==1" type="info" :disabled="multiSelected.length <= 0 || form.Stock_Status > 3 || (form.Stock_Status == 3 && form.From_Stock_Status === 0)" @click="releaseComponent">释放</el-button>
            <el-button
              v-else
              :disabled="multiSelected.length <= 0"
              @click="deleteRows"
            >删除构件</el-button>
            <ExportCustomReport name="导出清单" code="package_info" style="margin-left: 10px" :ids="[Id]" />

          </div>
          <div class="total-wrapper">
            <span><strong>合计: </strong>构件打包总数 {{ totalAmount }} 件，</span><span>{{ totalWeight }} {{ Unit }}</span>
          </div>
        </div>
      </div>
      <div class="twrap">
        <div style="height: 100%">
          <vxe-table
            ref="xTable"
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            empty-text="暂无数据"
            height="auto"
            show-overflow
            :loading="tbLoading"
            class="cs-vxe-table"
            align="left"
            stripe
            :data="tbData"
            :checkbox-config="{ checkField: 'checked', checkMethod:checkMethod }"
            resizable
            :edit-config="{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}"
            :tooltip-config="{ enterable: true }"
            @checkbox-all="multiSelectedChange"
            @checkbox-change="multiSelectedChange"
          >
            <vxe-column fixed="left" type="checkbox" width="60" />
            <template v-for="item in columns">
              <vxe-column
                v-if="item.Code === 'SteelAmount'"
                :key="item.Code"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :edit-render="{}"
                :min-width="item.Width"
                :align="item.Align"
              >
                <template #edit="{ row }">
                  <vxe-input
                    v-model.number="row.SteelAmount"
                    type="integer"
                    :min="1"
                    :max="row.Wait_Pack_Num"
                    :disabled="form.Stock_Status > 3"
                    @blur="changeSteelAmount(row, $event)"
                  />
                </template>
                <template #default="{ row }">
                  <div> {{ row.SteelAmount | displayValue }}</div>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Is_Component_Name'"
                :key="item.Code"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :width="item.Width"
                :min-width="item.Width"
                :align="item.Align"
              >
                <template #default="{ row }">
                  <div>
                    <el-tag v-if="row.Is_Component_Name=='直发件'" type="success">是</el-tag>
                    <el-tag v-else type="danger">否</el-tag>
                  </div>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'Near_Pic' || item.Code === 'Far_Pic'"
                :key="item.Code"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :width="item.Width"
                :min-width="item.Width"
                :align="item.Align"
              >
                <template #default="{ row }">
                  <div v-if="row[item.Code]">
                    <el-link type="primary" @click="showImage(row,row[item.Code],item.Code)">
                      {{ row.SteelName }}-{{ item.Code === 'Near_Pic' ? '近照' : '远照' }}
                    </el-link>
                  </div>
                  <span v-else> - </span>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="item.Code === 'SteelName'"
                :key="item.Code"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :width="item.Width"
                :min-width="item.Width"
                :align="item.Align"
              >
                <template #default="{ row }">
                  <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                  {{ row.SteelName }}
                </template>
              </vxe-column>
              <vxe-column
                v-else
                :key="item.Code"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                show-overflow="tooltip"
                sortable
                :field="item.Code"
                :title="item.Display_Name"
                :width="item.Width"
                :min-width="item.Width"
                :align="item.Align"
              >
                <template #default="{ row }">
                  <div> {{ row[item.Code] ? row[item.Code] : '-' }}</div>
                </template>
              </vxe-column>
            </template>
          </vxe-table>
        </div>
      </div>
    </div>
    <el-dialog
      v-dialog-drag
      :title="dialogCfgs.title"
      :visible.sync="dialogShow"
      :width="dialogCfgs.width"
      class="plm-custom-dialog"
      destroy-on-close
    >
      <keep-alive>
        <component
          :is="dialogCfgs.component"
          v-if="dialogShow"
          v-bind="dialogCfgs.props"
          @dialogCancel="dialogCancel"
          @dialogFormSubmitSuccess="dialogFormSubmitSuccess"
        />
      </keep-alive>
    </el-dialog>

    <el-dialog
      v-dialog-drag
      :title="imageTitle"
      :visible.sync="imageShow"
      width="60%"
      class="plm-custom-dialog"
      destroy-on-close
    >
      <div class="image-wrapper">
        <el-image
          class="image-content"
          :src="url"
          fit="contain"
          :preview-src-list="srcList"
        />
        <div class="image-footer">
          <el-button v-if="url" class="download-btn" type="primary" :loading="downloadLoading" @click="handleDownloadImage">下载</el-button>
          <el-button class="close-btn" @click="closeImage">关 闭</el-button>
        </div>
      </div></el-dialog>
  </div>
</template>
<script>
import { GetGridByCode, GetOssUrl } from '@/api/sys'
import { GetComponentTypeList } from '@/api/PRO/component-type'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import PackingAddDetail from './components/PackingAddDetail'
import { GetPacking2ndEntity, SavePacking2nd, UnzipPacking2nd } from '@/api/PRO/packing'
import { FIX_COLUMN, getFactoryProfessional } from './constant.js'
import { closeTagView } from '@/utils'
import { GetStopList } from '@/api/PRO/production-task'
import fa from 'element-ui/src/locale/lang/fa'
import ExportCustomReport from '@/components/ExportCustomReport/index.vue'
export default {
  name: 'PackingAdd',
  components: {
    ExportCustomReport,
    DynamicDataTable,
    PackingAddDetail
  },
  data() {
    return {
      tbLoading: false,
      btnLoading: false,
      isClicked: false,
      confirmed: false,
      downloadLoading: false,
      url: '',
      imageTitle: '',
      srcList: [],
      warehouses: [], // 仓库
      locations: [], // 库位
      imageShow: false,
      tbConfig: {},
      columns: [],
      tbData: [],

      pageInfo: {
        Page: 1,
        TotalCount: 0,
        PageSize: -1,
        PageSizes: [20, 40, 60, 80, 100]
      },

      form: {
        From_Stock_Status: null,
        Stock_Status: null, // 打包类型
        Warehouse_Id: '',
        Location_Id: '',
        ProjectName: '',
        Project_Id: '',
        Sys_Project_Id: '',
        PkgNO: '', // 包名称
        TypeId: '', // 工厂的专业ID
        Type: '', // 构件类型
        ContractNo: '', // 项目合同编号
        Volume: '', // 体积
        DIM: '', // 尺寸
        Gross: null, // 毛重系数
        Departure: '', // 起运港
        Remark: '',
        PackageSN: '',
        Container_Code: '',
        Fengzi_Code: ''
      },
      Stock_Status_Data: [{ Name: '工厂打包', Id: 0 }, { Name: '仓库打包', Id: 3 }, { Name: '仓库打包', Id: 4 }],
      TypeData: [],
      Details: [],

      multiSelected: [],

      dialogShow: false,
      dialogCfgs: {
        component: '',
        title: '',
        width: '50%'
      },
      gridCode: 'pro_packing_detail_list',
      Proportion: 0,
      Unit: '',
      totalAmount: 0,
      totalWeight: 0,

      Id: ''
    }
  },
  computed: {
    // 1 为编辑模式，否则新增模式
    mode() {
      return this.$router.currentRoute.query.mode
    }
  },
  mounted() {
    this.Id = this.$router.currentRoute.query.Id
    getFactoryProfessional().then(res => {
      this.Proportion = res[0].Proportion
      this.Unit = res[0].Unit
      this.getComponentTypeList()
    })
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.initRouterParams()
    })
  },
  beforeRouteUpdate(to, from, next) {
    this.initRouterParams()
    next()
  },
  // beforeRouteLeave(to, from, next) {
  //   if (this.confirmed) {
  //     next()
  //   } else {
  //     this.$confirm('此操作不会保存已修改但尚未保存的数据，是否离开?', '提示', {
  //       confirmButtonText: '确定',
  //       cancelButtonText: '取消',
  //       type: 'warning'
  //     }).then(() => {
  //       next()
  //     })
  //   }
  // },
  created() {
    this.initRouterParams()
    this.getGridByCode()
  },
  methods: {
    async handleDownloadImage() {
      this.downloadLoading = true
      await this.downloadImage(this.url)
      this.downloadLoading = false
    },
    async downloadImage(url) {
      try {
        const imageUrl = url
        const fileName = this.imageTitle
        console.log(imageUrl, fileName)
        const response = await fetch(imageUrl, {
          method: 'GET',
          headers: {
            'Accept': 'image/*'
          }
        })
        if (!response.ok) {
          throw new Error('图片下载失败')
        }

        const blob = await response.blob()
        console.log(blob)

        const blobUrl = window.URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = blobUrl
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(blobUrl)
      } catch (error) {
        this.$message.error('图片下载失败')
      }
    },
    getOssUrl(fielUrl) {
      return new Promise((resolve, reject) => {
        GetOssUrl({ url: fielUrl, day: 30 }).then((res) => {
          resolve(res.Data)
        })
      })
    },
    closeImage() {
      this.imageShow = false
      this.url = ''
      this.srcList = []
    },
    async showImage(row, url, code) {
      const ossUrl = await this.getOssUrl(url)

      this.imageTitle = `${row.SteelName}-${code === 'Near_Pic' ? '近照' : '远照'}`
      this.url = ossUrl
      this.srcList = [ossUrl]
      this.imageShow = true
    },
    // 构件大类
    getComponentTypeList() {
      GetComponentTypeList({ Level: 1, Category_Id: this.ProfessionalId, Factory_Id: localStorage.getItem('CurReferenceId') }).then(res => {
        if (res.IsSucceed) {
          this.TypeData = res.Data
        }
      })
    },

    getGridByCode() {
      this.tbLoading = true
      GetGridByCode({ Code: this.gridCode }).then(res => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          const list = Data.ColumnList || []
          this.columns = list.filter(v => v.Is_Display).map(item => {
            if (item.Width == 0) {
              item.minWidth = 120
            } else {
              item.minWidth = item.Width
            }
            if (FIX_COLUMN.includes(item.Code)) {
              item.fixed = 'left'
            }
            return item
          })
          this.tbLoading = false
          if (this.mode == 1 && this.Id) { // 如果是编辑
            this.fetchData()
          }
        }
      })
    },

    fetchData() {
      this.tbLoading = true
      GetPacking2ndEntity({ id: this.Id }).then(res => {
        if (res.IsSucceed) {
          this.setGridData(res.Data)
        }
      })
        .catch(console.error)
        .finally(() => {
        // 结束loading
          this.tbLoading = false
        })
    },
    checkMethod({ row }) {
      return !row.stopFlag
    },
    tagBack() {
      this.$confirm('此操作不会保存数据，是否离开?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.confirmed = true
        closeTagView(this.$store, this.$route)
        // const view = this.$route
        // this.$store
        //   .dispatch('tagsView/delView', view)
        //   .then(({ visitedViews }) => {
        //     if (view.path === this.$route.path) {
        //       const latestView = visitedViews.slice(-1)[0]
        //       this.$router.push(latestView.fullPath)
        //     }
        //   })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },

    subWeight() {
      let amount = 0
      let weight = 0
      if (this.tbData.length <= 0) {
        amount = 0
        weight = 0
      } else {
        amount = this.tbData.map(d => {
          d.SteelAmount += amount
          return d
        }).reduce((acc, cur) => {
          return acc + cur.SteelAmount
        }, 0)
        weight = this.tbData.map(d => {
          d.totalW = (d.SteelWeight || 0) * (d.SteelAmount || 0)
          return d
        }).reduce((acc, cur) => {
          return acc + cur.totalW
        }, 0)
      }
      this.totalAmount = amount
      this.totalWeight = Math.round((weight / this.Proportion) * 1000) / 1000
    },

    multiSelectedChange({ checked }) {
      const records = this.$refs.xTable.getCheckboxRecords()
      this.multiSelected = records
    },

    activeCellMethod({ row, column, columnIndex }) {
      return column.field === 'SteelAmount'
    },
    changeSteelAmount(row, value) {
      this.subWeight()
    },

    initRouterParams() {
      if (this.mode != 1) {
        const PackageParams = JSON.parse(sessionStorage.getItem('PackageParams'))
        // this.$route.params
        this.form = Object.assign({}, this.form, PackageParams)
      }
    },

    setGridData(data) {
      this.form = data.Entity
      this.tbData = data.Details
      this.getStopList(this.tbData, 'Import_Detail_Id')
      this.TypeData.forEach(item => {
        if (item.Code == this.form.Type) {
          this.form.Type_Name = item.Name
        }
      })
      this.subWeight()
    },
    async getStopList(list, key) {
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Type: 2// 1：零件，3：部件，2：构件
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },
    openDialog(opts) {
      if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
        opts = {}
      }
      this.dialogCfgs = Object.assign({}, this.dialogCfgs, opts, {})
      this.dialogShow = true
    },

    dialogCancel() {
      this.dialogShow = false
    },

    dialogFormSubmitSuccess({ type, data }) {
      this.dialogCancel()
      switch (type) {
        case 'merge':
          this.mergeEntity(data)
          break
      }
    },

    mergeEntity(data) {
      data.map((item) => {
        item.SteelAmount = item.Wait_Pack_Num
        return item
      })
      this.Details = data
      this.tbData = this.tbData.concat(data)
      this.pageInfo.TotalCount = this.tbData.length
      this.subWeight()
    },

    gridPageChange({ page }) {
      this.pageInfo.Page = Number(page)
      this.fetchData()
    },

    gridSizeChange({ size }) {
      this.tbConfig.Row_Number = Number(size)
      this.pageInfo.PageSize = Number(size)
      this.pageInfo.Page = 1
      this.fetchData()
    },

    handlePageChange({ currentPage, pageSize }) {
      this.pageInfo.Page = currentPage
      this.pageInfo.PageSize = pageSize
      this.fetchData()
    },

    deleteRows() {
      this.multiSelected.forEach(r => {
        const idf = r.Component_Id
        this.tbData = this.tbData.filter(d => {
          return idf !== d.Component_Id
        })
      })
      this.subWeight()
    },

    // 释放
    releaseComponent() {
      if (this.multiSelected.length == this.tbData.length) {
        this.releasePackage()
      } else {
        this.deleteRows()
        this.save()
      }
    },

    // 批量释放
    releasePackage() {
      UnzipPacking2nd({ Ids: this.Id }).then(res => {
        if (res.IsSucceed) {
          this.confirmed = true
          if (res.Message === '') {
            this.$message.success('批量释放成功')
          } else {
            this.$message.success(res.Message)
          }

          closeTagView(this.$store, this.$route)
          // const view = this.$route
          // this.$store
          //   .dispatch('tagsView/delView', view)
          //   .then(({ visitedViews }) => {
          //     if (view.path === this.$route.path) {
          //       const latestView = visitedViews.slice(-1)[0]
          //       this.$router.push(latestView.fullPath)
          //     }
          //   })
        } else {
          this.$message.warning(res.Message || '')
        }
      })
    },

    // 编辑保存
    save() {
      this.isClicked = true
      this.btnLoading = true
      this.form.ProjectID = this.form.Sys_Project_Id
      SavePacking2nd({ Entity: this.form, Details: this.tbData }).then(res => {
        if (res.IsSucceed) {
          this.confirmed = true
          this.$message.success(res.Message || '')
          closeTagView(this.$store, this.$route)
          // const view = this.$route
          // this.$store
          //   .dispatch('tagsView/delView', view)
          //   .then(({ visitedViews }) => {
          //     if (view.path === this.$route.path) {
          //       const latestView = visitedViews.slice(-1)[0]
          //       this.$router.push(latestView.fullPath)
          //     }
          //   })
        } else {
          this.isClicked = false
          this.btnLoading = false
          this.$message.warning(res.Message || '')
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: res.Message
        })
        this.btnLoading = false
      })
    },

    formatSaveData() {
      const data = { entity: {}, details: [] }
      Object.keys(this.entity).forEach(k => {
        if (k !== 'details') {
          data.entity[k] = this.entity[k]
        }
      })
      data.details = this.entity.details
      return data
    }
  }
}
</script>
<style lang="scss" scoped>
.sch-detail {
  height: calc(100% - 46px);
  padding: 16px 16px 16px 16px;
  background: #fff;
  display: flex;
  flex-direction: column;
  .cs-custom-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .header-text {
      line-height: 30px;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .assistant-wrapper {
    border-top: 1px solid #D8D8D8;
    margin-top: 6px;
    & > div {
      position: relative;
      height: 30px;
      margin-top: 16px;
      margin-bottom: 16px;
      .btn-wrapper {
        position: absolute;
        left: 0;
      }
      .total-wrapper {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        height: 30px;
        line-height: 30px;
        padding: 0 15px;
        background-color: rgba(41, 141, 255, 0.05);
        border-radius: 4px;
        font-size: 14px;
        color: #298DFF;
        text-align: center;
        span {
            margin-left: 5px;
            margin-right: 5px;
        }
      }
    }
  }

  .twrap {
    flex: 1 1 auto;
    height: 35vh;
  }
}

::v-deep .vxe-pager.is--perfect {
  border: 0;
}

::v-deep .el-form-item--small.el-form-item {
  margin-bottom: 18px;
}
::v-deep .el-form-item {
  .el-form-item__content {
    & > .el-input {
      width: 240px;
      padding-left: 0;
    }
    & > .el-select {
      width: 240px;
    }
    & > .el-textarea {
      width: 240px;
    }
    /**
    .el-input.is-disabled .el-input__inner, .el-textarea.is-disabled .el-textarea__inner {
      background-color: #FFFFFF;
      border: 1px solid #FFFFFF;
      color: #666666;
      cursor: default;
      padding-left: 0;
    }
     */
  }
}
.plm-custom-dialog{
  ::v-deep .el-dialog__body {
     height: 70vh;
  }
}
.image-wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .image-content{
    flex: 1;
  }
  .image-footer{
    position: relative;
    margin-top: 10px;
    height: 40px;
    width: 100%;
    .download-btn{
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
    }
    .close-btn{
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
}

</style>

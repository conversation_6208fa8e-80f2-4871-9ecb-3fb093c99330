<template>
  <div class="abs100 flex-pd16-wrap">
    <div class="page-main-content cs-z-shadow">
      <div ref="searchDom" class="form-search">
        <el-form ref="form" :model="form" inline label-width="80px">
          <el-form-item label="包编号" prop="PackageSnFormat">
            <el-input v-model="form.PackageSnFormat" placeholder="请输入（空格间隔筛选多个）" clearable />
          </el-form-item>
          <el-form-item label="项目名称" prop="Project_Id">
            <el-select
              ref="ProjectName"
              v-model="form.Project_Id"
              filterable
              placeholder="请选择"
              @change="projectChangeSingle"
            >
              <el-option
                v-for="item in ProjectNameData"
                :key="item.Id"
                :label="item.Short_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="打包类型" prop="From_Stock_Status">
            <el-select
              v-model="form.From_Stock_Status"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in Stock_Status_Data"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="assistant-wrapper">
        <div class="btn-wrapper">
          <!-- <el-button
              size="small"
              type="primary"
              @click="importPackage()"
            >导入打包</el-button>-->
          <el-button
            size="small"
            type="success"
            :loading="exportLoading"
            @click="exportPackage()"
          >导出打包件</el-button>
          <el-button
            size="small"
            type="primary"
            @click="newPack(2)"
          >打包</el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="!multiSelected.length"
            @click="releasePackage()"
          >批量释放</el-button>
          <ExportCustomReport name="导出清单" code="package_info" style="margin-left: 10px" :ids="multiSelected.map(i=>i.Id)" />
        </div>
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="form.SearchDate"
            type="daterange"
            :clearable="true"
            align="right"
            value-format="yyyy-MM-dd"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="dateChange()"
          />
        </div>
      </div>
      <div class="no-v-padding flex-1">
        <vxe-table
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          empty-text="暂无数据"
          height="auto"
          show-overflow
          :loading="loading"
          class="cs-vxe-table"
          align="left"
          stripe
          :data="tbData"
          resizable
          :edit-config="{trigger: 'click', mode: 'cell'}"
          :tooltip-config="{ enterable: true }"
          @checkbox-all="multiSelectedChange"
          @checkbox-change="multiSelectedChange"
        >
          <vxe-column fixed="left" type="checkbox" width="60" />
          <template v-for="(item, index) in columns">
            <vxe-column
              v-if="item.Code==='Create_Date'"
              :key="index"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :min-width="item.Width"
              :align="item.Align"
            >
              <template #default="{ row }">
                {{ row.Create_Date | timeFormat | displayValue }}
              </template>
            </vxe-column>

            <vxe-column
              v-else
              :key="item.Code"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :min-width="item.Width"
              :align="item.Align"
            />
          </template>
          <vxe-column fixed="right" title="操作" width="80">
            <template #default="{ row }">
              <el-button
                type="text"
                @click="
                  openPageTab('PROPackingEdit', 'Edit', {
                    query: {
                      mode: 1,
                      Id: row.Id
                    },
                    params: row
                  })
                "
              >编辑</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <Pagination
        :total="total"
        :page-sizes="tablePageSize"
        :page.sync="queryInfo.Page"
        :limit.sync="queryInfo.PageSize"
        class="pagination"
        @pagination="pageChange"
      />
    </div>
    <el-dialog
      v-dialogDrag
      :title="dialogCfgs.title"
      :visible.sync="dialogShow"
      :width="dialogCfgs.width"
      class="plm-custom-dialog"
      destroy-on-close
    >
      <keep-alive>
        <component
          :is="dialogCfgs.component"
          v-if="dialogShow"
          v-bind="dialogCfgs.props"
          @dialogCancel="dialogCancel"
          @dialogFormSubmitSuccess="dialogFormSubmitSuccess"
        />
      </keep-alive>
    </el-dialog>
  </div>
</template>
<script>
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'

import { GetPacking2ndPageList, UnzipPacking2nd, ExportPackingList } from '@/api/PRO/packing'

import { UnzipPacking } from '@/api/PRO/pro-stock'
import AddPackDialog from './components/AddPackDialog'
import PackingDelBox from './components/Delbox'
import PrintDialog from '../../Component/PrintDialog/index'
// import { handleAddRouterPage } from '@/utils'
import getProjectAreaUnit from './mixins/mixinsProject.js'
import { addSearchLog, getQueryParam } from '@/mixins/warehouse'
import { tablePageSize } from '@/views/PRO/setting'
import getTbInfo from '@/mixins/PRO/get-table-info'
import Pagination from '@/components/Pagination/index.vue'
import addRouterPage from '@/mixins/add-router-page/index'
import { combineURL } from '@/utils'
import ExportCustomReport from '@/components/ExportCustomReport/index.vue'

export default {
  components: {
    ExportCustomReport,
    DynamicDataTable,
    PackingDelBox,
    AddPackDialog,
    PrintDialog,
    Pagination
  },
  // name: 'PROSteelPack',
  mixins: [getProjectAreaUnit, getTbInfo, addRouterPage],
  data() {
    return {
      searchHeight: 0,
      form: {
        ProjectName: '',
        Project_Id: '',
        Sys_Project_Id: '',
        From_Stock_Status: null, // 打包类型
        PackageSnFormat: '',
        PackageSn: '',
        StartDate: '',
        EndDate: '',
        SearchDate: []
      },
      Stock_Status_Data: [{ Name: '工厂打包', Id: 0 }, { Name: '仓库打包', Id: 3 }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      addPageArray: [
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () => import('./detail.vue'),
          name: 'PROPackingDetail',
          meta: { title: '打包件详情' }
        },
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'PROPackingAdd',
          meta: { title: '新增打包件' }
        },
        {
          path: this.$route.path + '/edit',
          hidden: true,
          component: () => import('./add.vue'),
          name: 'PROPackingEdit',
          meta: { title: '编辑打包件' }
        }
      ],
      gridCode: 'pro_packing_list',
      loading: false,
      tablePageSize: tablePageSize,
      tbConfig: {},
      columns: [],
      tbData: [],
      total: 0,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      dialogShow: false,
      dialogCfgs: {
        component: '',
        title: '',
        width: '360px'
      },
      multiSelected: [],
      exportLoading: false
    }
  },
  mounted() {
    this.searchHeight = this.$refs.searchDom.offsetHeight + 200
  },

  async created() {
    const obj = await getQueryParam(['ProjectName', 'Project_Id', 'Sys_Project_Id'])
    this.form.Project_Id = obj.Project_Id
    this.form.Sys_Project_Id = obj.Sys_Project_Id
    this.form.ProjectName = obj.ProjectName
    await this.getTableConfig(this.gridCode)
    this.fetchData(1)
  },
  methods: {
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      addSearchLog(this.form)
      GetPacking2ndPageList({ ...this.form, ...this.queryInfo }).then(res => {
        if (res.IsSucceed) {
          this.setGridData(res.Data)
        }
      })
        .catch(console.error)
        .finally(() => {
        // 结束loading
          this.loading = false
        })
    },

    setGridData(data) {
      this.tbData = data.Data
      this.total = data.TotalCount
    },

    resetSearch() {
      this.$refs['form'].resetFields()
      this.form.ProjectName = ''
      this.form.Sys_Project_Id = ''
      this.handleSearch()
    },

    // 搜索
    handleSearch() {
      let PackageSn = (this.form.PackageSnFormat).trim()
      PackageSn = PackageSn.replace(/\s+/g, '\n')
      this.form.PackageSn = PackageSn
      this.queryInfo.Page = 1
      this.fetchData(1)
    },

    openPageTab(CmptName, Type, { params, query }) {
      // this.addPageArray.push(
      //   {
      //     path: this.$route.path + '/add',
      //     hidden: true,
      //     component: () => import('./add.vue'),
      //     name: 'PROPackingAdd',
      //     meta: { title: Type === 'Add' ? '新增打包件' : '编辑打包件' }
      //   }
      // )
      // this.addPageArray[1].meta.title = Type === 'Add' ? '新增打包件' : '编辑打包件'

      // this.initPage(this.$route.name)
      sessionStorage.setItem('PackageParams', JSON.stringify(params))
      this.$router.push({
        name: CmptName,
        query: Object.assign({}, query, {
          pg_redirect: this.$route.name
        }),
        params
      })
    },

    // 改变日期搜索
    dateChange() {
      if (this.form.SearchDate) {
        this.form.StartDate = this.form.SearchDate[0]
        this.form.EndDate = this.form.SearchDate[1]
      } else {
        this.form.StartDate = ''
        this.form.EndDate = ''
      }
      this.fetchData(1)
    },

    newPack(type) {
      this.openDialog({
        title: '打包数据',
        width: '860px',
        component: 'AddPackDialog',
        props: {}
      })
    },

    // 批量释放
    releasePackage() {
      UnzipPacking2nd({ Ids: this.multiSelected.map(v => v.Id).toString() }).then(res => {
        if (res.IsSucceed) {
          if (res.Message === '') {
            this.$message.success('批量释放成功')
          } else {
            this.$message.success(res.Message)
          }
          this.fetchData(1)
        } else {
          this.$message.warning(res.Message || '')
        }
      })
    },

    // 导入打包
    importPackage() {
      this.$message.warning('国庆后开发')
    },

    // 导出打包件
    exportPackage() {
      const submitObj = {
        ...this.form,
        ...this.queryInfo
      }
      if (this.multiSelected.length) {
        submitObj.Ids = this.multiSelected.map(v => v.Id).toString()
      } else {
        submitObj.Ids = ''
      }
      this.exportLoading = true
      ExportPackingList(submitObj).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
        .finally(() => {
        // 结束loading
          this.exportLoading = false
        })
    },

    openDialog(opts) {
      if (!opts || Object.prototype.toString.call(opts) !== '[object Object]') {
        opts = {}
      }
      this.dialogCfgs = Object.assign({}, this.dialogCfgs, opts, {})
      this.dialogShow = true
    },

    dialogCancel() {
      this.dialogShow = false
    },

    dialogFormSubmitSuccess({ form }) {
      this.dialogCancel()
      // return
      this.openPageTab('PROPackingAdd', 'Add', {
        query: {},
        params: form
      })
    },

    deleteRow(row) {
      // 直发'已入库不允许删除
      // 直发待入库，删除，不用指定库位
      // 构件已入库，删除，指定库位
      // 构件，待入库，删除，不用指定库位
      if (row.Stock_Status_Name !== '待入库' && !row.Is_Component) return
      if (row.Stock_Status_Name === '待入库') {
        this.$confirm('此操作将删除当前包, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            UnzipPacking({
              id: row.Id,
              locationId: row.Location_Id
            }).then(res => {
              if (res.IsSucceed) {
                this.$message.success(res.Message || '')
                this.data = this.data.filter(d => d.Id !== row.Id)
              } else {
                this.$message.warning(res.Message || '')
              }
            })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      } else if (row.Stock_Status_Name === '已入库') {
        this.openDialog({
          title: '删除',
          width: '420px',
          component: 'PackingDelBox',
          props: {
            row: row,
            warehouses: this.warehouses
          }
        })
      }
    },

    multiSelectedChange(arr) {
      this.multiSelected = arr.records
    }
  }
}
</script>

<style lang="scss" scoped>
  .form-search {
    width: 100%;
    margin-bottom: 16px;
    border-bottom: 1px solid #EEEEEE;

    ::v-deep .el-form-item {
      .el-form-item__content {
        & > .el-input {
          width: 220px;
        }
        & > .el-select {
          width: 220px;
        }
        .el-tree-select-input{
          width: 220px !important;
        }
      }
    }
  }

  .assistant-wrapper {
    height: 30px;
    margin-bottom: 16px;
    position: relative;
    .btn-wrapper {
      position: absolute;
      left: 0;
      display: flex;
      justify-content: flex-start;
      .change-total-wrapper {
        height: 30px;
        line-height: 30px;
        margin-left: 16px;
        background-color: rgba(0, 195, 97, 0.05);
        border-radius: 4px;
        font-size: 14px;
        color: #00C361;
        text-align: center;
        span {
          margin-left: 15px;
          margin-right: 15px;
        }
      }
    }
    .total-wrapper {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      height: 30px;
      line-height: 30px;
      background-color: rgba(41, 141, 255, 0.05);
      border-radius: 4px;
      font-size: 14px;
      color: #298DFF;
      text-align: center;
      span {
          margin-left: 15px;
          margin-right: 15px;
      }
    }
    .date-picker-wrapper {
      position: absolute;
      right: 0;
    }
  }

.flex-pd16-wrap {
  display: flex;
  flex-direction: column;
  flex-flow: column;
  padding: 16px;
}
.page-main-content {
  background: #fff;
  padding: 16px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .flex-1 {
    flex: 1;
  }
  .pagination {
    text-align: right;
    padding: 16px 16px 0 16px;
    margin: 0;
  }
}

</style>

<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content" style="padding: 0">
      <top-header style="height: auto; border-bottom:16px solid #f3f4f6" padding="16px 20px 0 20px">
        <template #left>
          <el-form
            ref="form"
            class="cs-form"
            :model="form"
            inline
          >
            <el-form-item label="日期选择" prop="Begin_Date">
              <el-date-picker
                v-model="planTime"
                value-format="yyyy-MM-dd"
                style="width:300px"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item label="项目名称" prop="Sys_Project_Id">
              <el-select
                v-model="form.Sys_Project_Id"
                filterable
                clearable
                placeholder="请选择"
              >
                <template>
                  <el-option
                    v-for="item in ProjectNameData"
                    :key="item.Id"
                    :label="item.Short_Name"
                    :value="item.Sys_Project_Id"
                  />
                </template>
              </el-select>
            </el-form-item>
            <el-form-item label="采购计划单" prop="Purchase_Plan_No">
              <el-input
                v-model="form.Purchase_Plan_No"
                clearable
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="项目编号" prop="Project_Code">
              <el-input
                v-model="form.Project_Code"
                clearable
                placeholder="请输入"
              />
            </el-form-item>
            <el-form-item label="计划类型" prop="Plan_Category">
              <el-select
                v-model="form.Plan_Category"
                clearable
                placeholder="请选择"
              >
                <el-option label="自采" :value="1 " />
                <el-option label="甲供" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目状态" prop="Project_Status">
              <el-select v-model="form.Project_Status" placeholder="请选择" clearable="" multiple>
                <el-option
                  v-for="item in projectStatusOptions"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="
                  getTotalInfo()
                  fetchData(1)
                "
              >搜 索</el-button>
              <el-button
                @click="
                  resetForm();
                  getTotalInfo()
                  fetchData(1);
                "
              >重 置
              </el-button>
              <el-button
                type="success"
                :loading="exportIng"
                @click=" handleExport() "
              >导 出
              </el-button>
            </el-form-item>
          </el-form>
        </template>
      </top-header>
      <div class="cs-z-tb-wrapper fff">
        <div class="item-x">
          <ItemInfo
            v-for="item in itemInfos"
            :key="item.title"
            :info="item"
            :is-raw="isRaw"
          />
        </div>
        <div class="tb-wrapper">
          <vxe-table
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            :loading="tbLoading"
            element-loading-spinner="el-icon-loading"
            element-loading-text="拼命加载中"
            empty-text="暂无数据"
            class="cs-vxe-table"
            height="100%"
            align="left"
            stripe
            :data="tbData"
            resizable
            :tooltip-config="{ enterable: true}"
          >
            <vxe-column v-if="tbConfig.Is_Row_Number" type="seq" width="60" title="序号" fixed="left" align="center" />
            <template v-for="item in columns">
              <vxe-column
                :key="item.Code"
                :min-width="item.Width"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                show-overflow="tooltip"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
              >
                <template v-if="['Plan_Weight','Order_Weight','In_Weight','Pick_Weight','Return_Weight','Goods_Weight','Store_Weight'].includes(item.Code)" #default="{ row }">
                  <el-link v-if="isRaw" type="primary" @click.stop="showScheduleDialog(item, row)">{{ row[item.Code] ? (row[item.Code] / 1000).toFixed(3) : 0 }}</el-link>
                  <el-link v-else type="primary" @click.stop="showScheduleDialog(item, row)"> {{ row[item.Code] | displayValue }}</el-link>
                </template>
                <template v-else-if="['Order_Money','In_Money','Pick_Money','Return_Money','Goods_Money','Store_Money'].includes(item.Code)" #default="{ row }">
                  <span type="primary" @click.stop="showScheduleDialog(item, row)">{{ row[item.Code] ? row[item.Code].toFixed(2) : 0 }}</span>
                </template>
                <template v-else-if="item.Code === 'Project_Name'" #default="{ row }">
                  <el-link type="primary" @click.prevent="handleProject(row)"> {{ row[item.Code] | displayValue }}</el-link>
                </template>
                <template v-else #default="{ row }">
                  <span>{{ row[item.Code] || 0 }}</span>
                </template>
              </vxe-column>

            </template>
          </vxe-table>
        </div>
        <Pagination
          :total="total"
          max-height="100%"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="pageChange"
        />
      </div>

      <el-dialog
        v-if="dialogVisible"
        v-dialogDrag
        class="plm-custom-dialog"
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        :width="dialogWidth"
        top="10vh"
        @close="handleClose"
      >
        <component
          :is="currentComponent"
          ref="content"
          :dialog-type="dialogType"
          :dialog-sys-project-id="dialogSysProjectId"
          :form-data="form"
          :is-raw="isRaw"
          @close="handleClose"
          @refresh="fetchData(1)"
        />
      </el-dialog>

    </div>
  </div>
</template>

<script>
import TopHeader from '@/components/TopHeader/index.vue'
import { GetProjectPageList } from '@/api/PRO/project'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { timeFormat } from '@/filters'
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import ItemInfo from './components/ItemInfo.vue'
import ScheduleDialog from './components/ScheduleDialog.vue'
import {
  ExportProjectRawAnalyse,
  FindProjectRawAnalyse,
  GetProjectRawAnalyseSum
} from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'
import addRouterPage from '@/mixins/add-router-page'
import { combineURL } from '@/utils'
import { getDictionary } from '@/utils/common'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'
import { GetGridByCode } from '@/api/sys'

export default {
  name: 'PRORawProjectAnalysis',
  components: { Pagination, TopHeader, ItemInfo, ScheduleDialog },
  mixins: [getTbInfo, addRouterPage],
  data() {
    return {
      addPageArray: [
        {
          path: this.$route.path + '/RawProjectDetail',
          hidden: true,
          component: () =>
            import(
              '@/views/PRO/material-inventory-reconfig/raw-project-analysis/detail.vue'
            ),
          name: 'PRORawProjectAnalysisDetail',
          meta: { title: '单项目原料分析' }
        },
        {
          path: this.$route.path + '/auxProjectDetail',
          hidden: true,
          component: () =>
            import(
              '@/views/PRO/material-inventory-reconfig/raw-project-analysis/detail.vue'
            ),
          name: 'PROAuxProjectAnalysisDetail',
          meta: { title: '单项目辅料分析' }
        }
      ],
      exportIng: false,
      dialogVisible: false,
      currentComponent: '',
      dialogTitle: '',
      dialogType: '',
      dialogWidth: '60%',
      dialogSysProjectId: '',
      tablePageSize: tablePageSize,
      itemInfos: [{
        title: '深化量',
        icon: 'raw-project-analysis1',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '采购计划量',
        icon: 'raw-project-analysis1',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '采购订单量',
        icon: 'raw-project-analysis2',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '入库量',
        icon: 'raw-project-analysis3',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '领用量',
        icon: 'raw-project-analysis4',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '退库量',
        icon: 'raw-project-analysis5',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '退货量',
        icon: 'raw-project-analysis6',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '项目锁定在库量',
        icon: 'raw-project-analysis7',
        num1: 0,
        num2: 0,
        Money: 0
      }, {
        title: '公共在库量',
        icon: 'raw-project-analysis8',
        num1: 0,
        num2: 0,
        Money: 0
      }],
      form: {
        Sys_Project_Id: '',
        Purchase_Plan_No: '',
        Plan_Category: '',
        Begin_Date: '',
        End_Date: '',
        Project_Code: '',
        Project_Status: [],
        MaterialType: ''
      },
      queryInfo: {
        Page: 1,
        PageSize: 20
      },
      ProjectNameData: [], // 项目名称数据
      tbData: [],
      columns: [],
      tbLoading: false,
      tbConfig: {},
      total: 0,
      projectStatusOptions: []
    }
  },
  computed: {
    planTime: {
      get() {
        return [timeFormat(this.form.Begin_Date), timeFormat(this.form.End_Date)]
      },
      set(v) {
        if (!v) {
          this.form.Begin_Date = ''
          this.form.End_Date = ''
        } else {
          const start = v[0]
          const end = v[1]
          this.form.Begin_Date = timeFormat(start)
          this.form.End_Date = timeFormat(end)
        }
      }
    },
    isRaw() {
      return this.$route.name === 'PRORawProjectAnalysis'
    }
  },
  async created() {
    this.form.MaterialType = this.$route.name === 'PRORawProjectAnalysis' ? 0 : 1
    if (!this.isRaw) {
      this.itemInfos.shift()
    }
    this.getProjectStatusOptions()
    await this.getTableConfig('PRORawProjectAnalysisPageList')
    await this.getTotalInfo()
    await this.fetchData(1)
    await this.getProjectOption()
  },
  methods: {
    getTableConfig(code) {
      return new Promise((resolve) => {
        GetGridByCode({
          code
        }).then(res => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message({
                message: '表格配置不存在',
                type: 'error'
              })
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            this.columns = (Data.ColumnList.filter(v => v.Is_Display) || []).map(item => {
              item.Is_Resizable = true
              item.Is_Sortable = true
              return item
            })
            if (!this.isRaw) {
              this.columns = this.columns.map(item => {
                item.Display_Name = item.Display_Name.replace('(t)', '')
                return item
              })
            }
            // this.queryInfo.PageSize = tablePageSize[0]
            if (this.queryInfo) {
              this.queryInfo.PageSize = +Data.Grid.Row_Number || tablePageSize[0]
            }
            resolve(this.columns)
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    async getProjectStatusOptions() {
      // 判断是否为品重租户
      GetPreferenceSettingValue({ code: 'Productweight' }).then((res) => {
        // 项目状态
        getDictionary(res.Data === 'true' ? 'Engineering Status' : 'project_status').then(res => {
          this.projectStatusOptions = res
        })
      })
    },
    handleProject(row) {
      this.$router.push({
        name: this.isRaw ? 'PRORawProjectAnalysisDetail' : 'PROAuxProjectAnalysisDetail',
        query: { pg_redirect: this.$route.name, id: row.Sys_Project_Id, t: this.form.Plan_Category, Material_Utilization_Rate: row.Material_Utilization_Rate, materialType: this.form.MaterialType }
      })
    },
    getTotalInfo() {
      GetProjectRawAnalyseSum({
        ...this.form
      }).then(res => {
        if (res.IsSucceed) {
          if (!this.isRaw) {
            res.Data.shift()
          }
          this.itemInfos.forEach((v, i) => {
            v.num1 = res.Data[i].Count
            v.num2 = res.Data[i].Weight
            v.Money = res.Data[i].Money
            v.showMoney = i > 1
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleExport() {
      this.exportIng = true
      ExportProjectRawAnalyse({
        ...this.queryInfo,
        ...this.form
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.exportIng = false
      })
    },
    pageChange({ page, limit }) {
      this.fetchData()
    },
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      this.tbLoading = true
      FindProjectRawAnalyse({
        ...this.queryInfo,
        ...this.form
      }).then(res => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map(v => {
            return v
          })
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
    },
    // 获取项目
    getProjectOption() {
      GetProjectPageList({
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProjectNameData = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    resetForm() {
      this.$refs['form'].resetFields()
      this.planTime = ''
    },
    handleClose() {
      this.dialogVisible = false
    },
    showScheduleDialog(item, row) {
      this.dialogTitle = item.Display_Name
      this.dialogSysProjectId = row.Sys_Project_Id
      switch (item.Code) {
        case 'Plan_Weight':
          this.dialogType = 1
          break
        case 'Order_Weight':
          this.dialogType = 2
          break
        case 'In_Weight':
          this.dialogType = 3
          break
        case 'Pick_Weight':
          this.dialogType = 4
          break
        case 'Return_Weight':
          this.dialogType = 5
          break
        case 'Goods_Weight':
          this.dialogType = 6
          break
        case 'Store_Weight':
          this.dialogType = 7
          break
      }
      this.currentComponent = 'ScheduleDialog'
      this.dialogWidth = '60%'
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";

.cs-top-header-box {
  line-height: unset!important;
}

.cs-form{
  ::v-deep {
    //.el-form-item{
    //  margin-bottom: 0;
    //}
  }
}

.cs-z-tb-wrapper {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  .item-x {
    display: flex;
    flex-wrap: nowrap;
    overflow: auto;
    @include scrollBar;
    margin-bottom: 8px;
    padding-bottom: 8px;

    // margin-top: 24px;
  }

  .tb-wrapper {
    flex: 1;
    height: 0;
  }

  .cs-red {
    color: #FC6B7F
  }

  .cs-green {
    color: #00C361
  }

  .pagination-container {
    text-align: right;
    padding: 16px;
    margin: 0;
  }
}
</style>

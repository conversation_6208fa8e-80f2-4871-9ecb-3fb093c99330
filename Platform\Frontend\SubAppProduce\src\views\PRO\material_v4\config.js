
module.exports = {
  WEIGHT_DECIMAL: 5, // 重量(t)保留小数位数
  INBOUND_DETAIL_UNIT_PRICE_DECIMAL: 6, // 入库明细单价保留位数
  DETAIL_TOTAL_PRICE_DECIMAL: 2, // 明细含税/不含税总价保留小数位数
  COUNT_DECIMAL: 2, // 出库时,数量允许的最大精度
  UNIT_WEIGHT_DECIMAL: 100, // 单重的计算精度，（比重*长度*宽度*厚度/1000）的小数位数
  TAX_MODE: 0, // 0-不含税推导出含税，1-含税推导出不含税
  // 入库明细中，需要汇总的字段
  SUMMARY_FIELDS: ['Theory_Weight', 'InStoreWeight', 'Voucher_Weight', 'InStoreCount', 'TaxTotalPrice', 'NoTaxTotalPrice', 'TaxPrice'],
  // 入库明细中，需要隐藏的字段
  INBOUND_DETAIL_HIDE_FIELDS: {
    isPurchase: ['PartyUnitName'],
    isCustomer: ['PurchaseNo', 'SupplierName', 'OrderTaxUnitPrice', 'OrderNoTaxUnitPrice'],
    isManual: ['PurchaseNo', 'PartyUnitName', 'OrderTaxUnitPrice', 'OrderNoTaxUnitPrice']
  },
  // 入库时，采购订单区别于甲供和手动，需要禁止编辑的字段
  INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS: ['SupplierName', 'Material', 'Tax_Rate', 'NoTaxUnitPrice', 'TaxUnitPrice'],
  // 入库明细需要合计的字段
  INBOUND_DETAIL_SUMMARY_FIELDS: ['InStoreCount', 'InStoreWeight', 'Voucher_Weight', 'NoTaxAllPrice', 'Tax_All_Price', 'Adjust_Amount', 'Tax', 'Theory_Weight'],
  // 出库/退库明细需要合计的字段
  OutBOUND_DETAIL_SUMMARY_FIELDS: ['OutStoreCount', 'OutStoreWeight', 'AvailableCount', 'AvailableWeight', 'NoTaxAllPrice', 'Tax_All_Price', 'Out_Store_Weight', 'Out_Store_Count', 'Returned_Weight', 'Returned_Count', 'InStoreCount', 'InStoreWeight'],
  // 原料退货明细需要合计的字段
  Return_DETAIL_SUMMARY_FIELDS: ['OutStoreCount', 'OutStoreWeight', 'NoTaxAllPrice', 'Tax_All_Price', 'AvailableCount', 'AvailableWeight', 'Voucher_Weight']
}

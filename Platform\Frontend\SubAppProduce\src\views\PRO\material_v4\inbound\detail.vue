<template>
  <div class="app-container abs100">
    <el-card v-if="!isAdjustAmount" class="box-card" :style="isRetract ? 'height: 110px; overflow: hidden;' : ''">
      <div
        class="toolbar-container"
        style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #d0d3db"
      >
        <div class="toolbar-title"><span />入库单信息</div>
        <div class="retract-container" @click="handleRetract">
          <el-button type="text">{{ isRetract ? '展开' : '收起' }}</el-button>
          <el-button type="text" :icon="isRetract ? 'el-icon-arrow-down' : 'el-icon-arrow-up'" />
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="入库类型" prop="InStoreType">
              <SelectMaterialStoreType v-model="form.InStoreType" :disabled="isView || isEdit || isReturn" type="RawInStoreType" @change="typeChange" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="入库日期" prop="InStoreDate">
              <el-date-picker
                v-model="form.InStoreDate"
                :disabled="isView || formStatus === 3|| isReturn"
                :picker-options="{
                  disabledDate: time => time.getTime() < new Date(statisticTime).getTime()
                }"
                value-format="yyyy-MM-dd"
                style="width: 100%"
                type="date"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="送货单编号" prop="Delivery_No">
              <el-input
                v-model="form.Delivery_No"
                :disabled="isView || isReturn"
                style="width: 100%"
                placeholder="送货单编号"
                type="text"
                clearable
                @change="changeFormKey('Delivery_No')"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.InStoreType == 1" :span="6">
            <el-form-item label="采购合同编号" prop="Purchase_Contract_No">
              <el-input
                ref="elInput"
                v-model="form.Purchase_Contract_No"
                :disabled="isView || formStatus === 3|| isReturn"
                style="width: 100%"
                placeholder="采购合同编号"
                type="text"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否代购" prop="Is_Replace_Purchase">
              <el-select
                v-model="form.Is_Replace_Purchase"
                :disabled="isView || formStatus === 3|| isReturn"
                placeholder="请选择"
                style="width: 100%"
                @change="isReplacePurchaseChange"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="form.Is_Replace_Purchase" :span="6">
            <el-form-item label="所属项目" prop="ProjectId">
              <el-select
                v-model="form.ProjectId"
                style="width: 100%"
                placeholder="所属项目"
                clearable
                filterable
                :disabled="isView || formStatus === 3|| isReturn"
                @change="projectChange"
              >
                <el-option
                  v-for="item in ProjectList"
                  :key="item.Id"
                  :label="item.Short_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            v-if="(form.InStoreType == 1 || form.InStoreType == 3) && form.Is_Replace_Purchase"
            :span="6"
          >
            <el-form-item label="供应商" prop="Supplier">
              <el-select
                v-model="form.Supplier"
                style="width: 100%"
                placeholder="供应商"
                clearable
                filterable
                :disabled="isView || formStatus === 3|| isReturn"
                @change="supplierChange"
              >
                <el-option
                  v-for="item in SupplierList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="form.InStoreType == 2 && form.Is_Replace_Purchase" :span="6">
            <el-form-item label="甲方单位" prop="PartyUnit">
              <el-select
                v-model="form.PartyUnit"
                style="width: 100%"
                placeholder="甲方单位"
                clearable
                filterable
                :disabled="isView || formStatus === 3|| isReturn"
                @change="partyUnitChange"
              >
                <el-option
                  v-for="item in PartyUnitList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="isRaw" :span="6">
            <el-form-item label="计量方式" prop="Weight_Method">
              <el-select
                v-model="form.Weight_Method"
                :disabled="isView || isReturn"
                style="width: 100%"
                @change="(val)=>setFormLocal('Weight_Method',val)"
              >
                <el-option label="理重" :value="0" />
                <el-option label="凭证重" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="车牌号" prop="CarNumber">
              <el-input
                v-model="form.CarNumber"
                :disabled="isView || isReturn"
                style="width: 100%"
                placeholder="车牌号"
                type="text"
                clearable
                @change="changeFormKey('CarNumber')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="司机信息" prop="Driver">
              <el-input
                v-model="form.Driver"
                :disabled="isView || isReturn"
                style="width: 35%; margin-right: 10px"
                placeholder="姓名"
                type="text"
                clearable
                @change="changeFormKey('Driver')"
              />
              <el-input
                v-model="form.DriverMobile"
                :disabled="isView || isReturn"
                style="width: calc(100% - 35% - 10px)"
                placeholder="手机号"
                type="text"
                clearable
                @change="changeFormKey('DriverMobile')"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="isRaw" :span="6">
            <el-form-item label="整车磅重(t)" prop="Car_Pound_Weight">
              <el-input
                v-model="form.Car_Pound_Weight"
                v-inp-num="{ toFixed: weightDecimal, min: 0 }"
                :disabled="isView || isReturn"
                style="width: 100%"
                placeholder="整车磅重"
                class="input-number"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="isRaw" :span="6">
            <el-form-item label="整车凭证重(t)" prop="Car_Voucher_Weight">
              <el-input
                v-model="form.Car_Voucher_Weight"
                v-inp-num="{ toFixed: weightDecimal, min: 0 }"
                :disabled="isView || isReturn"
                style="width: 100%"
                placeholder="整车凭证重"
                class="input-number"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否开票" prop="Has_Invoice">
              <el-select
                v-model="form.Has_Invoice"
                :disabled="(isView || isReturn) && !isInvoices"
                style="width: 100%"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="form.Has_Invoice">
            <el-col :span="6">
              <el-form-item
                label="发票号码"
                prop="Invoice_No"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' },
                ]"
              >
                <el-input
                  v-model="form.Invoice_No"
                  :disabled="(isView || isReturn) && !isInvoices"
                  style="width: 100%"
                  placeholder="发票号码"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="开票日期"
                prop="Invoice_Make_Time"
                :rules="[
                  { required: true, message: '请选择', trigger: 'blur' },
                ]"
              >
                <el-date-picker
                  v-model="form.Invoice_Make_Time"
                  :disabled="(isView || isReturn) && !isInvoices"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  type="date"
                  placeholder="开票日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="收票日期"
                prop="Invoice_Receive_Time"
                :rules="[
                  { required: true, message: '请选择', trigger: 'blur' },
                ]"
              >
                <el-date-picker
                  v-model="form.Invoice_Receive_Time"
                  :disabled="(isView || isReturn) && !isInvoices"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                  type="date"
                  placeholder="收票日期"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="备注" prop="Remark">
              <el-input
                v-model="form.Remark"
                :disabled="isView || isReturn"
                style="width: 100%"
                :rows="1"
                show-word-limit
                :maxlength="100"
                placeholder="备注"
                type="textarea"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="附件" class="factory-img">
              <OSSUpload
                class="upload-demo"
                action="alioss"
                :limit="5"
                :multiple="true"
                :on-success="
                  (response, file, fileList) => {
                    uploadSuccess(response, file, fileList)
                  }
                "
                :on-remove="uploadRemove"
                :on-preview="handlePreview"
                :on-exceed="handleExceed"
                :file-list="fileListData"
                :show-file-list="true"
                :disabled="isView || formStatus === 3|| isReturn"
              >
                <el-button
                  v-if="!(isView || formStatus === 3|| isReturn)"
                  type="primary"
                >上传文件</el-button>
              </OSSUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card v-else class="box-card">
      <el-form ref="adjustAmountFormRef" :model="adjustAmountForm" :rules="adjustAmountRules" inline label-width="100px">
        <el-form-item label="单据日期" prop="MoneyAdjustDate">
          <el-date-picker
            v-model="adjustAmountForm.MoneyAdjustDate"
            value-format="yyyy-MM-dd"
            style="width: 100%"
            type="date"
            placeholder="单据日期"
          />
        </el-form-item>
        <el-form-item label="单据编号">
          <span>{{ $route.query.id }}</span>
        </el-form-item>
        <el-form-item label="合同号">
          <span>{{ form.Purchase_Contract_No || '无' }}</span>
        </el-form-item>
        <el-form-item label="发票号码">
          <span>{{ form.Invoice_No || '无' }}</span>
        </el-form-item>
      </el-form>
      <el-form label-width="100px">
        <el-form-item label="备注">
          <el-input v-model="adjustAmountForm.Remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card box-card-tb">
      <div class="toolbar-container">
        <div class="toolbar-title"><span />入库明细</div>
      </div>
      <el-divider class="elDivder" />
      <div class="toolbar-container" style="margin-bottom: 0px">
        <el-form ref="searchForm" class="search-form" inline :model="searchForm">

          <el-form-item v-if="isRaw" :label="materialTypeName+'全名'" prop="RawNameFull">
            <el-input
              v-model="searchForm.RawNameFull"
              style="width: 100%"
              placeholder="通配符%"
              clearable
            />
          </el-form-item>
          <el-form-item :label="materialTypeName+'名称'" prop="RawName">
            <el-input
              v-model="searchForm.RawName"
              style="min-width: 100%"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="规格/厚度(理论)" prop="Spec" label-width="120px">
            <el-input
              v-model="searchForm.Spec"
              style="width: 100%"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item v-if="isRaw" label="材质" prop="Material" label-width="50px">
            <el-input
              v-model="searchForm.Material"
              style="width: 100%"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="所属项目" prop="SysProjectId">
            <el-select
              v-model="searchForm.SysProjectId"
              style="width: 100%"
              placeholder="所属项目"
              clearable
              filterable
            >
              <el-option
                v-for="item in ProjectList"
                :key="item.Id"
                :label="item.Short_Name"
                :value="item.Sys_Project_Id"
              />
            </el-select>
          </el-form-item>

          <!-- 收起/展开的其余字段 -->
          <template v-if="filterVisible">
            <el-form-item :label="materialTypeName+'分类'" prop="CategoryId">
              <SelectMaterialType v-model="searchForm.CategoryId" :is-raw="isRaw" />
            </el-form-item>
            <el-form-item v-if="isRaw" label="宽度" prop="Width">
              <el-input
                v-model="searchForm.Width"
                style="width: 100%"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item v-if="isRaw" label="长度" prop="Length">
              <el-input
                v-model="searchForm.Length"
                style="width: 100%"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <el-form-item label="仓库" prop="WarehouseId">
              <SelectWarehouse v-model="searchForm.WarehouseId" :type="materialType" />
            </el-form-item>
            <el-form-item label="库位" prop="LocationId">
              <SelectLocation v-model="searchForm.LocationId" :warehouse-id="searchForm.WarehouseId" />
            </el-form-item>
            <el-form-item v-if="form.InStoreType == 1 || form.InStoreType == 3" label="供应商" prop="Supplier">
              <el-select
                v-model="searchForm.Supplier"
                style="width: 100%"
                placeholder="供应商"
                clearable
                filterable
              >
                <el-option
                  v-for="item in SupplierList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="form.InStoreType == 2" label="甲方单位" prop="PartyUnit">
              <el-select
                v-model="searchForm.PartyUnit"
                style="width: 100%"
                placeholder="甲方单位"
                clearable
                filterable
              >
                <el-option
                  v-for="item in PartyUnitList"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </template>

          <el-form-item class="last-btn">
            <el-button type="primary" @click="handleSearch()">搜索</el-button>
            <el-button class="reset-btn" @click="handleReset">重置</el-button>
            <el-button @click="toggleFilter">{{ filterVisible ? '收起筛选' : '展开筛选' }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-divider class="elDivder" />
      <div class="toolbar-container" style="margin-bottom: 8px">
        <vxe-toolbar v-if="!isView && !isReturn">
          <template #buttons>
            <el-button type="primary" @click="openAddDialog(null)">新增</el-button>
            <el-button
              :disabled="!multipleSelection.length"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
            <el-button
              :disabled="!multipleSelection.length"
              type="primary"
              plain
              @click="handleBatchEdit"
            >批量编辑
            </el-button>
            <el-button v-if="isRaw" @click="syncWeight">理重同步至凭证重</el-button>
            <el-button
              type="primary"
              :disabled="!multipleSelection.length"
              @click="handleWarehouse(false)"
            >批量选择仓库/库位
            </el-button>
            <el-button @click="handleImport">导入</el-button>
            <USSLInboundDetailImport :material-type="materialType" @getAddList="addListReset" />
          </template>
        </vxe-toolbar>
        <div style="margin-left: auto" />
        <DynamicCustomFieldFilter :columns="columns" @search="searchCustomField" />
        <DynamicTableFields
          v-if="columns"
          title="表格配置"
          :table-columns="columns"
          :table-config-code="gridCode"
          :manual-hide-columns="manualHideTableColumns"
          :type="BigType"
          @updateColumn="getTableColumns"
        />
      </div>
      <div v-loading="!showTable" class="tb-x">
        <vxe-table
          v-if="showTable"
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true, keyField:'index'}"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading"
          :auto-resize="true"
          stripe
          size="medium"
          :data="tableData"
          resizable
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: !isView
          }"
          :tooltip-config="{ enterable: true }"
          :checkbox-config="{ checkMethod: checCheckboxkMethod }"
          :row-style="rowStyle"
          show-footer
          :footer-method="footerMethod"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column type="seq" title="序号" width="60" fixed="left" align="center" />
          <vxe-column v-if="!isView&&!isReturn" fixed="left" type="checkbox" width="45" title="" />
          <vxe-column v-if="!isView&&!isReturn" fixed="right" title="操作" width="60">
            <template #default="{ row, rowIndex }">
              <el-button type="text" :disabled="Boolean(formStatus === 3 && row.Sub_Id)" @click="handleCopy(row, rowIndex)">复制</el-button>
            </template>
          </vxe-column>
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              show-overflow="tooltip"
              :align="item.Align"
              :field="item.Code"
              :visible="item.Is_Display"
              :title="item.Is_Must_Input ? '*' + item.Display_Name : item.Display_Name"
              :min-width="item.Width"
              :edit-render="item.Is_Edit ? {} : null"
              :sortable="item.Is_Sort"
            >
              <template v-if="item.Style.tips" #header>
                <span>{{ item.Display_Name }}</span>
                <el-tooltip class="item" effect="dark">
                  <div slot="content" v-html="item.Style.tips" />
                  <i class="el-icon-question" style="cursor:pointer;font-size: 16px" />
                </el-tooltip>
              </template>
              <template #default="{ row }">
                <template v-if="item.Code === 'Weight_Method'">
                  {{ ['理重','凭证重'][form.Weight_Method] }}
                </template>
                <template v-else-if="item.Code === 'InStoreWeight'">
                  {{ form.Weight_Method == 0 ? row.Theory_Weight : row.Voucher_Weight }}
                </template>
                <template v-else-if="item.Code === 'InvoiceId'">
                  {{ row[item.Code] ? '已核销' : '未核销' }}
                </template>
                <template v-else>
                  <span> {{ row[item.Code] | displayValue }}</span>
                </template>
              </template>
              <template v-if="item.Is_Edit" #edit="{ row }">
                <div v-if="item.Code === 'Actual_Spec'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model="row[item.Code]" type="text" @change="$emit('updateRow')" />
                </div>
                <div v-else-if="item.Code === 'Width'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <span v-else-if="row.BigType === 3||row.BigType === 2"> {{ row[item.Code] | displayValue }}</span>

                  <el-input
                    v-else
                    v-model="row[item.Code]"
                    :min="0"
                    type="number"
                    @change="widthChange(row)"
                  />
                </div>
                <div v-else-if="item.Code === 'Length'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <span v-else-if="row.BigType === 3"> {{ row[item.Code] | displayValue }}</span>

                  <el-input
                    v-else
                    v-model="row[item.Code]"
                    :min="0"
                    type="number"
                    @change="lengthChange(row)"
                  />
                </div>
                <div v-else-if="item.Code === 'InStoreCount'">
                  <el-input
                    ref="numberInput"
                    v-model="row[item.Code]"
                    :min="0"
                    :controls="false"
                    type="number"
                    @change="checkWeight(row)"
                  />
                </div>
                <div v-else-if="item.Code === 'Theory_Weight'">
                  <span v-if="row.Specific_Gravity && row.BigType !== 3"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model="row[item.Code]" v-inp-num="{ toFixed: weightDecimal, min: 0 }" @change="countTaxUnitPrice(row,'Theory_Weight')" />
                </div>
                <div v-else-if="item.Code === 'PurchaseWeight'">
                  <span v-if="row.Specific_Gravity && row.BigType !== 3"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model="row[item.Code]" v-inp-num="{ toFixed: weightDecimal, min: 0 }" @change="countTaxUnitPrice(row,'PurchaseWeight')" />
                </div>
                <div v-else-if="item.Code.includes('Remark')">
                  <el-input v-model="row[item.Code]" type="text" @change="$emit('updateRow')" />
                </div>
                <div v-else-if="item.Code === 'Voucher_Weight'">
                  <el-input v-model="row[item.Code]" v-inp-num="{ toFixed: weightDecimal, min: 0 }" :min="0" @change="countTaxUnitPrice(row,'Voucher_Weight')" />
                </div>
                <div v-else-if="item.Code === 'Warehouse_Location'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <div v-else>
                    {{ row.WarehouseName }}/{{ row.LocationName }}
                    <i class="el-icon-edit pointer" @click="handleWarehouse(row)" />
                  </div>
                </div>
                <div v-else-if="item.Code === 'FurnaceBatchNo'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model="row.FurnaceBatchNo" type="text" @blur="$emit('updateRow')" />
                </div>
                <div v-else-if="['NoTaxUnitPrice','Tax_Rate','TaxUnitPrice'].includes(item.Code)">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ (row[item.Code] ||0 ) | displayValue }}</span>
                  <el-input v-else v-model="row[item.Code]" :min="0" type="number" @change="countTaxUnitPrice(row,'TaxUnitPrice')" />
                </div>

                <div v-else-if="item.Code === 'ReturnPoundWeight'">
                  <el-input v-model="row[item.Code]" />
                </div>
                <div v-else-if="item.Code === 'Delivery_No'">
                  <el-input v-model="row[item.Code]" @change="changeKeyInfo('Delivery_No',row[item.Code])" />
                </div>
                <div v-else-if="item.Code === 'CarNumber'">
                  <el-input v-model="row[item.Code]" @change="changeKeyInfo('CarNumber',row[item.Code])" />
                </div>
                <div v-else-if="item.Code === 'Driver'">
                  <el-input v-model="row[item.Code]" @change="changeKeyInfo('Driver',row[item.Code])" />
                </div>
                <div v-else-if="item.Code === 'DriverMobile'">
                  <el-input v-model="row[item.Code]" @change="changeKeyInfo('DriverMobile',row[item.Code])" />
                </div>

                <div v-else-if="item.Code === 'OutStoreCount'">
                  <span v-if="formStatus === 3 && row.OutStoreCount"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model.number="row[item.Code]" :max="row.AvailableCount" :min="0" type="number" @change="outStoreChange(row)" />
                </div>
                <div v-else-if="['Adjust_Amount_In','Adjust_Amount_Out'].includes(item.Code)">
                  <el-input v-model="row[item.Code]" type="number" />
                </div>
                <div v-else-if="item.Code === 'ProjectName'">
                  <span v-if="(formStatus === 3 && row.Sub_Id) || !row.IsCommonProject"> {{ row[item.Code] | displayValue }}</span>
                  <vxe-select v-else v-model="row.SysProjectId" filterable transfer @change="itemProjectChange(row)">
                    <vxe-option v-for="v in ProjectList" :key="v.Sys_Project_Id" :value="v.Sys_Project_Id" :label="v.Short_Name" />
                  </vxe-select>
                </div>
                <div v-else-if="item.Code === 'SupplierName'">
                  <vxe-select v-model="row.Supplier" transfer filterable clearable @change="itemSupplierChange(row)">
                    <vxe-option v-for="v in SupplierList" :key="v.Id" :value="v.Id" :label="v.Name" />
                  </vxe-select>
                </div>
                <div v-else-if="item.Code === 'PartyUnitName'">
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <vxe-select v-else v-model="row.PartyUnit" filterable transfer @change="itemPartyUnitChange(row)">
                    <vxe-option v-for="v in PartyUnitList" :key="v.Id" :value="v.Id" :label="v.Name" />
                  </vxe-select>
                </div>
                <div v-else>
                  <span v-if="formStatus === 3 && row.Sub_Id"> {{ row[item.Code] | displayValue }}</span>
                  <el-input v-else v-model="row[item.Code]" type="text" @blur="$emit('updateRow')" />
                </div>
              </template>

            </vxe-column>
          </template>

        </vxe-table>
      </div>
      <el-divider v-if="!isView" class="elDivder" />
      <footer v-if="!isView">
        <div class="data-info">
          <el-tag v-if="!isReturn" size="medium" class="info-x">已选{{ multipleSelection.length }}条数据 </el-tag>
        </div>
        <div>
          <el-button @click="closeView">取消</el-button>
          <el-button v-if="isReturn" :loading="returning" type="primary" @click="handleReturn">确认退货</el-button>
          <template v-else>
            <el-button
              v-if="formStatus !== 3"
              :loading="saveLoading"
              :disabled="submitLoading"
              @click="handleSaveDraft"
            >保存草稿
            </el-button>
            <el-button type="primary" :disabled="saveLoading" :loading="submitLoading" @click="handleSubmit">提交入库</el-button>
          </template>
        </div>
      </footer>
      <footer v-if="isAdjustAmount">
        <div style="margin-left: auto">
          <el-button @click="closeView">取消</el-button>
          <el-button type="primary" :disabled="saveLoading" :loading="submitLoading" @click="handleSubmitAdjustAmount">提交</el-button>
        </div>
      </footer>
      <footer v-if="isInvoices">
        <div style="margin-left: auto">
          <el-button @click="closeView">取消</el-button>
          <el-button type="primary" :disabled="saveLoading" :loading="submitLoading" @click="handleSubmitInvoice">提交</el-button>
        </div>
      </footer>
    </el-card>

    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      :width="dWidth"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :page-type="0"
        :project-list="ProjectList"
        :supplier-list="SupplierList"
        :party-unit-list="PartyUnitList"
        :is-replace-purchase="form.Is_Replace_Purchase"
        :check-type-list="checkTypeList"
        :is-raw="isRaw"
        @close="handleClose"
        @warehouse="getWarehouse"
        @batchEditor="batchEditorFn"
        @standard="getStandard"
        @refresh="fetchData"
        @getAddList="getAddList($event,true)"
      />
    </el-dialog>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      :title="'新增'+materialTypeName"
      :visible.sync="openAddList"
      width="70%"
      top="10vh"
      @close="handleClose"
    >
      <template v-if="openAddList">
        <add-purchase-list
          v-if="isPurchase"
          ref="draft"
          :is-single="isSingle"
          :big-type-data="BigType"
          :form-data="form"
          :project-list="ProjectList"
          :joined-items="rootTableData"
          @getAddList="getAddList"
          @getRowName="getRowName"
          @close="handleClose"
        />
        <add-raw-material-list
          v-else
          ref="draft"
          :is-purchasing="form.Is_Replace_Purchase"
          :is-single="isSingle"
          :is-manual="isManual"
          :is-purchase="isPurchase"
          :is-customer="isCustomer"
          :project-id="form.ProjectId"
          :project-list="ProjectList"
          @getAddList="getAddList"
          @getRowName="getRowName"
          @close="handleClose"
        />
      </template>
    </el-dialog>

    <el-dialog
      v-if="dialogRepeatVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      title="检查重复"
      :visible.sync="dialogRepeatVisible"
      width="70%"
      top="10vh"
      @close="handleClose"
    >
      <Repeat ref="repeat" :columns="columns" @submit="submitInfo(1,true)" @close="handleClose" />
    </el-dialog>

  </div>
</template>

<script>
import { closeTagView, debounce, deepClone, uniqueArr, parseTime } from '@/utils'
import BatchEdit from '../components/BatchEditor'
import AddList from './components/AddList.vue'
import AddPurchaseList from './components/AddPurchaseList.vue'
import AddRawMaterialList from './components/AddRawMaterialList.vue'
import ImportFile from '../components/ImportFile.vue'
import Warehouse from '../components/Warehouse.vue'
import Standard from '../components/Standard.vue'
import OSSUpload from '@/views/PRO/components/ossupload'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import draggable from 'vuedraggable'
import {
  GetInstoreDetail,
  GetOMALatestStatisticTime,
  GetPartyAs,
  GetSuppliers,
  RawReturnByReceipt,
  SaveInStore,
  StoreMoneyAdjust,
  UpdateInvoiceInfo
} from '@/api/PRO/materialManagement'
import { GetOssUrl } from '@/api/sys'
import { GetProjectPageList } from '@/api/PRO/project'
import { getTableConfig } from '@/views/PRO/material-receipt-management/utils'
import { getDictionary } from '@/utils/common'
import Repeat from '@/views/PRO/material-receipt-management/raw/components/Repeat.vue'
import { mapGetters } from 'vuex'
import { getMaterialName, isMaterialInclude } from '@/views/PRO/utils/material'
import { v4 as uuidv4 } from 'uuid'
import SelectMaterialStoreType from '@/components/Select/SelectMaterialStoreType/index.vue'
import SelectWarehouse from '@/components/Select/SelectWarehouse/index.vue'
import SelectLocation from '@/components/Select/SelectLocation/index.vue'
import {
  DETAIL_TOTAL_PRICE_DECIMAL,
  INBOUND_DETAIL_HIDE_FIELDS,
  INBOUND_DETAIL_SUMMARY_FIELDS,
  INBOUND_DETAIL_UNIT_PRICE_DECIMAL,
  INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS
} from '@/views/PRO/material_v4/config'
import USSLInboundDetailImport from '@/views/tenant-custom/ussl/USSL-Inbound-Detail-Import.vue'
import DynamicCustomFieldFilter from '@/views/PRO/material_v4/inbound/components/DynamicCustomFieldFilter.vue'
import SelectExternal from '@/components/Select/SelectExternal/index.vue'
import SelectMaterialType from '@/components/Select/SelectMaterialType/index.vue'
import getConfig from '@/views/PRO/material_v4/mixins/getConfig'

export default {
  components: {
    SelectMaterialType,
    SelectExternal,
    DynamicCustomFieldFilter,
    USSLInboundDetailImport,
    SelectLocation,
    SelectWarehouse,
    SelectMaterialStoreType,
    Repeat,
    AddPurchaseList,
    BatchEdit,
    ImportFile,
    Warehouse,
    Standard,
    AddList,
    AddRawMaterialList,
    OSSUpload,
    DynamicTableFields,
    draggable
  },
  mixins: [getConfig],
  props: {
    pageType: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      columns: [],
      num: 1,
      returning: false,
      isDraft: true,
      isRetract: false, // 是否展开
      tbLoading: false,
      filterVisible: false, // 否是显示过滤器
      statisticTime: '', // 运营核算结束时间
      multipleSelection: [],
      ProjectList: [],
      PartyUnitList: [],
      SupplierList: [],
      formStatus: +this.$route.query.status || '',
      form: {
        InStoreNo: '',
        InStoreType: +this.$route.query.type || 1,
        InStoreDate: this.getDate(),
        Delivery_No: '',
        Purchase_Contract_No: '',
        CarNumber: '',
        Is_Replace_Purchase: false,
        Driver: '',
        DriverMobile: '',
        Car_Pound_Weight: null,
        Car_Voucher_Weight: null,
        Remark: '',
        ProjectName: '',
        ProjectId: '',
        SysProjectId: '',
        PartyUnitName: '',
        PartyUnit: '',
        SupplierName: '',
        Supplier: '',
        Attachment: '',
        Status: null,
        Weight_Method: 0,
        Has_Invoice: false,
        Invoice_No: '',
        Invoice_Make_Time: '',
        Invoice_Receive_Time: ''
      },
      rules: {
        InStoreType: [{ required: true, message: '请选择', trigger: 'change' }],
        InStoreDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
        PartyUnit: [{ required: true, message: '请选择', trigger: 'change' }],
        Is_Replace_Purchase: [{ required: true, message: '请选择', trigger: 'change' }],
        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }],
        Supplier: [{ required: true, message: '请选择', trigger: 'change' }]
      },

      searchForm: {
        RawNameFull: '',
        RawName: '',
        Thick: '',
        Spec: '',
        Material: '',
        SysProjectId: '',
        CategoryId: '',
        Length: '',
        Width: '',
        Supplier: '',
        PartyUnit: '',
        WarehouseId: '',
        Location_Id: ''
      },
      currentComponent: '',
      title: '',
      dWidth: '60%',
      isSingle: false,
      submitLoading: false,
      saveLoading: false,
      search: () => ({}),
      openAddList: false,
      dialogVisible: false,
      dialogRepeatVisible: false,
      BigType: 1,

      fileListData: [],
      fileListArr: [],

      searchNum: 1,
      rootTableData: [],
      tableData: [],
      popoverVisible: false,
      manualHideTableColumns: [],
      RawReceiptTypeList: [],
      // 原料各类型表格校验规则
      checkTypeList: [
        {
          BigType: 1,
          checkList: [], // 必填字段校验
          checkSameList: [], // 重复字段校验
          remarkList: [] // 动态备注批量编辑
        },
        {
          BigType: 2,
          checkList: [],
          checkSameList: [],
          remarkList: []
        },
        {
          BigType: 3,
          checkList: [],
          checkSameList: [],
          remarkList: []
        },
        {
          BigType: 99,
          checkList: [],
          checkSameList: [],
          remarkList: []
        }
      ],
      showTable: false,
      adjustAmountForm: {
        MoneyAdjustDate: '',
        Remark: ''
      },
      adjustAmountRules: {
        MoneyAdjustDate: [
          { required: true, message: '请选择单据日期', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    isRaw() {
      return this.$route.params.type == '0'
    },
    isAux() {
      return this.$route.params.type == '1'
    },
    materialType() {
      return this.$route.params.type
    },
    materialTypeName() {
      return this.$route.params.type == '0' ? '原料' : '辅料'
    },
    isView() {
      return !['InboundAddRaw', 'InboundAddAux', 'InboundEditRaw', 'InboundEditAux'].includes(this.$route.name)
    },
    isInvoices() {
      return this.$route.name === 'addInvoices'
    },
    isAdd() {
      return ['InboundAddRaw', 'InboundAddAux'].includes(this.$route.name)
    },
    isEdit() {
      return ['InboundEditRaw', 'InboundEditAux'].includes(this.$route.name)
    },
    isPurchase() {
      return this.form.InStoreType == 1 // 采购入库
    },
    isCustomer() {
      return this.form.InStoreType == 2 // 甲供入库
    },
    isManual() {
      return this.form.InStoreType == 3 // 手动入库
    },
    isReturn() {
      return this.formStatus === 88
    },
    // 出入库金额调整
    isAdjustAmount() {
      return this.$route.name === 'InboundAdjustAmount' || this.$route.name === 'OutboundAdjustAmount'
    },
    // 入库金额调整
    isInboundAdjustAmount() {
      return this.$route.name === 'InboundAdjustAmount'
    },
    // 出库金额调整
    isOutboundAdjustAmount() {
      return this.$route.name === 'OutboundAdjustAmount'
    },
    gridCode() {
      return this.isRaw ? 'PRORawPurchaseList' : 'PROPurchaseAuxListNew'
    },
    ...mapGetters('factoryInfo', ['checkDuplicate'])
  },

  async mounted() {
    this.search = debounce(this.fetchData, 800, true)
    await this.getProject()

    // 初始化调整金额表单的日期为当天
    this.adjustAmountForm.MoneyAdjustDate = parseTime(new Date(), '{y}-{m}-{d}')

    if (!this.isAdd) {
      await this.getInfo()
    }
  },
  provide() {
    return {
      formData: this.form,
      checkDuplicate: this.getDuplicate
    }
  },
  methods: {
    async initCreated() {
      this.getFormLocal()
      await this.getFactoryInfo()
      await this.getOMALatestStatisticTime()
      await this.getTableColumns()
      await this.getRawReceiptTypeList()
      await this.getSuppliers()
      await this.getPartyAs()
    },
    rowStyle({ row }) {
      if (row.WaitInStoreWeight < 0) {
        return { 'background-color': '#F2AAB3!important' }
      }
    },
    changeKeyInfo(key, val) {
      const values = this.tableData.map(item => item[key])
      const arr = uniqueArr(values)
      if (arr.length === 1) {
        this.form[key] = val
      } else {
        this.form[key] = ''
      }
    },
    changeFormKey(key) {
      this.tableData.forEach(item => {
        this.$set(item, key, this.form[key])
      })
    },
    // 复选框禁用
    checCheckboxkMethod({ row }) {
      return !(this.formStatus === 3 && row.Sub_Id)
    },
    // 初始化表格
    init(_columns) {
      _columns = _columns.filter(item => {
        item.Style = item.Style ? JSON.parse(item.Style) : ''
        if (item.Code === 'ReturnSupplierName') {
          item.Is_Edit = false
        }
        // 不同类型的明细表，需要隐藏部分字段
        if (this.isPurchase) {
          if (INBOUND_PURCHASE_DETAIL_DISABLE_FIELDS.includes(item.Code)) {
            item.Is_Edit = false
          }
          if (item.Code === 'Tax_All_Price' && this.isRaw) {
            item.Style = { 'tips': '入库凭证重*订单含税单价' }
          }
          if (item.Code === 'NoTaxAllPrice' && this.isRaw) {
            item.Style = { 'tips': '入库凭证重*订单不含税单价' }
          }
          return !INBOUND_DETAIL_HIDE_FIELDS.isPurchase.includes(item.Code)
        }
        // 甲供
        if (this.isCustomer) {
          if (['ProjectName', 'PartyUnitName'].includes(item.Code)) {
            item.Is_Must_Input = true
          }
          return !INBOUND_DETAIL_HIDE_FIELDS.isCustomer.includes(item.Code)
        }
        // 手动
        if (this.isManual) {
          return !INBOUND_DETAIL_HIDE_FIELDS.isManual.includes(item.Code)
        }
      })
      this.columns = JSON.parse(JSON.stringify(_columns))
      // 获取所有分类校验对象
      this.getAllColumnsOption()
    },
    // 获取校验字段
    getCheckList(e, columns) {
      let fCode = []
      if (this.checkDuplicate) {
        fCode = [
          'PurchaseNo',
          'ProjectName',
          'Project_Code',
          'SupplierName',
          'RawNameFull',
          'CategoryName',
          'RawName',
          'RawCode',
          'Material',
          'Spec',
          'Actual_Spec',
          'TaxUnitPrice',
          'Tax_Rate',
          'Warehouse_Location',
          'FurnaceBatchNo'
        ]
      } else {
        fCode = []
      }
      const W = 'Width'
      const L = 'Length'
      this.checkTypeList.map(v => {
        if (v.BigType === e) {
          v.checkList = columns.filter(i => i.Is_Must_Input && i.Is_Display)
          v.checkList = v.checkList.map(i => {
            return i.Code
          })
          v.checkSameList = columns.filter(i => i.Is_Display && fCode.includes(i.Code))
          v.checkSameList = v.checkSameList.map(i => {
            return i.Code
          })
          if (this.checkDuplicate) {
            v.checkSameList.unshift('PurchaseSubId')
            if (e === 2) {
              v.checkSameList.push(L)
            } else if (e === 1) {
              v.checkSameList.push(W)
              v.checkSameList.push(L)
            }
          }
          v.remarkList = columns.filter(i => i.Is_Display && i.Code.indexOf('Remark') !== -1)
          v.remarkList = v.remarkList.map(i => {
            return {
              key: i.Code,
              label: i.Display_Name,
              type: 'string'
            }
          })
        }
      })
    },
    // 获取所有分类校验对象
    getAllColumnsOption() {
      const BigType = [1, 2, 3]
      const columns = JSON.parse(JSON.stringify(this.columns))
      BigType.forEach(e => {
        this.getCheckList(e, columns)
      })
    },
    // 清空表格数据
    tbfetchData() {
      this.tableData = []
    },
    // 复制
    handleCopy(row, rowIndex) {
      const t = JSON.parse(JSON.stringify(row))
      if (t.Sub_Id) { t.Sub_Id = '' }
      delete t._X_ROW_KEY
      delete t.index
      this.$emit('updateTb', [t], 'copy')
      this.tableData.splice(rowIndex + 1, 0, t)
    },
    setRowFullName(row) {
      if (this.isRaw) {
        row.Raw_FullName = getMaterialName(row)
      }
    },
    lengthChange(row) {
      this.checkWeight(row)
      this.setRowFullName(row)
    },
    widthChange(row) {
      this.checkWeight(row)
      this.setRowFullName(row)
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    addData(list, isImport = false) {
      const t = list.map(v => {
        v.Spec = v.BigType === 1 && this.isRaw ? v.Thick : v.Spec
        v.Actual_Spec = v.Actual_Spec || (v.BigType === 1 ? v.Thick : v.Spec)
        if (this.isRaw) {
          v.RawNameFull = getMaterialName(v)
        }

        // 采购入库，会有订单单价概念，此时，采购订单中的单价赋值给入库的订单单价.如果是导入接收单，则返回数据中存在OrderTaxUnitPrice，直接使用该字段
        if (this.isPurchase && this.isRaw) {
          this.$set(v, 'OrderTaxUnitPrice', (v.OrderTaxUnitPrice || v.TaxUnitPrice).toFixed(6) / 1)
          this.$set(v, 'OrderNoTaxUnitPrice', ((v.OrderTaxUnitPrice || v.TaxUnitPrice) / (1 + v.Tax_Rate / 100)).toFixed(6) / 1)
        }

        if (typeof v.Tax_Rate !== 'number' && this.form.InStoreType != 1) {
          v.Tax_Rate = 13
        }

        // 初始化入库重量和理重
        if ((!v.Specific_Gravity || v.BigType === 3) && v.PurchaseCount) {
          v.Theory_Weight = v.PurchaseWeight || 0
        }
        if (isImport && this.form.InStoreType == 1) {
          v.InStoreCount = v.PurchaseCount || 0
          v.Theory_Weight = v.PurchaseWeight || 0
        }
        this.checkWeight(v)
        v.rowIndex = ''
        return v
      })
      this.rootTableData.push(...t)
      this.handleSearch()
    },
    // 重量校验
    checkWeight(row) {
      if (this.isAux) {
        this.countTaxUnitPrice(row, 'InStoreCount')
        return
      }
      if ((row.Specific_Gravity !== 0 && !row.Specific_Gravity) || row.BigType === 3) {
        this.countTaxUnitPrice(row, 'InStoreCount_Width_Length')
        return
      }
      if (
        (row.BigType === 1 || row.BigType === 2) &&
        (row.Specific_Gravity === 0 || row.Length === 0)
      ) {
        row.Theory_Weight = 0
      } else if (row.BigType === 1) {
        // 花纹板计算公式：长*宽*比重*数量
        // 普通板材计算公式：厚*长*宽*比重*数量
        if (row.Thick === 0 || row.Width === 0) {
          row.Theory_Weight = 0
        } else if (row.Thick && row.Width && row.Length && row.Specific_Gravity) {
          const unitWeight =
            (
              (row.CategoryName === '花纹板' ? 1 : row.Thick) *
              row.Width *
              row.Length *
              row.Specific_Gravity / 1000
            ).toFixed(this.unitWeightDecimal)
          row.Theory_Weight = (unitWeight * row.InStoreCount).toFixed(this.weightDecimal) / 1
        } else {
          row.Theory_Weight = ''
        }
      } else if (row.BigType === 2) {
        if (row.Length && row.Specific_Gravity) {
          const unitWeight = (row.Length * row.Specific_Gravity / 1000).toFixed(this.unitWeightDecimal)
          console.log('unitWeight', unitWeight)
          row.Theory_Weight = (unitWeight * row.InStoreCount).toFixed(this.weightDecimal) / 1
          console.log('Theory_Weight', row.Theory_Weight)
        } else {
          row.Theory_Weight = ''
        }
      } else {
        row.Theory_Weight = ''
      }
      // 重量改变计算含税总价
      this.countTaxUnitPrice(row, 'InStoreCount_Width_Length')
    },
    // 重量改变重新计算统计数据/计算含税总价
    countTaxUnitPrice(row, type) {
      if (type === 'TaxUnitPrice') {
        if (this.taxMode) {
          this.$set(row, 'NoTaxUnitPrice', (Number(row.TaxUnitPrice || 0) / (1 + (row.Tax_Rate || 0) / 100)).toFixed(INBOUND_DETAIL_UNIT_PRICE_DECIMAL) / 1)
        } else {
          this.$set(row, 'TaxUnitPrice', (Number(row.NoTaxUnitPrice || 0) * (1 + (row.Tax_Rate || 0) / 100)).toFixed(INBOUND_DETAIL_UNIT_PRICE_DECIMAL) / 1)
        }
      }
      /*
      * 计算含税总价
      * 原料：
      *   ·采购入库   总价 = 订单单价 * 凭证重    单价 = 总价 / 计量重量
      *   ·手动/甲供入库  总价 = 计量单价 * 计量重量
      * 辅料： 数量 * 单价
      */
      const weight = this.form.Weight_Method == 0 ? row.Theory_Weight : row.Voucher_Weight
      console.log({ weight })
      if (this.isPurchase && this.isRaw) {
        row.Tax_All_Price = ((row.OrderTaxUnitPrice || 0) * (row.Voucher_Weight || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL) / 1
        row.TaxUnitPrice = (row.Tax_All_Price / weight).toFixed(INBOUND_DETAIL_UNIT_PRICE_DECIMAL) / 1
      } else if (this.isRaw) {
        if (type === 'TaxUnitPrice' || type === 'Theory_Weight' || type === 'Voucher_Weight' || type === 'InStoreCount_Width_Length') {
          if (weight === 0 || row.TaxUnitPrice === 0) {
            row.Tax_All_Price = 0
          } else if (weight && row.TaxUnitPrice) {
            row.Tax_All_Price = Number(((weight || 0) * (row.TaxUnitPrice || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL)) / 1
          } else {
            row.Tax_All_Price = ''
          }
        }
      } else {
        // 计算辅料
        row.Tax_All_Price = Number(((row.InStoreCount || 0) * (row.TaxUnitPrice || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL)) / 1
      }

      this.taxAllPriceChange(row)
      // 表格数据变更
      this.$emit('updateRow')
    },
    taxAllPriceChange(row) {
      const weight = this.form.Weight_Method == 0 ? row.Theory_Weight : row.Voucher_Weight
      if (this.isPurchase && this.isRaw) {
        row.NoTaxAllPrice = ((row.OrderNoTaxUnitPrice || 0) * (row.Voucher_Weight || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL) / 1
        row.NoTaxUnitPrice = (row.NoTaxAllPrice / weight).toFixed(INBOUND_DETAIL_UNIT_PRICE_DECIMAL) / 1
      } else if (this.isRaw) {
        if (weight == 0 || row.NoTaxUnitPrice == 0) {
          row.NoTaxAllPrice = ''
        } else {
          row.NoTaxAllPrice = Number(((weight || 0) * (row.NoTaxUnitPrice || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL)) / 1
        }
      } else {
        row.NoTaxAllPrice = Number((row.InStoreCount || 0) * (row.NoTaxUnitPrice || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL) / 1
      }
      row.Tax = ((row.Tax_All_Price || 0) - (row.NoTaxAllPrice || 0)).toFixed(DETAIL_TOTAL_PRICE_DECIMAL) / 1
      this.$refs?.xTable?.updateFooter()
    },
    outStoreChange(row) {
      /*      1.1退货理重
      1板材（非花纹板）：长*宽*理论厚度*比重*退货数量
        花纹板：长*宽*比重*退货数量
      2型材：长*比重*退货数量
      3钢卷.99其他：比重*退货数量

      没有比重使用单重计算:理重/可用数量  PerWeight

      1.2退货凭证重
      退货凭证单重*退货数量

      1.3 退货磅重
      手输*/

      const w = row.Specific_Gravity
      let _OutStoreWeightKg = 0

      if (!w) {
        _OutStoreWeightKg = row.PerWeight * row.OutStoreCount
      } else {
        if (row.BigType === 1) {
          _OutStoreWeightKg = Number(
            (
              row.Width *
              row.Length *
              Number(row.CategoryName === '花纹板' ? 1 : row.Spec) *
              w *
              row.OutStoreCount
            ))
        } else if (row.BigType === 2) {
          _OutStoreWeightKg = Number(
            (
              row.Length *
              w *
              row.OutStoreCount
            ))
        } else {
          _OutStoreWeightKg = Number(
            (row.PerWeight * row.OutStoreCount))
        }
      }
      row.ReturnVoucherWeight = (row.PerVoucherWeight * row.OutStoreCount).toFixed(this.weightDecimal) / 1
      this.$emit('updateRow')
    },
    getDuplicate() {
      return this.checkDuplicate
    },
    async getFactoryInfo() {
      await this.$store.dispatch('factoryInfo/getWorkshop')
    },
    // 收起/展开
    handleRetract() {
      this.isRetract = !this.isRetract
    },
    /**
     * 明细筛选实现逻辑
     * 1. tableData：明细表格数据
     * 2. rootTableData：原始明细表格数据
     * 3. this.tableData：子组件明细表格数据
     * 4. 三个数据源互相独立互不关联，tableData同tbData保持一致
     * 5. 新增数据和复制操作后，调用handleUpdateTb更新表格数据条数，三个数据源同步更新
     * 6. tbData数据变更后，调用handleUpdateRow同步更新表格数据tableData和rootTableData
     */
    handleSearch(diyColumns) {
      this.tableData = this.rootTableData
      if (diyColumns) {
        this.tableData = this.tableData.filter(i => {
          let f = true
          diyColumns && diyColumns.forEach(item => {
            if (item.Value && !i[item.Code]?.includes(item.Value)) {
              f = false
            }
          })
          return f
        })
      }
      if (this.searchForm.RawNameFull) {
        this.tableData = this.tableData.filter(item => {
          return isMaterialInclude(item.Raw_FullName, this.searchForm.RawNameFull)
        })
      }
      if (this.searchForm.RawName) {
        this.tableData = this.tableData.filter(item => {
          return item.RawName.includes(this.searchForm.RawName)
        })
      }
      if (this.searchForm.Thick) {
        this.tableData = this.tableData.filter(item => {
          return item.Thick.includes(this.searchForm.Thick)
        })
      }
      if (this.searchForm.Spec) {
        this.tableData = this.tableData.filter(item => {
          return item.Spec.includes(this.searchForm.Spec)
        })
      }
      if (this.searchForm.Material) {
        this.tableData = this.tableData.filter(item => {
          return item.Material.includes(this.searchForm.Material)
        })
      }
      if (this.searchForm.SysProjectId) {
        this.tableData = this.tableData.filter(item => {
          return item.SysProjectId === this.searchForm.SysProjectId
        })
      }

      if (this.searchForm.CategoryId) {
        this.tableData = this.tableData.filter(item => {
          return item.CategoryId === this.searchForm.CategoryId
        })
      }
      if (this.searchForm.Length) {
        this.tableData = this.tableData.filter(item => {
          return (
            Number(item.Length) === Number(this.searchForm.Length)
          )
        })
      }
      if (this.searchForm.Width) {
        this.tableData = this.tableData.filter(item => {
          return (
            Number(item.Width) === Number(this.searchForm.Width)
          )
        })
      }
      if (this.searchForm.Supplier) {
        this.tableData = this.tableData.filter(item => {
          return item.Supplier === this.searchForm.Supplier
        })
      }
      if (this.searchForm.PartyUnit) {
        this.tableData = this.tableData.filter(item => {
          return item.PartyUnit === this.searchForm.PartyUnit
        })
      }
      if (this.searchForm.WarehouseId) {
        this.tableData = this.tableData.filter(item => {
          return item.WarehouseId === this.searchForm.WarehouseId
        })
      }
      if (this.searchForm.LocationId) {
        this.tableData = this.tableData.filter(item => {
          return item.LocationId === this.searchForm.LocationId
        })
      }
    },
    // 重置搜索
    handleReset() {
      this.searchForm.RawNameFull = ''
      this.searchForm.RawName = ''
      this.searchForm.Thick = ''
      this.searchForm.Spec = ''
      this.searchForm.Material = ''
      this.searchForm.SysProjectId = ''
      this.searchForm.CategoryId = ''
      this.searchForm.Length = ''
      this.searchForm.Width = ''
      this.searchForm.Supplier = ''
      this.searchForm.PartyUnit = ''
      this.searchForm.WarehouseId = ''
      this.searchForm.LocationId = ''
      this.tableData = this.rootTableData
    },
    // 获取表格配置数据
    async getTableColumns() {
      this.showTable = false
      this.columns = await getTableConfig(this.gridCode, this.BigType)
      if (this.isReturn) {
        const c = [{
          Code: 'AvailableCount',
          Display_Name: '可退数量',
          Width: 150,
          Frozen_Dirction: 'right',
          Is_Frozen: true
        }, {
          Code: 'OutStoreCount',
          Display_Name: '退货数量',
          Width: 120,
          Is_Edit: true,
          Frozen_Dirction: 'right',
          Is_Frozen: true
        }, {
          Code: 'OutStoreWeight',
          Display_Name: '退货理重',
          Width: 120,
          Frozen_Dirction: 'right',
          Is_Frozen: true
        }, {
          Code: 'ReturnSupplierName',
          Display_Name: '退货供应商',
          Width: 120,
          Is_Edit: true,
          Frozen_Dirction: 'right',
          Is_Frozen: true
        }, {
          Code: 'ReturnVoucherWeight',
          Display_Name: '退货凭证重',
          Width: 120,
          Frozen_Dirction: 'right',
          Is_Frozen: true
        }, {
          Code: 'ReturnPoundWeight',
          Display_Name: '退货磅重',
          Width: 120,
          Frozen_Dirction: 'right',
          Is_Edit: true,
          Is_Frozen: true
        }]
        this.columns.forEach(ele => {
          ele.Is_Edit = false
        })
        this.columns.push(...c)
      }
      if (this.isAdjustAmount) {
        const c = [{
          Code: 'Adjust_Amount_In',
          Display_Name: '入库调整金额',
          Width: 150,
          Frozen_Dirction: 'right',
          Is_Frozen: true,
          Is_Edit: this.isInboundAdjustAmount
        }, {
          Code: 'Adjust_Amount_Out',
          Display_Name: '出库调整金额',
          Width: 120,
          Frozen_Dirction: 'right',
          Is_Frozen: true,
          Is_Edit: this.isOutboundAdjustAmount
        }]
        this.columns.forEach(ele => {
          ele.Is_Edit = false
        })
        this.columns.push(...c)
      }
      this.init(this.columns)
      this.showTable = true
    },
    /**
     * 获取原料入库类型列表
     */
    getRawReceiptTypeList() {
      getDictionary('RawReceiptType').then(res => {
        this.RawReceiptTypeList = res.filter(v => v.Is_Enabled)
      })
    },

    /**
     * 附件上传
     */
    uploadSuccess(response, file, fileList) {
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    uploadRemove(file, fileList) {
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    async handlePreview(file) {
      let encryptionUrl = ''
      if (file.response && file.response.encryptionUrl) {
        encryptionUrl = file.response.encryptionUrl
      } else {
        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })
        encryptionUrl = encryptionUrl.Data
      }
      window.open(encryptionUrl)
    },
    // 文件超出数量限制
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '附件数量不能超过5个'
      })
    },
    fetchData() {},

    /**
     * 编辑时获取数据详情
     */
    getInfo() {
      GetInstoreDetail({
        inStoreNo: this.$route.query.id,
        materialType: this.materialType,
        inStoreType: this.$route.query.type
      }).then(res => {
        if (res.IsSucceed) {
          const Receipt = res.Data.Receipt
          const Sub = res.Data.Sub
          const {
            InStoreDate,
            CarNumber,
            Driver,
            DriverMobile,
            Car_Pound_Weight,
            Car_Voucher_Weight,
            Remark,
            ProjectId,
            SysProjectId,
            PartyUnit,
            Supplier,
            Attachment,
            Status,
            Delivery_No,
            Purchase_Contract_No,
            Is_Replace_Purchase,
            Invoice_Make_Time,
            Invoice_No,
            Invoice_Receive_Time,
            Has_Invoice,
            Weight_Method,
            Id
          } = Receipt
          this.form.InStoreDate = this.getDate(new Date(InStoreDate))
          this.form.CarNumber = CarNumber
          this.form.Driver = Driver
          this.form.DriverMobile = DriverMobile
          this.form.Car_Pound_Weight = Car_Pound_Weight ? Number(Car_Pound_Weight.toFixed(3)) : Car_Pound_Weight
          this.form.Car_Voucher_Weight = Car_Voucher_Weight ? Number(Car_Voucher_Weight.toFixed(3)) : Car_Voucher_Weight
          this.form.Remark = Remark
          this.form.ProjectId = ProjectId
          this.form.SysProjectId = SysProjectId
          this.form.ProjectName = SysProjectId
            ? this.ProjectList.find(v => v.Sys_Project_Id === SysProjectId).Short_Name
            : ''
          this.form.PartyUnit = PartyUnit
          this.form.PartyUnitName = PartyUnit
            ? this.PartyUnitList.find(v => v.Id === PartyUnit).Name
            : ''
          this.form.Supplier = Supplier
          this.form.SupplierName = Supplier
            ? this.SupplierList.find(v => v.Id === Supplier).Name
            : ''
          this.form.Status = Status
          this.form.Delivery_No = Delivery_No
          this.form.Purchase_Contract_No = Purchase_Contract_No
          this.form.Is_Replace_Purchase = Is_Replace_Purchase
          this.form.Invoice_Make_Time = Invoice_Make_Time
          this.form.Invoice_No = Invoice_No
          this.form.Invoice_Receive_Time = Invoice_Receive_Time
          this.form.Has_Invoice = this.isInvoices ? true : Has_Invoice
          this.form.Weight_Method = Weight_Method
          this.form.Id = Id
          if (Attachment) {
            this.form.Attachment = Attachment
            const AttachmentArr = Attachment.split(',')
            AttachmentArr.forEach(item => {
              const fileUrl =
                item.indexOf('?Expires=') > -1
                  ? item.substring(0, item.lastIndexOf('?Expires='))
                  : item
              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))
              const AttachmentJson = {}
              AttachmentJson.name = fileName
              AttachmentJson.url = fileUrl
              AttachmentJson.encryptionUrl = fileUrl
              this.fileListData.push(AttachmentJson)
              this.fileListArr.push(AttachmentJson)
            })
          }
          // 处理表格数据
          const SubData = Sub.map((row, index) => {
            row.index = uuidv4()
            row.Warehouse_Location = row.WarehouseName
              ? row.WarehouseName + '/' + row.LocationName
              : ''
            row.Width = (row.BigType === 3 || row.BigType === 2) ? 0 : row.Width
            row.Length = (row.BigType === 3) ? 0 : row.Length
            row.NoTaxUnitPrice = (row.TaxUnitPrice / (1 + row.Tax_Rate / 100)).toFixed(INBOUND_DETAIL_UNIT_PRICE_DECIMAL) / 1
            if (this.isInboundAdjustAmount) {
              row.Adjust_Amount_In = ''
            }
            if (this.isOutboundAdjustAmount) {
              row.Adjust_Amount_Out = ''
            }
            this.taxAllPriceChange(row)
            return row
          })
          this.$nextTick(_ => {
            this.tableData = this.rootTableData = SubData
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    /**
     * 获取运营核算结算时间
     */
    getOMALatestStatisticTime() {
      GetOMALatestStatisticTime().then(res => {
        if (res.IsSucceed) {
          this.statisticTime = res.Data
        }
      })
    },
    /**
     * 获取供应商
     */
    getSuppliers() {
      GetSuppliers({
        Page: 1,
        PageSize: -1
      }).then(res => {
        if (res.IsSucceed) {
          const SupplierList = []
          for (const key in res.Data) {
            const item = {
              Id: key,
              Name: res.Data[key]
            }
            SupplierList.push(item)
          }
          this.SupplierList = SupplierList
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    /**
     * 获取甲方单位
     */
    getPartyAs() {
      GetPartyAs({
        Page: 1,
        PageSize: -1
      }).then(res => {
        if (res.IsSucceed) {
          const PartyUnitList = []
          for (const key in res.Data) {
            const item = {
              Id: key,
              Name: res.Data[key]
            }
            PartyUnitList.push(item)
          }
          this.PartyUnitList = PartyUnitList
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    /**
     * 获取所属项目
     */
    async getProject() {
      await GetProjectPageList({
        Page: 1,
        PageSize: -1
      }).then(res => {
        if (res.IsSucceed) {
          this.ProjectList = res.Data.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    /* *
     * 获取仓库/库位数据
     */
    getWarehouse({ warehouse, location }) {
      if (this.currentRow) {
        this.currentRow.WarehouseId = warehouse.Id
        this.currentRow.LocationId = location.Id
        this.$set(this.currentRow, 'WarehouseName', warehouse.Display_Name)
        this.$set(this.currentRow, 'LocationName', location.Display_Name)
        this.$set(
          this.currentRow,
          'Warehouse_Location',
          warehouse.Display_Name + '/' + location.Display_Name
        )
      } else {
        this.multipleSelection.forEach((element, idx) => {
          this.$set(element, 'WarehouseName', warehouse.Display_Name)
          this.$set(element, 'LocationName', location.Display_Name)
          this.$set(
            element,
            'Warehouse_Location',
            warehouse.Display_Name + '/' + location.Display_Name
          )
          element.LocationId = location.Id
          element.WarehouseId = warehouse.Id
        })
      }
      this.handleUpdateRow()
    },

    // 批量编辑
    batchEditorFn(data) {
      this.multipleSelection.forEach((element, idx) => {
        data.forEach(item => {
          this.$set(element, item.key, item.val)
          if (item.key === 'Supplier') {
            this.$set(element, 'SupplierName', item.name)
          } else if (item.key === 'PartyUnit') {
            this.$set(element, 'PartyUnitName', item.name)
          } else if (item.key === 'SysProjectId') {
            const p = this.ProjectList.find(v => v.Sys_Project_Id === item.val)
            if (this.isRaw) {
              const version = p.Big_Type === 1 ? p.Version_Id : p.Big_Type === 2 ? p.Version_Id2 : p.Version_Id3
              const curVer = element.Versions.find(i => i.Id === version)
              if (curVer) {
                element.Specific_Gravity = curVer?.Specific_Gravity
                element.Version_Id = curVer?.Id
              } else {
                const cur = element.Versions.find(i => i.Is_System)
                element.Specific_Gravity = cur?.Specific_Gravity
                element.Version_Id = cur?.Id
              }
            }
            this.$set(element, 'ProjectName', item.name)
            this.$set(element, 'Project_Code', p?.Code)
          } else if (item.key === 'NoTaxUnitPrice' || item.key === 'Tax_Rate') {
            this.countTaxUnitPrice(element, 'TaxUnitPrice')
          }
        })
      })
      // }
      // 批量编辑 重量校验
      this.multipleSelection.forEach((element, idx) => {
        data.forEach(item => {
          if (['SysProjectId', 'Width', 'Length', 'InStoreCount'].includes(item.key)) {
            this.checkWeight(element)
          } else if (['TaxUnitPrice'].includes(item.key)) {
            this.countTaxUnitPrice(element, item.key)
          }
        })
      })
      this.handleUpdateRow()
      this.handleClose()
    },
    changeStandard(row) {
      this.currentRow = row
      this.currentComponent = 'Standard'
      this.dWidth = '40%'
      this.title = '选择规格'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].getOption(row)
      })
    },
    getStandard({ type, val }) {
      if (type === 1) {
        this.$set(this.currentRow, 'StandardDesc', val)
      } else {
        this.$set(this.currentRow, 'StandardDesc', val.StandardDesc)
        this.currentRow.StandardId = val.StandardId
      }
    },
    // 类型切换
    typeChange(n) {
      if (n !== 0) {
        this.fileListData = []
        this.form.DriverMobile = '' // 清空司机手机号
        this.$refs['form'].resetFields()
        this.form.ProjectName = ''
        this.form.SysProjectId = ''
        this.form.SupplierName = ''
        this.form.Supplier = ''
        this.form.PartyUnitName = ''
        this.form.PartyUnit = ''
        this.form.Is_Replace_Purchase = false
        this.form.InStoreType = n
        this.multipleSelection = []
        this.rootTableData = []
        this.handleReset()
        this.getTableColumns()
      }
    },
    // 是否代购切换
    isReplacePurchaseChange(e) {
      if (e) {
        this.tableData.length = 0
        this.multipleSelection = []
        this.rootTableData = []
        this.handleReset()
      } else {
        this.form.ProjectId = ''
        this.form.SysProjectId = ''
        this.form.Supplier = ''
      }
    },
    // 项目切换
    projectChange(e) {
      this.form.ProjectName = this.ProjectList.find(v => v.Id === e).Short_Name
      this.form.SysProjectId = this.ProjectList.find(v => v.Id === e).Sys_Project_Id
      if (e) {
        this.tableData.length = 0
        this.multipleSelection = []
        this.rootTableData = []
        this.handleReset()
      }
    },
    supplierChange(e) {
      this.form.SupplierName = this.SupplierList.find(v => v.Id === e).Name
      if (e) {
        this.tableData.length = 0
        this.multipleSelection = []
        this.rootTableData = []
        this.handleReset()
      }
    },
    partyUnitChange(e) {
      this.form.PartyUnitName = this.PartyUnitList.find(v => v.Id === e).Name
      if (e) {
        this.tableData.length = 0
        this.multipleSelection = []
        this.rootTableData = []
        this.handleReset()
      }
    },
    // 供应商切换
    itemSupplierChange(row) {
      row.SupplierName = this.SupplierList.find(v => v.Id === row.Supplier).Name
    },
    // 甲方单位切换
    itemPartyUnitChange(row) {
      row.PartyUnitName = this.PartyUnitList.find(v => v.Id === row.PartyUnit).Name
    },
    // 项目切换
    itemProjectChange(row) {
      const cur = this.ProjectList.find(v => v.Sys_Project_Id === row.SysProjectId)
      if (cur) {
        row.ProjectName = cur.Short_Name
        row.Project_Code = cur.Code
        const key = row.Big_Type === 1 ? 'Version_Id' : row.Big_Type === 2 ? 'Version_Id2' : 'Version_Id3'
        const curVer = row.Versions.find(i => i.Id === cur[key])
        if (curVer) {
          row.Specific_Gravity = curVer.Specific_Gravity
          row.Version_Id = cur[key]
        }
      }
      this.checkWeight(row)
    },

    // 打开新增弹窗
    openAddDialog(row) {
      // 开启代购，判断是否有项目和供应商/甲方单位
      if (this.form.Is_Replace_Purchase) {
        if (
          (this.form.InStoreType == 1 || this.form.InStoreType == 3) &&
          (!this.form.ProjectId || !this.form.Supplier)
        ) {
          this.$message({
            message: '请先选择所属项目和供应商',
            type: 'warning'
          })
          return
        } else if (this.form.InStoreType == 2 && (!this.form.ProjectId || !this.form.PartyUnit)) {
          this.$message({
            message: '请先选择所属项目和甲方单位',
            type: 'warning'
          })
          return
        }
      }
      this.currentRow = row
      this.openAddList = true
      if (row) {
        this.isSingle = true
        this.$nextTick(_ => {
          this.$refs['draft'].setRow(row)
        })
      } else {
        this.isSingle = false
      }
    },
    closeView() {
      closeTagView(this.$store, this.$route)
    },

    // 导入
    handleImport() {
      this.currentComponent = 'ImportFile'
      this.dWidth = '40%'
      this.title = this.materialTypeName + '导入'
      this.dialogVisible = true
    },
    addListReset(list) {
      this.rootTableData = []
      this.tableData = []
      this.getAddList(list, false)
    },
    // 获取新增表格数据
    getAddList(list, isImport = false) {
      // 单价转换
      list.map(item => {
        if (this.form.Delivery_No) {
          item.Delivery_No = this.form.Delivery_No
        }
        if (this.form.CarNumber) {
          item.CarNumber = this.form.CarNumber
        }
        if (this.form.Driver) {
          item.Driver = this.form.Driver
        }
        if (this.form.DriverMobile) {
          item.DriverMobile = this.form.DriverMobile
        }
        item.Width = (item.BigType === 3 || item.BigType === 2) ? 0 : item.Width
        item.Length = (item.BigType === 3) ? 0 : item.Length
        // 组装全名
        if (this.isRaw) {
          item.Raw_FullName = getMaterialName({
            BigType: item.BigType,
            RawName: item.RawName,
            Material: item.Material,
            Thick: item.Thick,
            Spec: item.Spec,
            Width: item.Width,
            Length: item.Length
          })
        }
        return item
      })
      console.log(this.form)
      if (
        this.form.Is_Replace_Purchase &&
        (this.form.InStoreType == 2 || this.form.InStoreType == 3)
      ) {
        const cur = this.ProjectList.find(v => v.Sys_Project_Id === this.form.SysProjectId)
        list.forEach(item => {
          item.ProjectName = this.form.ProjectName
          item.Project_Code = this.ProjectList.find(v => v.Sys_Project_Id === this.form.SysProjectId)?.Code
          item.SysProjectId = this.form.SysProjectId
          item.SupplierName = this.form.SupplierName
          item.Supplier = this.form.Supplier
          item.PartyUnitName = this.form.PartyUnitName
          item.PartyUnit = this.form.PartyUnit

          if (cur && !isImport && this.isRaw) {
            const gravity = item.Versions.find(i => i.Is_System)
            const key = item.Big_Type === 1 ? 'Version_Id' : item.Big_Type === 2 ? 'Version_Id2' : 'Version_Id3'
            const curVer = item.Versions.find(i => i.Id === cur[key])
            item.Specific_Gravity = curVer?.Specific_Gravity || gravity.Specific_Gravity
            item.Version_Id = curVer?.Id || gravity.Id

            console.log(item.Specific_Gravity, item.Version_Id)
          }
        })
      } else if (!isImport && !this.form.Is_Replace_Purchase &&
        (this.form.InStoreType == 2 || this.form.InStoreType == 3)) {
        list.forEach((element, idx) => {
          if ((element.ProjectName && element.Version_Id) || !this.isRaw) {

          } else {
            const gravity = element.Versions.find(i => i.Is_System)?.Specific_Gravity
            element.Specific_Gravity = gravity ?? null
            element.Version_Id = element.Versions.find(i => i.Is_System)?.Id
          }
        })
      }
      this.handleUpdateTb(list, 'add', isImport)
    },

    // 更新表格数据条数
    handleUpdateTb(list, type, isImport) {
      // 添加唯一index
      list.map((item, index) => {
        // item.index = this.rootTableData.length + index
        item.index = uuidv4()
      })
      const tempList = JSON.parse(JSON.stringify(list))
      if (type === 'add') {
        this.addData(tempList, isImport)
      }
    },
    // 更新表格数据Row
    handleUpdateRow() {
      this.rootTableData.map(item => {
        const tempV = this.tableData.find(v => v.index === item.index)
        if (tempV) {
          Object.assign(item, tempV)
        }
      })
    },
    getRowName({ Name, Id }) {
      this.currentRow.Name = Name
      this.currentRow.RawId = Id
      this.currentRow.StandardDesc = ''
    },
    handleBatchEdit() {
      this.width = '40%'
      this.generateComponent('批量编辑', 'BatchEdit')
      this.$nextTick(_ => {
        this.$refs['content'].init(this.multipleSelection, this.BigType, this.form.InStoreType, this.taxMode)
      })
    },
    generateComponent(title, component) {
      this.title = title
      this.currentComponent = component
      this.dialogVisible = true
    },
    handleWarehouse(isInline) {
      this.currentRow = isInline
      this.currentComponent = 'Warehouse'
      this.dWidth = '40%'
      this.title = '批量选择仓库/库位'
      !isInline && (this.currentRow = null)
      this.dialogVisible = true
    },

    handleDelete(row) {
      this.multipleSelection.forEach((element, idx) => {
        // 通过索引删除数据
        const i1 = this.rootTableData.findIndex(v => v.index === element.index)
        this.rootTableData.splice(i1, 1)
        this.handleSearch()
      })
      this.multipleSelection = []
      this.$refs?.xTable.clearCheckboxRow()
    },
    handleClose() {
      this.openAddList = false
      this.dialogVisible = false
      this.dialogRepeatVisible = false
    },
    handleDetail(row) {},
    // 表格数据校验
    checkValidate(type) {
      this.handleUpdateRow()
      const submit = deepClone(this.rootTableData)
      if (!submit.length) {
        this.$message({
          message: '数据不能为空',
          type: 'warning'
        })
        return {
          status: false
        }
      }
      let message = {
        status: true,
        type: '',
        msg: ''
      }
      const sub = submit
      if (sub.length && message.status) {
        message = this.checkTb(sub, type)
      }
      if (!message.status) {
        this.$message({
          message: `${message.type + message.msg || '必填字段'}不能为空`,
          type: 'warning'
        })
        return {
          status: false
        }
      }
      // 校验重复数据
      const message2 = {
        status: true,
        type: '',
        msg: ''
      }
      if (!message2.status) {
        this.$message({
          message: `${message2.type}存在重复数据`,
          type: 'warning'
        })
        return {
          status: false
        }
      }
      return {
        data: submit,
        status: true
      }
    },
    // 校验列表必填数据
    checkTb(list, type) {
      for (let i = 0; i < list.length; i++) {
        let check = this.checkTypeList.find(v => v.BigType === list[i].BigType)?.checkList || []
        // 保存草稿时，不需要校验凭证重
        if (type === 1) {
          check = check.filter(i => i !== 'Voucher_Weight' && i !== 'InStoreWeight')
        }
        const item = list[i]
        for (let j = 0; j < check.length; j++) {
          const c = check[j]
          if (['', null, undefined].includes(item[c])) {
            const cloumns = this.columns
            const element = cloumns.find(v => v.Code === c)
            return {
              status: false,
              msg: element?.Display_Name,
              type:
                item.BigType === 1
                  ? '板材'
                  : item.BigType === 2
                    ? '型材'
                    : item.BigType === 3
                      ? '钢卷'
                      : '其他'
            }
          }
        }
        delete item._X_ROW_KEY
        delete item.WarehouseName
        delete item.LocationName
      }

      return {
        status: true,
        msg: '',
        type: ''
      }
    },
    handleSaveDraft() {
      this.isDraft = true
      this.saveDraft(1, false)
    },
    submitInfo() {
      if (this.isDraft) {
        this.saveDraft(1, true)
      } else {
        this.$confirm('确认提交入库单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.saveDraft(3, true)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
            this.$refs['repeat']?.setLoading(false)
          })
      }
    },
    /* *
     * 提交入库
     */
    handleSubmit(row) {
      this.isDraft = false
      this.saveDraft(3, false)
    },
    /* *
     * 保存数据
     */
    saveDraft(type = 1, isMerge = false) {
      if (this.isRaw) {
        this.rootTableData = this.rootTableData.map(item => {
          item.InStoreWeight = this.form.Weight_Method == 0 ? item.Theory_Weight : item.Voucher_Weight
          item.Weight_Method = this.form.Weight_Method
          return item
        })
      }
      this.handleUpdateRow()
      const { data, status } = this.checkValidate(type)
      if (!status) return
      this.$refs['form'].validate(valid => {
        if (!valid) return false
        const formAttachment = []
        if (this.fileListArr.length > 0) {
          this.fileListArr.forEach(item => {
            formAttachment.push(
              item.response && item.response.encryptionUrl
                ? item.response.encryptionUrl
                : item.encryptionUrl
            )
          })
        }
        const form = { ...this.form }
        form.Attachment = formAttachment.join(',')
        form.Status = type === 1 ? 1 : 3
        form.InStoreNo = this.$route.query.id
        if (this.isRaw) {
          const { show, resultArray, indexes } = this.checkSameInfo()
          if (!isMerge && show && this.checkDuplicate) {
            this.dialogRepeatVisible = true
            this.$nextTick(_ => {
              this.$refs['repeat'].setTbData(resultArray, indexes)
            })
          }
        }
        if (type === 1) {
          this.saveLoading = true
        } else {
          this.submitLoading = true
        }
        this.submitDraft(form, data)
      })
    },
    submitDraft(form, data) {
      SaveInStore({
        Receipt: form,
        Sub: data,
        MaterialType: this.materialType,
        InStoreType: form.InStoreType
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.saveLoading = false
        this.submitLoading = false
        this.$refs['repeat']?.setLoading(false)
      })
    },

    // 选中行
    setSelectRow(multipleSelection) {
      this.multipleSelection = multipleSelection
    },

    // 日期格式化
    getDate(data) {
      const date = data || new Date()
      const year = date.getFullYear()
      const month = ('0' + (date.getMonth() + 1)).slice(-2)
      const day = ('0' + date.getDate()).slice(-2)
      return `${year}-${month}-${day}`
    },
    checkSameInfo() {
      const tb = JSON.parse(JSON.stringify(this.tableData))
      tb.forEach(v => {
        const sameItem = this.checkTypeList.find(item => item.BigType === v.BigType)
        const sameCode = sameItem.checkSameList.filter(v => !!v)

        v.currentKey = sameCode.reduce((acc, cur) => {
          return acc + '-' + ((v[cur] || '').toString().trim())
        }, '')
      })

      const list = tb
      const keyCount = list.reduce((count, obj) => {
        if (obj.currentKey) {
          count[obj.currentKey] = (count[obj.currentKey] || 0) + 1
        }
        return count
      }, {})

      const nonUniqueKeys = Object.keys(keyCount).filter(key => keyCount[key] > 1)
      const filteredList = list.filter(obj => nonUniqueKeys.includes(obj.currentKey))
      const groupedByKey = filteredList.reduce((acc, obj) => {
        if (!acc[obj.currentKey]) {
          acc[obj.currentKey] = []
        }
        acc[obj.currentKey].push(obj)
        return acc
      }, {})

      const resultArray = Object.keys(groupedByKey).reduce((acc, key) => {
        acc = acc.concat(groupedByKey[key])
        return acc
      }, [])

      const indexes = resultArray.reduce((indexes, obj, index) => {
        if (index === 0 || obj.currentKey !== resultArray[index - 1].currentKey) {
          indexes.push(index)
        }
        return indexes
      }, [])

      return {
        show: resultArray.length > 0,
        resultArray,
        indexes
      }
    },
    toggleFilter() {
      this.filterVisible = !this.filterVisible
    },
    handleReturn() {
      const { data, status } = this.checkValidate()
      if (!status) return
      this.$refs['form'].validate(valid => {
        if (!valid) return false
        const formAttachment = []
        if (this.fileListArr.length > 0) {
          this.fileListArr.forEach(item => {
            formAttachment.push(
              item.response && item.response.encryptionUrl
                ? item.response.encryptionUrl
                : item.encryptionUrl
            )
          })
        }
        let msg = ''
        const form = { ...this.form }
        form.Attachment = formAttachment.join(',')
        form.InStoreNo = this.$route.query.id
        const curData = data.filter(item => {
          if ((item.OutStoreWeight >= item.AvailableWeight && item.OutStoreCount != item.AvailableCount) || (item.OutStoreWeight != item.AvailableWeight && item.OutStoreCount >= item.AvailableCount)) {
            msg = '明细数据中数量或重量有一个值为库存最大值但另一个值非最大值，非最大值将自动被改为最大值'
            item.OutStoreCount = item.AvailableCount
            item.OutStoreWeight = item.AvailableWeight
          }
          return item.OutStoreCount > 0
        })
        msg && this.$message.info(msg)
        this.returning = true

        RawReturnByReceipt({
          Receipt: form,
          Sub: curData
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '退货成功',
              type: 'success'
            })
            this.closeView()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.returning = false
        })
      })
    },

    // 保存form属性到localStorage
    setFormLocal(field, value) {
      let m = localStorage.getItem('materialInboundDetailForm')
      if (m) {
        m = JSON.parse(m)
      } else {
        m = {}
      }
      m[field] = value
      localStorage.setItem('materialInboundDetailForm', JSON.stringify(m))
      if (field === 'Weight_Method') {
        this.$refs.xTable.updateFooter()
        this.tableData.forEach(row => {
          this.countTaxUnitPrice(row, 'Theory_Weight')
        })
      }
    },
    getFormLocal() {
      let m = localStorage.getItem('materialInboundDetailForm')
      if (m) {
        m = JSON.parse(m)
        for (const key in m) {
          this.form[key] = m[key]
        }
      }
    },
    // 保存调整金额
    handleSubmitAdjustAmount() {
      // 验证表单
      this.$refs.adjustAmountFormRef.validate((valid) => {
        if (!valid) {
          this.$message.error('请完善必填信息')
          return
        }
        this.submitAdjustAmount()
      })
    },
    submitAdjustAmount() {
      const d = this.tableData.map(item => {
        return {
          MoneyAdjustId: this.form.Id,
          StoreSubId: item.Sub_Id,
          Code: item.RawCode,
          Name: item.RawName,
          SysProjectId: item.SysProjectId,
          ProjectId: item.ProjectId,
          AdjustMoney: this.isInboundAdjustAmount ? item.Adjust_Amount_In : item.Adjust_Amount_Out
        }
      }).filter(i => i.AdjustMoney && i.AdjustMoney !== '0')
      if (!d.length) {
        this.$message.error('请输入调整金额')
        return
      }
      this.submitLoading = true
      const model = {
        StoreType: this.isInboundAdjustAmount ? 1 : 0, // 出入库类型 1入库 0 出库
        MoneyAdjustDate: this.adjustAmountForm.MoneyAdjustDate,
        StoreId: this.form.Id,
        StoreNo: this.$route.query.id,
        MaterielType: this.materialType,
        Remark: this.adjustAmountForm.Remark,
        PurchaseContractNo: this.form.Purchase_Contract_No,
        Detail: d
      }
      StoreMoneyAdjust({
        model
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 保存补录发票信息
    async handleSubmitInvoice() {
      await this.$refs['form'].validate()
      UpdateInvoiceInfo({
        MaterialType: this.materialType,
        InStoreId: this.form.Id,
        HasInvoice: this.form.Has_Invoice,
        InvoiceNo: this.form.Invoice_No,
        InvoiceMakeTime: this.form.Invoice_Make_Time,
        InvoiceReceiveTime: this.form.Invoice_Receive_Time
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    // 合计
    footerMethod({ columns, data }) {
      const footerData = [
        columns.map((column, index) => {
          if (column.field === 'InStoreWeight') {
            return this.sumNum(data, this.form.Weight_Method == 0 ? 'Theory_Weight' : 'Voucher_Weight', 5)
          }
          if (INBOUND_DETAIL_SUMMARY_FIELDS.includes(column.field)) {
            return this.sumNum(data, column.field, 5)
          }
          if (index === 0) {
            return '合计'
          }
          return null
        })
      ]
      return footerData
    },
    // 进行合计
    sumNum(costForm, field, digit) {
      let total = 0
      for (let i = 0; i < costForm.length; i++) {
        total += Number(costForm[i][field]) || 0
      }
      return total.toFixed(digit) / 1
    },
    searchCustomField(val) {
      this.handleSearch(val)
    },
    // 理重同步至凭证重
    syncWeight() {
      this.tableData.forEach(item => {
        this.$set(item, 'Voucher_Weight', item.Theory_Weight)
        this.countTaxUnitPrice(item, 'Voucher_Weight')
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  .box-card-tb {
    flex: 1;
    margin-top: 8px;
  }
}
// 表格工具栏css
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  ::v-deep .el-radio-group {
    width: 400px;
  }
  .toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    span {
      display: inline-block;
      width: 2px;
      height: 14px;
      background: #009dff;
      margin-right: 6px;
      vertical-align: text-top;
    }
  }
  .search-form {
    ::v-deep {
      .el-form-item--small {
        margin-bottom: 8px;
      }
      .el-form-item__content {
        width: 110px;
      }
      .last-btn {
        .el-form-item__content {
          width: 320px;
        }
      }
      .last-btn.el-form-item{
        margin-right: 0 ;
      }
    }
  }
  .statistics-container {
    display: flex;
    .statistics-item {
      margin-right: 32px;
      span:first-child {
        display: inline-block;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        color: #999999;
        margin-right: 16px !important;
      }
      span:last-child {
        font-size: 18px;
        font-weight: 600;
        color: #00c361;
      }
    }
  }
}
// 滚动条css
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #e5e5e5;
}
::-webkit-scrollbar-thumb {
  background-color: #bbbbbb;
  border-radius: 5px;
}
// 表格配置css
.popover-container {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 12px 0;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    .handle {
      margin-right: 12px;
    }
  }
}
.el-card {
  ::v-deep {
    .el-card__body {
      display: flex;
      flex-direction: column;
    }
  }

  .tb-x {
    flex: 1;
    margin-bottom: 10px;
    overflow: auto;
  }
}

::v-deep .elDivder {
  margin: 10px 0;
}

.pagination-container {
  text-align: right;
  margin-top: 10px;
  padding: 0;
}

.upload-file-list {
  & > div {
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    position: relative;
    i {
      margin-right: 10px;
    }
    i:last-child {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      color: #999999;
      font-size: 18px;
    }
  }
  & > div:hover {
    background-color: #f8f8f8;
    i:last-child {
      color: #298dff;
    }
  }
}

.vxe-toolbar {
  padding: 0;
}

footer {
  display: flex;
  justify-content: space-between;
}

// 解决日期选择器删除icon不显示问题
::v-deep {
  .el-date-editor {
    .el-icon-circle-close {
      color: #c0c4cc;
    }
  }
}

::v-deep {
  .input-number {
    input {
      padding-right: 2px;
    }
  }
}
</style>

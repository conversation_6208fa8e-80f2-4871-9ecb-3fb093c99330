<template>
  <div class="app-container abs100">
    <el-card class="box-card">
      <el-form ref="detail" class="detail" :model="detail" :rules="rules" inline>
        <el-form-item label="单号">
          <el-input v-if="pickType==1" v-model="detail.Pick_No" />
          <span v-else>{{ isAdd?'自动生成':detail.Pick_No }}</span>
        </el-form-item>
        <el-form-item label="领用工序" prop="Pick_Process_Id">
          <SelectProcess v-model="detail.Pick_Process_Id" />
        </el-form-item>
        <el-form-item label="性质" prop="Nature">
          <el-select v-model="detail.Nature">
            <el-option label="内调" value="内调" />
            <el-option label="转运" value="转运" />
          </el-select>
        </el-form-item>
        <el-form-item label="领用班组" prop="Pick_Team_Id">
          <SelectTeam v-model="detail.Pick_Team_Id" />
        </el-form-item>
        <el-form-item label="仓库" prop="Warehouse_Id">
          <SelectWarehouse v-model="detail.Warehouse_Id" :type="isRaw ? 0 : 1" />
        </el-form-item>
        <el-form-item label="批号">
          <el-input v-model="detail.Batch_No" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card box-card-tb">
      <div class="toolbar-container" style="margin-bottom: 8px">
        <vxe-toolbar>
          <template v-if="pickType===0" #buttons>
            <el-button type="primary" @click="openAddDialog(null)">新增</el-button>
            <el-button
              :disabled="!multipleSelection.length"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
          </template>
        </vxe-toolbar>
        <DynamicTableFields
          v-if="columns"
          title="表格配置"
          :table-columns="columns"
          :table-config-code="gridCode"
          @updateColumn="getDetailGrid"
        />
      </div>
      <div v-loading="!showTable" class="tb-x">
        <vxe-table
          v-if="showTable"
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true, keyField:'index'}"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading"
          :auto-resize="true"
          stripe
          size="medium"
          :data="tableData"
          resizable
          :edit-config="{
            trigger: 'click',
            mode: 'cell'
          }"
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column fixed="left" type="checkbox" width="60" title="" />
          <vxe-column type="seq" title="序号" width="60" fixed="left" align="center" />
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              show-overflow="tooltip"
              :align="item.Align"
              :field="item.Code"
              :visible="item.Is_Display"
              :title="item.Is_Must_Input ? '*' + item.Display_Name : item.Display_Name"
              :min-width="item.Width"
              :edit-render="item.Is_Edit ? {} : null"
            >
              <template #default="{ row }">
                <span> {{ row[item.Code] | displayValue }}</span>
              </template>
              <template #edit="{ row }">
                <div v-if="item.Code === 'Remark'">
                  <el-input v-model="row[item.Code]" />
                </div>
                <div v-if="item.Code === 'Plan_Count'">
                  <el-input v-model="row[item.Code]" v-inp-num="{ toFixed: 0, min: 1,max: row.StoreCount }" />
                </div>
              </template>
            </vxe-column>
          </template>

        </vxe-table>
      </div>
      <el-divider class="elDivder" />
      <footer style="justify-content: flex-end">
        <el-button @click="closeView">取消</el-button>
        <el-button
          v-if="pickType===0"
          :loading="saveLoading"
          :disabled="submitLoading"
          @click="handleSubmit(0)"
        >保存草稿
        </el-button>
        <el-button type="primary" :disabled="saveLoading" :loading="submitLoading" @click="handleSubmit(1)">提交</el-button>
      </footer>
    </el-card>

    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      title="新增"
      :visible.sync="dialogVisible"
      width="70%"
      top="10vh"
      @close="closeDialog"
    >
      <BimForm
        ref="bimform"
        v-model="form"
        :form-items="formItems"
        :rules="rules"
        @search="searchStock"
        @reset="resetStock"
      >
        <el-button type="primary" @click="addList(false)">加入列表</el-button>
      </BimForm>
      <div style="height: calc(100vh - 550px)">
        <vxe-table
          ref="xTable2"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true, keyField:'index'}"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading2"
          :auto-resize="true"
          stripe
          size="medium"
          :data="pickingData"
          resizable
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
          }"
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange2"
          @checkbox-change="tbSelectChange2"
        >
          <vxe-column fixed="left" type="checkbox" width="60" title="" />
          <vxe-column type="seq" title="序号" width="60" fixed="left" align="center" />
          <template v-for="item in columns2">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              show-overflow="tooltip"
              :align="item.Align"
              :field="item.Code"
              :visible="item.Is_Display"
              :title="item.Is_Must_Input ? '*' + item.Display_Name : item.Display_Name"
              :min-width="item.Width"
              :edit-render="item.Is_Edit ? {} : null"
            >
              <template #default="{ row }">
                <span> {{ row[item.Code] | displayValue }}</span>
              </template>
              <template #edit="{ row }">
                <div v-if="item.Code === 'Plan_Count'">
                  <el-input v-model="row[item.Code]" v-inp-num="{ toFixed: 0, min: 1,max: row.StoreCount }" @change="$emit('updateRow')" />
                </div>
              </template>
            </vxe-column>
          </template>

        </vxe-table>
      </div>
      <div class="button">
        <Pagination
          :total="total"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          @pagination="pageChange"
        />
        <div style="margin-top: 10px">
          <el-button @click="closeDialog()">取消</el-button>
          <el-button
            type="primary"
            :disabled="!multipleSelection2.length"
            @click="addList"
          >保存
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { closeTagView } from '@/utils'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetPickingDetail, GetPickPlate, GetStoreSelectPage, SavePicking } from '@/api/PRO/materialManagement'
import { GetGridByCode } from '@/api/sys'
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import BimForm from '@/components/BimForm/index.vue'
import SelectProject from '@/components/Select/SelectProject/index.vue'
import SelectMaterialType from '@/components/Select/SelectMaterialType/index.vue'
import SelectProcess from '@/components/Select/SelectProcess/index.vue'
import SelectTeam from '@/components/Select/SelectTeam/index.vue'
import SelectWarehouse from '@/components/Select/SelectWarehouse/index.vue'

const form = {
  Raw_FullName: '',
  CategoryId: '',
  RawMaterial: '',
  SysProjectId: '',
  RawSpec: '',
  RawWidth: '',
  RawLength: ''
}

export default {
  components: {
    SelectWarehouse,
    SelectTeam,
    SelectProcess,
    BimForm,
    Pagination,
    DynamicTableFields,
    SelectMaterialType
  },
  data() {
    return {
      detail: {
        Batch_No: '',
        Pick_Process_Id: '',
        Pick_Team_Id: '',
        Pick_No: '',
        Warehouse_Id: '',
        Nature: '内调'
      },
      columns: [],
      form: { ...form },
      rules: {
        Pick_Process_Id: [{ required: true, message: '请选择', trigger: 'change' }],
        Pick_Team_Id: [{ required: true, message: '请选择', trigger: 'change' }],
        Warehouse_Id: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      dWidth: '60%',
      // 原料各类型表格校验规则
      showTable: false,
      dialogVisible: false,
      tbLoading: false,
      tbLoading2: false,
      saveLoading: false,
      submitLoading: false,
      tableData: [],
      multipleSelection: [],
      multipleSelection2: [],
      columns2: [],
      pickingData: [],
      stockData: [],
      tablePageSize: tablePageSize,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      total: 0,
      pickType: this.$route.query.pickType == 1 ? 1 : 0,
      factoryId: localStorage.getItem('CurReferenceId')
    }
  },
  computed: {
    isAdd() {
      return this.$route.name === 'AddMaterialPickList' || this.$route.name === 'AddMaterialPickAuxList'
    },
    isEdit() {
      return this.$route.name === 'EditMaterialPickList'
    },
    isRaw() {
      return this.$route.query.type == 0
    },
    materialType() {
      return this.$route.query.type
    },
    gridCode() {
      return this.isRaw ? 'pickListDetail' : 'pickListDetailAux'
    },
    gridCode2() {
      return this.isRaw ? 'pickStock' : 'pickStockAux'
    },
    formItems() {
      return this.isRaw ? [
        {
          label: '原料全名',
          prop: 'Raw_FullName',
          type: 'input',
          placeholder: '通配符%'
        },
        {
          label: '原料分类',
          prop: 'CategoryId',
          type: 'component',
          component: SelectMaterialType
        },
        {
          label: '材质',
          prop: 'RawMaterial',
          type: 'input'
        },
        {
          label: '项目名称',
          prop: 'SysProjectId',
          type: 'component',
          component: SelectProject
        },
        {
          label: '规格',
          prop: 'RawSpec',
          type: 'input'
        },
        {
          label: '宽度',
          prop: 'RawWidth',
          type: 'input'
        },
        {
          label: '长度',
          prop: 'RawLength',
          type: 'input'
        }
      ] : [
        {
          label: '辅料名称',
          prop: 'Raw_FullName',
          type: 'input',
          placeholder: '请输入'
        },
        {
          label: '辅料分类',
          prop: 'CategoryId',
          type: 'component',
          component: SelectMaterialType,
          props: {
            isRaw: false
          }
        },
        {
          label: '项目名称',
          prop: 'SysProjectId',
          type: 'component',
          component: SelectProject
        },
        {
          label: '规格',
          prop: 'RawSpec',
          type: 'input'
        }
      ]
    }

  },
  watch: {
    // 监听领用工序变化，保存到localStorage
    'detail.Pick_Process_Id'(newVal) {
      if (newVal && this.isAdd) {
        const factoryId = localStorage.getItem('CurReferenceId')
        localStorage.setItem(`pickApply_Pick_Process_Id_${factoryId}`, newVal)
      }
    },
    // 监听领用班组变化，保存到localStorage
    'detail.Pick_Team_Id'(newVal) {
      if (newVal && this.isAdd) {
        const factoryId = localStorage.getItem('CurReferenceId')
        localStorage.setItem(`pickApply_Pick_Team_Id_${factoryId}`, newVal)
      }
    },
    // 监听领用班组变化，保存到localStorage
    'detail.Warehouse_Id'(newVal) {
      if (newVal && this.isAdd) {
        const factoryId = localStorage.getItem('CurReferenceId')
        localStorage.setItem(`pickApply_Warehouse_Id_${this.isRaw}_${factoryId}`, newVal)
      }
    }
  },
  created() {
    console.log(this.$route.name)
    this.getDetailGrid()
    this.getStockGrid()
    if (this.isEdit) {
      this.getInfo()
    } else if (this.isAdd) {
      // 新增时从localStorage恢复上次的选择
      this.loadSavedSelections()
    }
    if (this.pickType === 1) {
      this.getPickPlate()
    }
  },

  methods: {
    async getPickPlate() {
      const res = await GetPickPlate({
        ids: this.$route.query.ids
      })
      this.tableData = res.Data.map(item => {
        item.Project_Id = item.Sys_Project_Id
        item.Raw_Name = item.RawName
        item.Raw_Spec = item.RawSpec
        item.Raw_Length = item.RawLength
        item.Raw_Width = item.RawWidth
        item.Raw_Material = item.RawMaterial
        item.Category_Name = item.CategoryName
        item.Id = item.Raw_Code + item.Project_Id + item.RawLength + item.RawWidth
        return item
      })
    },
    getInfo() {
      GetPickingDetail({
        PickingId: this.$route.params.id,
        MaterialType: this.materialType
      }).then(res => {
        this.tableData = res.Data.Subs.map(item => {
          item.Id = item.Raw_Code + item.Project_Id + item.RawLength + item.RawWidth
          return item
        })
        this.detail = {
          Pick_Process_Id: res.Data.Pick_Process_Id,
          Pick_Team_Id: res.Data.Pick_Team_Id,
          Warehouse_Id: res.Data.Warehouse_Id,
          Batch_No: res.Data.Batch_No,
          Pick_No: res.Data.Pick_No,
          Id: res.Data.Id
        }
      })
    },
    getDetailGrid() {
      this.showTable = false
      GetGridByCode({
        code: this.gridCode
      }).then(res => {
        this.columns = res.Data.ColumnList
        this.showTable = true
      })
    },
    pageChange({ page, limit, type }) {
      console.log(page, limit, type)
      if (type === 'limit') {
        this.queryInfo.Page = 1
      } else {
        this.queryInfo.Page = page
      }
      this.queryInfo.PageSize = limit
      this.fetchStock()
    },
    getStockGrid() {
      GetGridByCode({
        code: this.gridCode2
      }).then(res => {
        this.columns2 = res.Data.ColumnList
      })
    },
    searchStock() {
      this.queryInfo.Page = 1
      this.fetchStock()
    },
    resetStock() {
      this.form = { ...form }
      this.searchStock()
    },
    fetchStock() {
      this.pickingData = []
      this.tbLoading2 = true
      GetStoreSelectPage({
        pageInfo: { ...this.queryInfo },
        MaterialType: this.materialType,
        ...this.form
      }).then(res => {
        // 获取到数据后，过滤掉表格中已经存在的数据
        const existingIds = this.tableData.map(item => item.Id)
        this.pickingData = res.Data.Data.filter(item => {
          const itemId = item.Raw_Code + item.Sys_Project_Id + item.Sys_Project_Id + item.RawLength + item.RawWidth
          return !existingIds.includes(itemId)
        })
        this.total = res.Data.TotalCount
      }).finally(() => {
        this.tbLoading2 = false
      })
    },
    closeDialog() {
      this.dialogVisible = false
    },
    // 打开新增弹窗
    openAddDialog() {
      this.fetchStock()
      this.dialogVisible = true
    },
    addList(close = true) {
      const arr = this.multipleSelection2.map(item => {
        item.Project_Id = item.Sys_Project_Id
        item.Raw_Name = item.RawName
        item.Raw_Spec = item.RawSpec
        item.Raw_Length = item.RawLength
        item.Raw_Width = item.RawWidth
        item.Raw_Material = item.RawMaterial
        item.Category_Name = item.CategoryName
        item.Id = item.Raw_Code + item.Project_Id + item.RawLength + item.RawWidth
        return item
      })
      // 将数据加入tableData，如果数据已存在则覆盖原数据
      arr.forEach(newItem => {
        const existingIndex = this.tableData.findIndex(existingItem => existingItem.Id === newItem.Id)
        if (existingIndex !== -1) {
          // 如果数据已存在，覆盖原数据
          this.$set(this.tableData, existingIndex, newItem)
        } else {
          // 如果数据不存在，添加新数据
          this.tableData.push(newItem)
        }
      })
      if (close) {
        this.closeDialog()
      } else {
        this.$message.success('添加成功')
      }
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    tbSelectChange2(array) {
      this.multipleSelection2 = array.records
    },

    closeView() {
      closeTagView(this.$store, this.$route)
    },

    // 从localStorage加载保存的选择
    loadSavedSelections() {
      const factoryId = localStorage.getItem('CurReferenceId')
      const savedProcessId = localStorage.getItem(`pickApply_Pick_Process_Id_${factoryId}`)
      const savedTeamId = localStorage.getItem(`pickApply_Pick_Team_Id_${factoryId}`)
      const savedWarehouseId = localStorage.getItem(`pickApply_Warehouse_Id_${this.isRaw}_${factoryId}`)

      if (savedProcessId) {
        this.detail.Pick_Process_Id = savedProcessId
      }

      if (savedTeamId) {
        this.detail.Pick_Team_Id = savedTeamId
      }
      if (savedWarehouseId) {
        this.detail.Warehouse_Id = savedWarehouseId
      }
    },

    handleDelete() {
      this.tableData = this.tableData.filter(item => !this.multipleSelection.find(i => i.Id === item.Id))
    },
    /* *
     * 提交入库
     */
    async handleSubmit(Status) {
      await this.$refs.detail.validate()
      if (!this.tableData.length) {
        this.$message({
          message: '请选择添加明细',
          type: 'error'
        })
        return
      }
      if (this.tableData.some(i => !i.Plan_Count)) {
        this.$message.error('计划数量不允许存在为0的情况')
        return
      }
      this.submitLoading = true
      SavePicking({
        Pick_Type: this.pickType, // 0手动生成 1套料生成
        ...this.detail,
        Status,
        Material_Type: this.materialType,
        Subs: this.tableData.map(item => {
          delete item.Id
          return item
        })
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.closeView()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  .box-card-tb {
    flex: 1;
    margin-top: 8px;
  }
}
// 表格工具栏css
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  ::v-deep .el-radio-group {
    width: 400px;
  }
  .toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    span {
      display: inline-block;
      width: 2px;
      height: 14px;
      background: #009dff;
      margin-right: 6px;
      vertical-align: text-top;
    }
  }
  .search-form {
    ::v-deep {
      .el-form-item--small {
        margin-bottom: 8px;
      }
      .el-form-item__content {
        width: 110px;
      }
      .last-btn {
        .el-form-item__content {
          width: 320px;
        }
      }
      .last-btn.el-form-item{
        margin-right: 0 ;
      }
    }
  }
  .statistics-container {
    display: flex;
    .statistics-item {
      margin-right: 32px;
      span:first-child {
        display: inline-block;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        color: #999999;
        margin-right: 16px !important;
      }
      span:last-child {
        font-size: 18px;
        font-weight: 600;
        color: #00c361;
      }
    }
  }
}
// 滚动条css
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #e5e5e5;
}
::-webkit-scrollbar-thumb {
  background-color: #bbbbbb;
  border-radius: 5px;
}
// 表格配置css
.popover-container {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 12px 0;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    .handle {
      margin-right: 12px;
    }
  }
}
.el-card {
  ::v-deep {
    .el-card__body {
      display: flex;
      flex-direction: column;
    }
  }

  .tb-x {
    flex: 1;
    margin-bottom: 10px;
    overflow: auto;
  }
}

::v-deep .elDivder {
  margin: 10px 0;
}

.pagination-container {
  text-align: right;
  margin-top: 10px;
  padding: 0;
}

.upload-file-list {
  & > div {
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    position: relative;
    i {
      margin-right: 10px;
    }
    i:last-child {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      color: #999999;
      font-size: 18px;
    }
  }
  & > div:hover {
    background-color: #f8f8f8;
    i:last-child {
      color: #298dff;
    }
  }
}

.vxe-toolbar {
  padding: 0;
}

footer {
  display: flex;
  justify-content: space-between;
}

// 解决日期选择器删除icon不显示问题
::v-deep {
  .el-date-editor {
    .el-icon-circle-close {
      color: #c0c4cc;
    }
  }
  .detail .el-form-item--small.el-form-item{
    margin-bottom: 0;
  }
}

::v-deep {
  .input-number {
    input {
      padding-right: 2px;
    }
  }
}
.button{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

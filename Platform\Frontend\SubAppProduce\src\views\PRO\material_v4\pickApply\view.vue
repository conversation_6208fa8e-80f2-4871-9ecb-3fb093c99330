<template>
  <div class="app-container abs100">
    <el-card class="box-card box-card-tb">
      <div class="toolbar-container" style="margin-bottom: 8px">
        <el-form inline>
          <el-form-item label="单号">{{ info.Pick_No }}</el-form-item>
          <el-form-item style="margin-left:10px" label="领用工序">{{ info.PickingProcessName }}</el-form-item>
          <el-form-item style="margin-left:10px" label="领用班组">{{ info.PickingTeamName }}</el-form-item>
          <el-form-item style="margin-left:10px" label="性质">{{ info.Nature }}</el-form-item>
          <el-form-item style="margin-left:10px" label="仓库">{{ info.WarehouseName }}</el-form-item>
          <el-form-item style="margin-left:10px" label="批号">{{ info.Batch_No }}</el-form-item>
        </el-form>
        <el-form inline style="margin-left: auto">
          <el-form-item :label="materialName + '名称'" style="margin-left: auto">
            <el-input v-model="searchForm.name" clearable placeholder="请输入" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" clearable>
              <el-option label="已完成" value="已完成" />
              <el-option label="未完成" value="未完成" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">搜索</el-button>
            <el-button type="success" @click="exportExcel">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-divider />
      <div style="margin-bottom: 10px;display: flex">
        <template v-if="isLock">
          <el-button type="primary" :disabled="!multipleSelection.length" @click="lockStock('lock')">锁定库存</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="lockStock('unlock')">释放库存</el-button>
        </template>
        <DynamicTableFields
          v-if="columns"
          style="margin-left: auto"
          title="表格配置"
          :table-columns="columns"
          :table-config-code="gridCode"
          @updateColumn="getGrid"
        />
      </div>
      <div v-loading="!showTable" class="tb-x">
        <vxe-table
          v-if="showTable"
          ref="xTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          class="cs-vxe-table"
          :row-config="{ isCurrent: true, isHover: true, keyField:'index'}"
          align="left"
          height="auto"
          show-overflow
          :loading="tbLoading"
          :auto-resize="true"
          stripe
          size="medium"
          :data="tableData"
          resizable
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: true
          }"
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column v-if="!isView" fixed="left" type="checkbox" width="60" title="" />
          <vxe-column type="seq" title="序号" width="60" fixed="left" align="center" />
          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              show-overflow="tooltip"
              :align="item.Align"
              :field="item.Code"
              :visible="item.Is_Display"
              :title="item.Is_Must_Input ? '*' + item.Display_Name : item.Display_Name"
              :min-width="item.Width"
              :edit-render="item.Is_Edit ? {} : null"
            >
              <template #default="{ row }">
                <template v-if="['Plan_Count','Locked_Count','Picked_Count'].includes(item.Code)">
                  <el-button type="text" @click="handleView(item.Code,row)"> {{ row[item.Code] | displayValue }}</el-button>
                </template>
                <template v-else-if="['StatusName'].includes(item.Code)">
                  <el-tag :type="row[item.Code]==='已完成'?'success':'danger'"> {{ row[item.Code] | displayValue }}</el-tag>
                </template>
                <template v-else>
                  <span> {{ row[item.Code] | displayValue }}</span>
                </template>
              </template>
            </vxe-column>
          </template>

        </vxe-table>
      </div>
    </el-card>
    <el-dialog
      v-dialogDrag
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="800px"
      top="10vh"
      @close="closeDialog"
    >
      <div class="plm-bimtable">
        <el-table
          :data="detailData"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column v-if="info.Pick_Type" label="排版编号" prop="TypeSettingNo" />
          <el-table-column label="领料单号" prop="PickingNo" min-width="170px" />
          <el-table-column v-if="detailType==='Plan_Count'" label="计划领用数量" prop="PlanCount" />
          <el-table-column v-if="detailType==='Locked_Count'" label="库存锁定数量" prop="LockCount" />
          <template v-if="detailType==='Picked_Count'">
            <el-table-column label="出库数量" prop="PickCount" />
            <el-table-column v-if="isRaw" label="炉批号" prop="RollNo" />
            <el-table-column label="仓库/库位" prop="WarehouseName" min-width="150px" />
            <el-table-column label="出库单号" prop="OutStoreNo" min-width="150px" />
          </template>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { GetGridByCode } from '@/api/sys'
import {
  ExportPicking,
  GetPickingDetail,
  GetPickingTypeSettingDetail,
  LockPicking,
  UnLockPicking
} from '@/api/PRO/materialManagement'

export default {
  components: {
    DynamicTableFields
  },
  data() {
    return {
      columns: [],
      showTable: false,
      tableData: [],
      gridCode: '',
      tbLoading: false,
      dialogVisible: false,
      detailData: [],
      oriData: [],
      detailType: '',
      info: {},
      searchForm: {
        status: '',
        name: ''
      },
      multipleSelection: [],
      materialName: ''
    }
  },
  computed: {
    isView() {
      return this.$route.name === 'ViewMaterialPickList'
    },
    isLock() {
      return this.$route.name === 'LockMaterialPickList'
    },
    isRaw() {
      return this.$route.query.type == 0
    }
  },

  created() {
    this.gridCode = this.$route.query.type == 0 ? 'pickListView' : 'pickListViewAux'
    this.materialName = this.$route.query.type == 0 ? '原料' : '辅料'
    this.getGrid()
    this.getInfo()
  },

  methods: {
    getGrid() {
      this.showTable = false
      GetGridByCode({
        code: this.gridCode
      }).then(res => {
        this.columns = res.Data.ColumnList
        this.showTable = true
      })
    },
    getInfo() {
      GetPickingDetail({
        PickingId: this.$route.params.id,
        MaterialType: this.$route.query.type
      }).then(res => {
        this.tableData = res.Data.Subs
        this.oriData = JSON.parse(JSON.stringify(res.Data.Subs))
        this.info = res.Data
      })
    },
    handleView(code, row) {
      this.detailType = code
      GetPickingTypeSettingDetail({
        PickingId: this.$route.params.id,
        PickingSubId: row.Id,
        Type: ['Plan_Count', 'Locked_Count', 'Picked_Count'].indexOf(code),
        MaterialType: this.$route.query.type
      }).then(res => {
        this.detailData = res.Data
        this.dialogVisible = true
      })
    },
    closeDialog() {
      this.dialogVisible = false
    },
    search() {
      console.log(this.oriData)
      this.tableData = this.oriData.filter(row => {
        let flag = true
        if (this.searchForm.status) {
          flag = flag && (row.StatusName === this.searchForm.status)
        }
        if (this.searchForm.name) {
          flag = flag && (row.RawName.includes(this.searchForm.name))
        }
        return flag
      })
    },
    tbSelectChange(array) {
      this.multipleSelection = array.records
    },
    exportExcel() {
      ExportPicking({
        sendId: this.$route.params.id
      }).then(res => {
        if (res.IsSucceed) {
          window.open(this.$baseUrl + res.Data)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    lockStock(type) {
      const api = type === 'lock' ? LockPicking : UnLockPicking
      api({
        PickingId: this.$route.params.id,
        MaterialType: this.$route.query.type,
        Subs: this.multipleSelection.map(item => {
          return {
            PickingSubId: item.Id,
            FreeCount: item.StoreCount,
            PlanCount: item.Plan_Count,
            LockCount: item.Locked_Count,
            PickedCount: item.Picked_Count
          }
        })
      }).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.getInfo()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  .box-card-tb {
    flex: 1;
    margin-top: 8px;
  }
}
// 表格工具栏css
.toolbar-container {
  display: flex;
  align-items: center;
  width: 100%;
  ::v-deep .el-radio-group {
    width: 400px;
  }
  .toolbar-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    span {
      display: inline-block;
      width: 2px;
      height: 14px;
      background: #009dff;
      margin-right: 6px;
      vertical-align: text-top;
    }
  }
  .search-form {
    ::v-deep {
      .el-form-item--small {
        margin-bottom: 8px;
      }
      .el-form-item__content {
        width: 110px;
      }
      .last-btn {
        .el-form-item__content {
          width: 320px;
        }
      }
      .last-btn.el-form-item{
        margin-right: 0 ;
      }
    }
  }
  .statistics-container {
    display: flex;
    .statistics-item {
      margin-right: 32px;
      span:first-child {
        display: inline-block;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        color: #999999;
        margin-right: 16px !important;
      }
      span:last-child {
        font-size: 18px;
        font-weight: 600;
        color: #00c361;
      }
    }
  }
}
// 滚动条css
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #e5e5e5;
}
::-webkit-scrollbar-thumb {
  background-color: #bbbbbb;
  border-radius: 5px;
}
// 表格配置css
.popover-container {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  margin: 12px 0;
  .item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    .handle {
      margin-right: 12px;
    }
  }
}
.el-card {
  ::v-deep {
    .el-card__body {
      display: flex;
      flex-direction: column;
    }
  }

  .tb-x {
    flex: 1;
    margin-bottom: 10px;
    overflow: auto;
  }
}

::v-deep .elDivder {
  margin: 10px 0;
}

.pagination-container {
  text-align: right;
  margin-top: 10px;
  padding: 0;
}

.upload-file-list {
  & > div {
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    position: relative;
    i {
      margin-right: 10px;
    }
    i:last-child {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 15px;
      color: #999999;
      font-size: 18px;
    }
  }
  & > div:hover {
    background-color: #f8f8f8;
    i:last-child {
      color: #298dff;
    }
  }
}

.vxe-toolbar {
  padding: 0;
}

footer {
  display: flex;
  justify-content: space-between;
}

// 解决日期选择器删除icon不显示问题
::v-deep {
  .el-date-editor {
    .el-icon-circle-close {
      color: #c0c4cc;
    }
  }
  .el-form-item--small.el-form-item{
    margin-bottom: 0;
  }
}

::v-deep {
  .input-number {
    input {
      padding-right: 2px;
    }
  }
}
.button{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>

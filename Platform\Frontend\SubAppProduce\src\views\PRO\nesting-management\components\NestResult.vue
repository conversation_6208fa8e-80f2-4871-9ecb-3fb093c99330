<template>
  <div>
    <el-alert
      type="warning"
      :closable="false"
    >
      <template>
        <span class="cs-label">注意：请先 <el-link
          type="primary"
          :underline="false"
          @click="handleExport"
        >点击下载模板</el-link></span>
      </template>
    </el-alert>

    <div class="cs-upload-x">
      <upload-excel ref="upload" :before-upload="beforeUpload" />
    </div>
    <div slot="footer" class="dialog-footer" style="text-align: right;">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button
        type="primary"
        :loading="btnLoading"
        @click="handleSubmit"
      >确 定
      </el-button>
    </div>
  </div>
</template>

<script>
import { combineURL } from '@/utils'
import { GetPlateNestingResultImportFile, ImportPlateNestingResult } from '@/api/PRO/production-task'
import UploadExcel from '@/components/UploadExcel/index.vue'

export default {
  components: { UploadExcel },
  data() {
    return {
      btnLoading: false
    }
  },
  methods: {
    beforeUpload(file) {
      this.btnLoading = true
      const fileFormData = new FormData()
      fileFormData.append('Files', file)
      ImportPlateNestingResult(fileFormData).then(res => {
        if (res.IsSucceed) {
          this.$message({
            message: '导入成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('refresh')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          res.Data && window.open(combineURL(this.$baseUrl, res.Data))
        }
        this.btnLoading = false
      })
    },
    handleSubmit() {
      this.$refs.upload.handleSubmit()
    },
    handleExport() {
      GetPlateNestingResultImportFile({}).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data))
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.cs-label {
  font-size: 14px;
  display: flex;
  align-items: center;
  white-space: pre-wrap;
}

.cs-upload-x {
  margin: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  .c-upload-container{
    width: 100%;
  }
}

</style>

<template>
  <div class="cs-z-flex-pd16-wrap abs100">
    <div class="cs-z-page-main-content">
      <el-form ref="form" :model="form" inline label-width="80px" style="width: 100%">
        <el-form-item label="排版名称" prop="Nesting_Result_Name">
          <el-input v-model="form.Nesting_Result_Name" placeholder="请输入" clearable type="text" />
        </el-form-item>
        <el-form-item label="原料名称" prop="Raw_Name">
          <el-input v-model="form.Raw_Name" placeholder="请输入" type="text" clearable />
        </el-form-item>
        <el-form-item label="厚度" prop="Thickness" label-width="50px">
          <el-input-number v-model="form.Thickness" :min="0" :max="1000000" class="w100 cs-number-btn-hidden" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="材质" prop="Texture" label-width="50px">
          <el-input v-model="form.Texture" placeholder="请输入" type="text" clearable />
        </el-form-item>
        <el-form-item label="切割状态" prop="Cut_Status">
          <el-select v-model="form.Cut_Status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="待切割" value="待切割" />
            <el-option label="已切割" value="已切割" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="Type" label-width="50px">
          <el-select v-model="form.Type" placeholder="请选择" clearable style="width: 120px">
            <el-option label="手动导入" :value="1" />
            <el-option label="系统推送" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData(1)">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <vxe-toolbar ref="xToolbar">
        <template #buttons>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="toCreatePickList">生成内调单</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="toCreateReturnList">生成余料退库单</el-button>
          <el-button type="success" @click="handleImport">导入套料报告</el-button>
          <el-button type="success" @click="handleImportResult">导入套料结果</el-button>
          <el-button type="success" @click="handleImportThumbs">导入缩略图</el-button>
          <el-button type="danger" :disabled="!multipleSelection.length" @click="handleDelete">删除</el-button>
          <el-button type="primary" :disabled="!multipleSelection.length" @click="handleSchedule">下发排产任务</el-button>
          <el-button type="default" :disabled="!multipleSelection.length" :loading="downloadLoading" @click="handleDownload">导出加工信息</el-button>
          <el-upload
            :action="$baseUrl + 'Pro/Nesting/ImportProcess'"
            :show-file-list="false"
            :headers="headers"
            style="margin: 0 10px"
            accept=".xlsx,.xls"
            :on-success="()=>fetchData(1)"
          >
            <el-button type="default">导入加工信息</el-button>
          </el-upload>
          <el-upload
            :action="$baseUrl + 'Pro/Nesting/ImportProfiles'"
            :show-file-list="false"
            :on-success="uploadSuccess"
            :headers="headers"
            accept=".xlsx,.xls"
          >
            <el-button type="default">导入型材</el-button>
          </el-upload>
          <DynamicTableFields
            style="margin-left: auto"
            title="表格配置"
            table-config-code="PRONestingManagementIndex"
            @updateColumn="getTbConfig"
          />
        </template>
      </vxe-toolbar>

      <div v-loading="tbLoading" class="tb-x">
        <vxe-table
          v-if="!tbLoading"
          ref="xTable"
          class="cs-vxe-table"
          :checkbox-config="{checkField: 'checked'}"
          :empty-render="{name: 'NotData'}"
          :row-config="{ isCurrent: true, isHover: true}"
          align="center"
          height="auto"
          show-overflow
          :auto-resize="true"
          stripe
          size="medium"
          :data="tbData"
          resizable
          :tooltip-config="{ enterable: true }"
          @checkbox-all="tbSelectChange"
          @checkbox-change="tbSelectChange"
        >
          <vxe-column fixed="left" type="checkbox" />

          <template v-for="item in columns">
            <vxe-column
              :key="item.Code"
              :fixed="item.Is_Frozen ? (item.Frozen_Dirction || 'left') : ''"
              show-overflow="tooltip"
              :align="item.Align"
              :field="item.Code"
              :visible="item.Is_Display"
              :title="item.Display_Name"
              :min-width="item.Width"
              :edit-render="item.Is_Edit ? {} : null"
            >
              <template #default="{ row }">
                <span v-if="item.Code === 'Type'">
                  <el-tag v-if="row[item.Code] === 1" effect="plain" type="success">手动导入</el-tag>
                  <el-tag v-else effect="plain" type="warning">自动推送</el-tag>
                </span>
                <span v-else-if="item.Code === 'Surplus_Raw'">
                  <el-button v-if="row.Surplus_Count>0" type="text" @click="handleRaw(row)">查看</el-button>
                </span>
                <span v-else-if="item.Code === 'Part_Amount'">
                  <el-link type="primary" :underline="false" @click="handleAmount(row)">{{ row[item.Code] }}</el-link>
                </span>
                <span v-else-if="item.Code === 'Utilization'">
                  {{ row[item.Code] }}<span v-if="row[item.Code]">%</span>
                </span>
                <span v-else>{{ row[item.Code] }}</span>
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
      <footer class="data-info">
        <el-tag
          size="medium"
          class="info-x"
        >已选 {{ multipleSelection.length }} 条数据
        </el-tag>
        <Pagination
          :total="total"
          max-height="100%"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="pageChange"
        />
      </footer>

      <el-dialog
        v-if="dialogVisible"
        v-dialogDrag
        class="plm-custom-dialog"
        :title="title"
        :visible.sync="dialogVisible"
        :width="width"
        @close="handleClose"
      >
        <component
          :is="currentComponent"
          ref="content"
          @close="handleClose"
          @refresh="fetchData(1)"
        />
      </el-dialog>

    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { tablePageSize } from '@/views/PRO/setting'
import addRouterPage from '@/mixins/add-router-page'
import { DeleteNestingResult, GetNestingResultPageList } from '@/api/PRO/production-task'
import SurplusRaw from './components/SurplusRaw.vue'
import NestReport from './components/NestReport.vue'
import NestResult from './components/NestResult.vue'
import NestThumbs from './components/NestThumbs.vue'
import PartsLayout from './components/PartsLayout.vue'
import { mapActions } from 'vuex'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { ExportProcess } from '@/api/PRO/materialManagement'
import { combineURL } from '@/utils'
import OSSUpload from '@/views/plm/components/ossupload.vue'
import { getToken } from '@/utils/auth'

export default {
  name: 'PRONestingManagement',
  components: { OSSUpload, DynamicTableFields, Pagination, SurplusRaw, NestReport, NestResult, PartsLayout, NestThumbs },
  mixins: [getTbInfo, addRouterPage],
  data() {
    return {
      addPageArray: [
        {
          path: '/material/pick/add',
          hidden: true,
          component: () => import('@/views/PRO/material_v4/pickApply/add.vue'),
          name: 'AddMaterialPickList',
          meta: { title: '新增领料单' },
          query: { pg_redirect: this.$route.name }
        },
        {
          path: this.$route.path + '/requisition',
          hidden: true,
          component: () => import('@/views/PRO/nesting-management/requisition.vue'),
          name: 'ModelCompare',
          meta: { title: '领料单' }
        },
        {
          path: this.$route.path + '/schedule',
          hidden: true,
          component: () => import('@/views/PRO/nesting-management/schedule.vue'),
          name: 'PRONestingSchedule',
          meta: { title: '下发排产任务' }
        }, {
          path: this.$route.path + '/draft',
          hidden: true,
          component: () => import('@/views/PRO/plan-production/schedule-production-new-part/draft'),
          name: 'PRO2PartScheduleDraftNestNew',
          meta: { title: '草稿' }
        }, {
          path: this.$route.path + '/add-return',
          hidden: true,
          component: () => import('@/views/PRO/material-receipt-management/raw-stock-return/add.vue'),
          name: 'PRORawMaterialStockReturnAddReturn',
          meta: { title: '新建退库单' }
        }
      ],
      form: {
        Nesting_Result_Name: '',
        Raw_Name: '',
        Thickness: undefined,
        Texture: '',
        Cut_Status: '',
        Type: undefined
      },
      dialogVisible: false,
      width: '70%',
      title: '',
      currentComponent: '',
      tbLoading: false,
      options: [],
      columns: [],
      tbData: [],
      multipleSelection: [],
      tablePageSize: tablePageSize,
      total: 0,
      queryInfo: {
        Page: 1,
        PageSize: 20
      },
      downloadLoading: false,
      headers: {
        Authorization: getToken(),
        Last_Working_Object_Id: localStorage.getItem('Last_Working_Object_Id')
      }
    }
  },
  mounted() {
    this.getTbConfig()
    this.fetchData(1)
  },
  methods: {
    uploadSuccess(response) {
      if (response.IsSucceed) {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        this.fetchData(1)
      } else {
        this.$message({
          message: response.Message,
          type: 'error'
        })
      }
    },
    async getTbConfig() {
      this.tbLoading = true
      await this.getTableConfig('PRONestingManagementIndex')
      this.tbLoading = false
      const aaa = ['Picking_Bills', 'Out_Bills', 'Machining_Files', 'Thumbnail']
      this.columns = this.columns.filter(item => !aaa.includes(item.Code))
    },
    ...mapActions('schedule', ['changeNestIds']),
    fetchData(page) {
      page && (this.queryInfo.Page = page)
      this.tbLoading = true
      GetNestingResultPageList({
        ...this.queryInfo,
        ...this.form
      }).then(res => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data
          this.total = res.Data.TotalCount
          this.multipleSelection = []
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
    },
    handleSearch() {

    },
    handleImport() {
      this.currentComponent = 'NestReport'
      this.width = '30%'
      this.title = '导入套料报告'
      this.dialogVisible = true
      this.$nextTick(_ => {

      })
    },
    handleImportResult() {
      this.currentComponent = 'NestResult'
      this.width = '30%'
      this.title = '导入套料结果'
      this.dialogVisible = true
      this.$nextTick(_ => {

      })
    },
    handleImportThumbs() {
      this.currentComponent = 'NestThumbs'
      this.width = '30%'
      this.title = '导入缩略图'
      this.dialogVisible = true
    },
    handleDownload() {
      this.downloadLoading = true
      ExportProcess({ ids: this.multipleSelection.map(v => v.Id) }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data))
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.downloadLoading = false
      })
    },
    handleDelete() {
      this.$confirm(' 是否删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteNestingResult({
          ids: this.multipleSelection.map(v => v.Id).toString()
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.fetchData(1)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleSubmit() {

    },
    tbSelectChange(array) {
      console.log('111111')
      this.multipleSelection = array.records
    },
    handleRaw(row) {
      this.width = '70%'
      this.currentComponent = 'SurplusRaw'
      this.title = '余料信息'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].getData(row.Id)
      })
    },
    handleAmount(row) {
      this.width = '70%'
      this.currentComponent = 'PartsLayout'
      this.title = '排版零件'
      this.dialogVisible = true
      this.$nextTick(_ => {
        this.$refs['content'].getData(row.Id)
      })
    },
    handleSchedule() {
      const ids = this.multipleSelection.filter(s => s.Type === 1).map(v => v.Id)
      if (!ids) {
        return
      }
      this.changeNestIds(ids)
      this.$router.push({ name: 'PRO2PartScheduleDraftNestNew', query: { status: 'edit', pg_type: 'part', pg_redirect: this.$route.name, type: '1' }})
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleReset() {
      this.$refs['form'].resetFields()
      this.fetchData(1)
    },
    toCreatePickList() {
      console.log(this.multipleSelection)
      if (this.multipleSelection.some(item => item.PickNo)) {
        this.$message.error('已有内调单号的不能重复生成')
        return
      }
      this.$router.push({
        name: 'AddMaterialPickList',
        query: { pg_redirect: this.$route.name, type: 0, pickType: 1, ids: this.multipleSelection.map(i => i.Id) } // type原料为0,pickType：0手动生成 1套料生成
      })
    },
    toCreateReturnList() {
      this.$router.push({
        name: 'PRORawMaterialStockReturnAddReturn',
        query: { pg_redirect: this.$route.name, isNesting: 1, ids: this.multipleSelection.map(i => i.Id) }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-divider{
  margin:0 0 8px  0;
}
.cs-z-page-main-content{
  display: flex;
  flex-direction: column;
  .tb-x{
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }
  .data-info{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .info-x{
      margin-right: 16px;
    }
  }
}
.pagination-container {
  text-align: right;
  padding: 16px 16px 0 16px;
  margin: 0;
}
.el-form-item{
  margin-bottom: 10px;
}
</style>

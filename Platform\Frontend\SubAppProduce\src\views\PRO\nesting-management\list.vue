<template>
  <div class="app-container abs100">
    <div v-loading="pgLoading" class="h100 app-wrapper" element-loading-text="加载中">
      <ExpandableSection
        v-model="showExpand"
        :width="400"
        class="cs-left fff "
      >
        <div class="inner-wrapper">
          <div class="tree-search">
            <el-select v-model="statusType" clearable class="search-select" placeholder="套料进度" @change="fetchTreeData">
              <el-option label="全部" value="" />
              <el-option label="未完成" value="未完成" />
              <el-option label="已完成" value="已完成" />
            </el-select>
            <el-select v-model="ProcessId" multiple clearable class="search-select" placeholder="套料进度选择（可多选）" style="margin-left: 8px" @change="fetchTreeData">
              <el-option v-for="item in processList" :key="item.ProcessId" :label="item.ProcessName" :value="item.ProcessId" />
            </el-select>
          </div>
          <el-input
            v-model.trim="projectName"
            placeholder="工序任务单号"
            size="small"
            clearable
            suffix-icon="el-icon-search"
            @blur="fetchTreeData"
            @clear="fetchTreeData"
            @keydown.enter.native="fetchTreeData"
          />
          <el-divider class="cs-divider" />
          <div class="tree-x cs-scroll">
            <tree-detail
              ref="tree"
              icon="icon-folder"
              :loading="treeLoading"
              :tree-data="treeData"
              node-key="Id"
              :expanded-key="expandedKey"
              show-checkbox
              @check="handleNodeClick"
            >
              <template #csLabel="{data}">
                <template v-if="!data.Children.length">
                  <span class="cs-blue" />{{ data.Label }}
                  <span :class="['cs-tag',data.Status=='已完成' ? 'greenBg' : 'orangeBg']">
                    <i :class="[data.Status=='已完成' ? 'fourGreen' : 'fourOrange']">{{
                      data.Status == '已完成' ? '已完成' : '未完成'
                    }}</i>
                  </span>
                  <span :class="['cs-tag',data.IsMatched ? 'greenBg' : 'orangeBg']">
                    <i :class="[data.IsMatched ? 'fourGreen' : 'fourOrange']">{{
                      data.IsMatched ? '已关联' : '未关联'
                    }}</i>
                  </span>
                </template>
              </template>
            </tree-detail>
          </div>

        </div>
      </ExpandableSection>
      <div class="cs-right">
        <div ref="searchDom" class="cs-from">
          <div class="cs-search">
            <el-form
              ref="searchParams"
              :model="searchParams"
              class="demo-form-inline"
              inline
            >
              <el-form-item label="项目名称" prop="ProjectName">
                <SelectProject v-model="searchParams.ProjectName" />
              </el-form-item>
              <el-form-item label="批次" prop="BatchNo">
                <el-input v-model="searchParams.BatchNo" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="零件号" prop="PartNo">
                <el-input v-model="searchParams.PartNo" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="套料任务单号" prop="NestCode">
                <el-input v-model="searchParams.NestCode" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="机床" prop="MachineId">
                <SelectDict v-model="searchParams.MachineId" code="nesting_machine" />
              </el-form-item>
              <el-form-item label="套料进度" prop="NestProgress">
                <el-select v-model="searchParams.NestProgress" clearable class="search-select" placeholder="请选择">
                  <el-option label="未完成" value="未完成" />
                  <el-option label="已完成" value="已完成" />
                </el-select>
              </el-form-item>
              <el-form-item label="零件类型" prop="PartType">
                <SelectPartType v-model="searchParams.PartType" />
              </el-form-item>
              <el-form-item class="mb0" label-width="16px">
                <el-button
                  type="primary"
                  @click="handleSearch(false,true)"
                >搜索
                </el-button>
                <el-button @click="handleSearch(true)">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="fff cs-z-tb-wrapper">
          <div class="cs-button-box">
            <ExportCustomReport name="导出清单" code="nest_manage" style="margin-left: 10px" :ids="selectListIds" :json-data="jsonData" />
            <el-button
              type="primary"
              :disabled="!selectListIds.length"
              @click="handleOpenExportDialog('Sigma','导出Sigma文件')"
            >导出Sigma文件
            </el-button>
            <el-button
              type="primary"
              :disabled="!selectListIds.length"
              :loading="lentakLoading"
              @click="submitExport2('Lentak','导出Lentak文件')"
            >导出Lentak文件
            </el-button>
            <el-button
              type="primary"
              :disabled="!selectListIds.length"
              :loading="profilesLoading"
              @click="submitExport2('Profiles','导出型材套料文件')"
            >导出型材套料文件
            </el-button>
            <el-button
              type="primary"
              :disabled="!selectListIds.length"
              :loading="bochuLoading"
              @click="submitExport2('Bochu','推送柏楚套料')"
            >推送柏楚套料
            </el-button>
            <el-button
              type="primary"
              :disabled="!selectListIds.length"
              @click="assignTheMachine"
            >分配机床
            </el-button>
            <DynamicTableFields
              style="margin-left: auto"
              title="表格配置"
              table-config-code="NestingBillTable"
              @updateColumn="updateColumns"
            />
          </div>
          <div v-loading="tbLoading" class="tb-container">
            <vxe-table
              v-if="!tbLoading"
              element-loading-spinner="el-icon-loading"
              element-loading-text="拼命加载中"
              empty-text="暂无数据"
              :empty-render="{name: 'NotData'}"
              class="cs-vxe-table"
              height="auto"
              auto-resize
              align="left"
              stripe
              :data="tbData"
              resizable
              :tooltip-config="{ enterable: true }"
              @checkbox-all="tbSelectChange"
              @checkbox-change="tbSelectChange"
            >
              <vxe-column fixed="left" type="checkbox" />
              <vxe-column
                v-for="(item, index) in columns"
                :key="index"
                show-overflow="tooltip"
                sortable
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                :min-width="item.Width ? item.Width : 120"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              >
                <template #default="{ row }">
                  <span>{{ row[item.Code] || "-" }}</span>
                </template>
              </vxe-column>

            </vxe-table>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-if="dialogVisibleMachine"
      ref="content"
      v-el-drag-dialog
      :title="title"
      :visible.sync="dialogVisibleMachine"
      :width="width"
      class="z-dialog"
      @close="handleClose"
    >
      <div style="display: flex;align-items: center">
        <span>机床：</span>
        <el-select v-model="batchEditMachineId" clearable filterable>
          <el-option v-for="item in nestingMachine" :key="item.Id" :value="item.Id" :label="item.Value" />
        </el-select>
        <el-button type="primary" style="margin-left: 10px" @click="batchEdit">批量修改</el-button>
        <div style="margin-left: auto;color:#298DFF">
          <span v-for="(value, key) in sumData" :key="key" style="margin-left: 10px">{{ key }}：{{ value }}</span>
        </div>
      </div>
      <div style="height: calc(100vh - 500px);margin-top: 16px">
        <vxe-table
          v-loading="machineLoading"
          element-loading-spinner="el-icon-loading"
          element-loading-text="拼命加载中"
          empty-text="暂无数据"
          class="cs-vxe-table"
          height="auto"
          auto-resize
          align="left"
          stripe
          :data="assignMachineTbData"
          resizable
          :tooltip-config="{ enterable: true }"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showIcon: true
          }"
          show-footer
          :footer-method="footerMethod"
          @checkbox-all="machineTbSelectChange"
          @checkbox-change="machineTbSelectChange"
        >
          <vxe-column fixed="left" type="checkbox" width="60" />
          <vxe-column
            v-for="(item, index) in machineColumns"
            :key="index"
            :fixed="item.fixed"
            show-overflow="tooltip"
            sortable
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            :min-width="item.Width ? item.Width : 120"
          >
            <template #default="{ row }">
              <template v-if="item.Code==='MachineId'">
                <el-select v-model="row[item.Code]" clearable filterable @change="getSumData">
                  <el-option v-for="item in nestingMachine" :key="item.Id" :value="item.Id" :label="item.Value" />
                </el-select>
              </template>
              <template v-else>
                <span>{{ row[item.Code] || "-" }}</span>
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <div style="text-align: right;margin-top: 16px">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitAssignMachine">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :width="width"
      class="z-dialog"
      @close="handleClose"
    >
      <el-form ref="machineForm" label-width="80px" :model="exportParams">
        <template v-if="exportType==='Sigma'">
          <el-form-item label="设备类型" prop="deviceType" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
            <SelectDict v-model="exportParams.deviceType" code="nesting_sigma_device" />
          </el-form-item>
          <el-form-item label="打印类型" prop="printType" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
            <el-radio-group v-model="exportParams.printType">
              <el-radio label="0">打印零件序号</el-radio>
              <el-radio label="1">打印零件号</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="导入模式" prop="importModel" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
            <SelectDict v-model="exportParams.importModel" code="nesting_import_model" />
          </el-form-item>
        </template>
      </el-form>
      <div style="text-align: right;margin-top: 16px">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitExport">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'

import TreeDetail from '@/components/TreeDetail/index.vue'
import elDragDialog from '@/directive/el-drag-dialog'
import axios from 'axios'

import ExpandableSection from '@/components/ExpandableSection/index.vue'
import {
  BochuAddTask,
  GetDetailSummaryList,
  GetNestingBillDetailList,
  GetNestingBillTreeList, GetProcessSelectList, LentakExport, ProfilesExport, SigmaWOLExport,
  UpdateMachineName
} from '@/api/PRO/production-task'
import SelectProject from '@/components/Select/SelectProject/index.vue'
import SelectDict from '@/components/Select/SelectDict/index.vue'
import SelectPartType from '@/components/Select/SelectPartType/index.vue'
import { GetDictionaryDetailListByCode } from '@/api/PRO/settings'
import { groupBy, mapValues, sumBy } from 'lodash'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import { combineURL } from '@/utils'
import ExportCustomReport from '@/components/ExportCustomReport/index.vue'

export default {

  directives: { elDragDialog },
  components: {
    ExportCustomReport,
    DynamicTableFields,
    SelectProject,
    ExpandableSection,
    TreeDetail,
    SelectDict,
    SelectPartType
  },
  data() {
    return {
      showExpand: true,
      treeData: [],
      treeLoading: true,
      expandedKey: '', // -1是全部
      projectName: '',
      statusType: '未完成',
      ProcessId: [],
      tbData: [],
      total: 0,
      tbLoading: false,
      pgLoading: false,
      searchParams: {
        ProjectName: '',
        BatchNo: '',
        PartNo: '',
        NestCode: '',
        MachineId: '',
        NestProgress: '',
        PartType: ''
      },
      exportParams: {
        deviceType: '',
        printType: '0',
        importModel: ''
      },
      dialogVisible: false,
      selectList: [],
      columns: [],
      columnsOption: [],
      title: '',
      width: '60%',
      dialogVisibleMachine: false,
      assignMachineParams: {},
      assignMachineTbData: [],
      machineColumns: [],
      selectListIds: [],
      machineLoading: false,
      nestingMachine: [],
      batchEditMachineId: '',
      submitLoading: false,
      selectedMachineIndexs: [],
      sumData: {},
      checkedTree: [],
      exportType: '',
      processList: [],
      lentakLoading: false,
      profilesLoading: false,
      bochuLoading: false
    }
  },

  computed: {
    jsonData() {
      return JSON.stringify({
        Machine: this.exportParams?.deviceType,
        ImportMode: this.exportParams?.importModel,
        TagType: this.exportParams?.printType,
        TaskDetailIdList: this.selectListIds,
        TreeKeyIds: this.currentNode?.TreeKeyIds
      })
    }
  },
  created() {
    this.getTableConfig('NestingBillTable', 'columns')
    this.getDict('nesting_machine', 'nestingMachine')
    this.fetchTreeData()
    this.getProcessList()
  },

  methods: {
    getSumData() {
      console.log('分配')
      const groupedData = groupBy(this.assignMachineTbData, 'MachineId')
      const obj = {
        '未分配': 0
      }
      this.nestingMachine.forEach(item => {
        obj[item.Value] = 0
      })
      for (const key in groupedData) {
        const f = this.nestingMachine.find(item => item.Id === key)
        if (f) {
          obj[f.Value] = groupedData[key]
        } else {
          obj['未分配'] = groupedData[key]
        }
      }
      this.sumData = mapValues(obj, (group) => {
        return sumBy(group, 'PartArea').toFixed(2) / 1 // 对每个分组的 value 值求和
      })
    },
    footerMethod({ columns, data }) {
      const footerData = [
        columns.map((column, index) => {
          if (['PartArea', 'PartCount'].includes(column.field)) {
            return this.sumNum(data, column.field, 2)
          }
          if (index === 1) {
            return '合计'
          }
          return null
        })
      ]
      return footerData
    },
    // 进行合计
    sumNum(costForm, field, digit) {
      let total = 0
      for (let i = 0; i < costForm.length; i++) {
        total += Number(costForm[i][field]) || 0
      }
      return total.toFixed(digit) / 1
    },
    getDict(code, prop) {
      GetDictionaryDetailListByCode({ dictionaryCode: code }).then(res => {
        this[prop] = res.Data
      })
    },
    // 项目区域数据集
    fetchTreeData() {
      this.treeLoading = true
      GetNestingBillTreeList({
        KeyWord: this.projectName,
        Status: this.statusType,
        ProcessId: Array.isArray(this.ProcessId) ? this.ProcessId.join(',') : this.ProcessId
      }).then((res) => {
        this.treeData = res.Data
        if (this.treeData.length > 0) {
          // this.currentNode = this.treeData[0]
          // this.$refs.tree.setCheckedKeys([this.currentNode?.TreeKeyIds])
        } else {
          this.currentNode = {}
          this.tbData = []
        }
      }).finally(() => [
        this.treeLoading = false
      ])
    },
    // 选中左侧项目节点
    handleNodeClick({ data, dataArray }) {
      this.selectListIds = []
      this.currentNode = data
      this.expandedKey = data.TreeKeyIds
      this.checkedTree = dataArray
      this.pgLoading = true
      this.handleSearch(true, true)
    },

    // 搜索
    handleSearch(reset, hasSearch = true) {
      if (reset) {
        this.$refs.searchParams.resetFields()
      }
      hasSearch && this.fetchData()
    },

    // 获取表格配置
    async getTableConfig(code, arr) {
      await GetGridByCode({ code: code }).then((res) => {
        this.$set(this, arr, res.Data.ColumnList)
      })
    },
    async updateColumns() {
      this.tbLoading = true
      await this.getTableConfig('NestingBillTable', 'columns')
      this.tbLoading = false
    },
    // 获取表格数据
    async fetchData() {
      console.log(this.checkedTree)
      this.tbLoading = true
      const res = await GetNestingBillDetailList({
        TreeKeyIds: this.checkedTree.checkedKeys.join(','),
        ...this.searchParams
      })
      this.tbData = res.Data
      this.tbLoading = false
      this.pgLoading = false
    },

    tbSelectChange(array) {
      this.selectListIds = array.records.map((item) => {
        return item.Id
      })
    },
    machineTbSelectChange(array) {
      this.selectedMachineIndexs = array.records.map((item, index) => {
        return index
      })
    },

    getTbData(data) {
      const { CountInfo } = data
      // this.tipLabel = `累计上传构件${YearSteel}件，总重${YearAllWeight}t。`
      this.tipLabel = CountInfo
    },

    getProcessList() {
      GetProcessSelectList({
        IsNest: true,
        Type: 2
      }).then(res => {
        this.processList = res.Data
      })
    },

    // 获取文件的arraybuffer格式并传入进行打包准备
    getFile(url) {
      return new Promise((resolve, reject) => {
        axios({
          method: 'get',
          url,
          responseType: 'arraybuffer'
        })
          .then((res) => {
            resolve(res.data)
          })
          .catch((error) => {
            reject(error.toString())
          })
      })
    },
    handleOpenExportDialog(type, title) {
      this.title = title
      this.exportType = type
      this.width = '40%'
      this.dialogVisible = true
    },

    handleClose() {
      this.dialogVisible = false
      this.dialogVisibleMachine = false
      this.batchEditMachineId = ''
      this.selectedMachineIndexs = []
    },

    assignTheMachine() {
      this.title = '分配机床'
      this.width = '60%'
      this.dialogVisibleMachine = true
      this.machineLoading = true
      this.getTableConfig('assignMachine', 'machineColumns')
      GetDetailSummaryList({ TaskDetailIds: this.selectListIds }).then(res => {
        this.assignMachineTbData = res.Data
        this.getSumData()
      }).finally(() => {
        this.machineLoading = false
      })
    },

    batchEdit() {
      this.selectedMachineIndexs.forEach(index => {
        this.assignMachineTbData[index].MachineId = this.batchEditMachineId
      })
      this.getSumData()
    },

    submitAssignMachine() {
      this.submitLoading = true
      UpdateMachineName({
        TreeKeyIds: this.expandedKey,
        details: this.assignMachineTbData
      }).then(res => {
        if (res.IsSucceed) {
          this.$message.success('分配成功')
          this.handleClose()
          this.submitLoading = false
        } else {
          this.$message.error(res.Message)
        }
      })
    },

    async submitExport() {
      await this.$refs.machineForm.validate()
      const apiDict = {
        'Sigma': SigmaWOLExport
      }
      const api = apiDict[this.exportType]
      api({
        Machine: this.exportParams.deviceType,
        ImportMode: this.exportParams.importModel,
        TagType: this.exportParams.printType,
        TaskDetailIdList: this.selectListIds,
        TreeKeyIds: this.currentNode.TreeKeyIds
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message.error(res.Message)
        }
      })
      this.handleClose()
    },

    async submitExport2(type) {
      const apiDict = {
        'Lentak': LentakExport,
        'Profiles': ProfilesExport,
        'Bochu': BochuAddTask
      }
      const loadingDict = {
        'Lentak': 'lentakLoading',
        'Profiles': 'profilesLoading',
        'Bochu': 'bochuLoading'
      }
      const api = apiDict[type]
      const loadingKey = loadingDict[type]

      this[loadingKey] = true

      api({
        Machine: this.exportParams.deviceType,
        ImportMode: this.exportParams.importModel,
        TagType: this.exportParams.printType,
        TaskDetailIdList: this.selectListIds,
        TreeKeyIds: this.currentNode.TreeKeyIds
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message.error(res.Message)
        }
      }).finally(() => {
        this[loadingKey] = false
      })
    }

  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/tabs.scss";

.min900 {
  min-width: 900px;
  overflow: auto;
}

.app-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;

  .cs-left {
    display: flex;
    flex-direction: column;
    margin-right: 20px;

    .inner-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px 10px 16px 16px;
      border-radius: 4px;
      overflow: hidden;

      .tree-search {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
      }

      .tree-x {
        overflow: hidden;
        margin-top: 16px;
        flex: 1;

        .cs-scroll {
          overflow-y: auto;
          @include scrollBar;
        }

        .el-tree {
          height: 100%;
        }
      }

    }
  }

  .cs-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .cs-z-tb-wrapper {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 0;

      .tb-container {
        overflow: hidden;
        padding: 0 16px 16px 16px;
        flex: 1;
        height: 0;
      }
    }

    .cs-bottom {
      padding: 8px 16px 8px 16px;
      position: relative;
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .data-info {
        .info-x {
          margin-right: 20px;
        }
      }

      .pg-input {
        width: 100px;
        margin-right: 20px;
      }

    }

  }
}

.z-dialog {
  ::v-deep {
    .el-dialog__header {
      background-color: #298dff;

      .el-dialog__title,
      .el-dialog__close {
        color: #ffffff;
      }
    }

    .el-dialog__body {
      // max-height: 740px;
      overflow: auto;
      @include scrollBar;

      &::-webkit-scrollbar {
        width: 8px;
      }
    }
  }
}

.cs-from {
  background-color: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 16px 16px 0;
  display: flex;
  font-size: 14px;
  color: rgba(34, 40, 52, 0.65);

  label {
    display: inline-block;
    margin-right: 20px;
    white-space: nowrap;
    vertical-align: top;
  }

  .cs-from-title {
    flex: 1;
  }

  .mb0 {
    margin-bottom: 0;

    ::v-deep {
      .el-form-item {
        margin-bottom: 0
      }
    }
  }

  .cs-search {
    width: 100%;

  }
}

.input-with-select {
  //width: 250px;
}

.cs-button-box {
  padding: 16px 16px 6px 16px;
  box-sizing: border-box;
  position: relative;
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  ::v-deep .el-button {
    margin-left: 0 !important;
    margin-right: 10px !important;
    margin-bottom: 10px !important;
  }

  .cs-length {
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: row-reverse;
  }
}

.info-box {
  margin:0 16px 16px 16px;
  display: flex;
  justify-content: center;
  font-size: 14px;
  height: 64px;
  background: rgba(41, 141, 255, 0.05);

  .cs-col {
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    margin-right: 64px;
  }

  .info-label {
    color: #999999;
  }

  i {
    color: #00c361;
    font-style: normal;
    font-weight: 600;
    margin-left: 10px;
  }
}

::v-deep .el-tree-node {
  min-width: 240px;
  width: min-content;
}

::v-deep .el-tree-node > .el-tree-node__children {
  overflow: inherit;
}

.stretch-btn {
  position: absolute;
  width: 20px;
  height: 130px;
  top: calc((100% - 130px) / 2);
  right: -20px;
  display: flex;
  align-items: center;
  background: #eff1f3;
  cursor: pointer;

  .center-btn {
    width: 14px;
    height: 100px;
    border-radius: 0 9px 9px 0;
    background-color: #8c95a8;

    > i {
      line-height: 100px;
      text-align: center;
      color: #fff;
    }
  }
}

* {
  box-sizing: border-box;
}

.fourGreen {
  color: #00C361;
  font-style: normal;
}

.fourOrange {
  color: #FF9400;
  font-style: normal;
}

.fourRed {
  color: #FF0000;
  font-style: normal;
}

.cs-blue {
  color: #5AC8FA;
}

.orangeBg{
  background: rgba(255,148,0,0.1);
}

.redBg{
  background: rgba(252,107,127,0.1);
}
.greenBg{
  background: rgba(0, 195, 97, 0.10);
}

.cs-tag{
  margin-left: 8px;
  font-size: 12px;
  padding:2px 4px;
  border-radius: 1px;
}

.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}
.cs-divider{
  margin:16px 0 0 0;
}
</style>

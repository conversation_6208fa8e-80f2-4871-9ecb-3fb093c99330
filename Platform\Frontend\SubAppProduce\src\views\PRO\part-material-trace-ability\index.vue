<template>
  <div class="app-container abs100">
    <div v-loading="pgLoading" class="h100 app-wrapper" element-loading-text="加载中">
      <ExpandableSection
        v-model="showExpand"
        :width="300"
        class="cs-left fff "
      >
        <div class="inner-wrapper">
          <div class="tree-search">
            <el-select v-model="statusType" clearable class="search-select" placeholder="导入状态选择">
              <el-option label="已导入" value="已导入" />
              <el-option label="未导入" value="未导入" />
              <el-option label="已变更" value="已变更" />
            </el-select>
            <el-input
              v-model.trim="projectName"
              placeholder="关键词搜索"
              size="small"
              clearable
              suffix-icon="el-icon-search"
            />
          </div>
          <el-divider class="cs-divider" />
          <div class="tree-x cs-scroll">
            <tree-detail
              ref="tree"
              icon="icon-folder"
              is-custom-filter
              :custom-filter-fun="customFilterFun"
              :loading="treeLoading"
              :tree-data="treeData"
              show-status
              show-detail
              :filter-text="filterText"
              :expanded-key="expandedKey"
              @handleNodeClick="handleNodeClick"
            >
              <template #csLabel="{showStatus,data}">
                <span v-if="!data.ParentNodes" class="cs-blue">({{ data.Code }})</span>{{ data.Label }}
                <template v-if="showStatus && data.Label!='全部'">
                  <span v-if="data.Data.Is_Deepen_Change" class="cs-tag redBg"> <i class="fourRed">已变更</i></span>
                  <span v-else :class="['cs-tag',data.Data.Is_Imported==true ? 'greenBg' : 'orangeBg']">
                    <i :class="[data.Data.Is_Imported==true ? 'fourGreen' : 'fourOrange']">{{
                      data.Data.Is_Imported == true ? '已导入' : '未导入'
                    }}</i>
                  </span>
                </template>
              </template>
            </tree-detail>
          </div>

        </div>
      </ExpandableSection>

      <div class="cs-right">
        <div class="tb-x">
          <el-row>

            <el-form ref="form" :model="form" label-width="80px">
              <el-col :span="7">
                <el-form-item label="构件名称" prop="Component_Code">
                  <!--                  <el-input v-model="form.Component_Code" />-->
                  <el-input
                    v-model="form.Component_Code"
                    clearable
                    style="width: 100%"
                    class="input-with-select"
                    placeholder="请输入内容"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="componentCodeMode"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="模糊搜索" :value="1" />
                      <el-option label="精确搜索" :value="2" />
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="部件名称" prop="Com_Name">
                  <!--                  <el-input v-model="form.Com_Name" />-->
                  <el-input
                    v-model="form.Com_Name"
                    clearable
                    style="width: 100%"
                    class="input-with-select"
                    placeholder="请输入内容"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="comNameMode"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="模糊搜索" :value="1" />
                      <el-option label="精确搜索" :value="2" />
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item label="零件名称" prop="Code">
                  <!--                  <el-input v-model="form.Code" />-->
                  <el-input
                    v-model="form.Code"
                    clearable
                    style="width: 100%"
                    class="input-with-select"
                    placeholder="请输入内容"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="codeMode"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="模糊搜索" :value="1" />
                      <el-option label="精确搜索" :value="2" />
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label-width="60px" label="批次" prop="InstallUnit_Ids">
                  <el-select
                    v-model="form.InstallUnit_Ids"
                    filterable
                    clearable
                    multiple
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in installUnitIdNameList"
                      :key="item.Id"
                      :label="item.Name"
                      :value="item.Id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-form>

          </el-row>
          <vxe-toolbar>
            <template #buttons>
              <el-button type="primary" @click="fetchData(1)">搜 索</el-button>
              <el-button @click="handleReset">重 置</el-button>
            </template>
            <template #tools>
              <DynamicTableFields
                title="表格配置"
                :table-config-code="gridCode"
                @updateColumn="changeColumn"
              />
            </template>
          </vxe-toolbar>
          <!--          activeMethod: activeCellMethod,-->
          <div class="tb-container">
            <vxe-table
              ref="xTable"
              :empty-render="{name: 'NotData'}"
              show-header-overflow
              show-overflow
              class="cs-vxe-table"
              :row-config="{isCurrent: true, isHover: true}"
              align="left"
              height="100%"
              :loading="tbLoading"
              stripe
              :data="tbData"
              resizable
              empty-text="暂无数据"
              :tooltip-config="{ enterable: true }"
            >
              <vxe-column
                v-for="(item, index) in columns"
                :key="index"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                show-overflow="tooltip"
                sortable
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                :min-width="item.Width ? item.Width : 120"
              >
                <template #default="{ row }">
                  <div v-if="item.Code == 'Component_Code'">
                    <span class="isPicActive" @click="getComponentInfo(row)"> {{ row[item.Code] || '-' }}</span>
                  </div>
                  <div v-else-if="item.Code == 'Com_Name'">
                    <span :class="row.unitDrawings && row.unitDrawings.length > 0 ? 'isPicActive' : ''" @click="getPartInfo(row, 3)"> {{ row[item.Code] || '-' }} </span>
                  </div>
                  <div v-else-if="item.Code == 'Code'">
                    <span :class="row.partDrawings && row.partDrawings.length > 0 ? 'isPicActive' : ''" @click="getPartInfo(row, 1)"> {{ row[item.Code] || '-' }} </span>
                  </div>
                  <div v-else-if="item.Code == 'MaterialInfo'">
                    <span>
                      <el-button type="text" @click="handleView(row)">查看</el-button>
                    </span>
                  </div>
                  <div v-else>
                    <span>{{ row[item.Code] || "-" }}</span>
                  </div>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
        <Pagination
          :total="total"
          :page-sizes="tablePageSize"
          :page.sync="queryInfo.Page"
          :limit.sync="queryInfo.PageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="pageChange"
        />
      </div>
      <el-dialog
        v-if="dialogVisible"
        v-dialogDrag
        class="plm-custom-dialog"
        :title="title"
        :visible.sync="dialogVisible"
        width="70%"
        @close="handleClose"
      >
        <component
          :is="currentComponent"
          ref="content"
        />
      </el-dialog>
    </div>
    <modelDrawing ref="modelDrawingRef" :type="drawingType" />
  </div>
</template>

<script>
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import TreeDetail from '@/components/TreeDetail/index.vue'
import { GetInstallUnitIdNameList } from '@/api/PRO/project'
import { GetPartWithParentPageList, GetDrawingFileList } from '@/api/PRO/production-task'
import { GetSteelCadAndBimId } from '@/api/PRO/component'
import getTbInfo from '@/mixins/PRO/get-table-info'
import { tablePageSize } from '@/views/PRO/setting'
import Pagination from '@/components/Pagination/index.vue'
import DynamicTableFields from '@/components/DynamicTableFields/index.vue'
import MaterialInfo from './components/Info.vue'
import { GetProjectAreaTreeList } from '@/api/PRO/partslist'
import modelDrawing from '@/views/PRO/components/modelDrawing.vue'
const SPLIT_SYMBOL = '$_$'
export default {
  name: 'PROPartMaterialTraceability',
  components: { DynamicTableFields, MaterialInfo, Pagination, TreeDetail, ExpandableSection, modelDrawing },
  mixins: [getTbInfo],
  data() {
    return {
      pgLoading: false,
      showExpand: true,
      statusType: '',
      projectName: '',
      expandedKey: '',
      currentComponent: '',
      title: '',
      treeLoading: false,
      dialogVisible: false,
      tbLoading: false,
      treeData: [],
      tbData: [],
      installUnitIdNameList: [],
      columns: [],
      tablePageSize: tablePageSize,
      total: 0,
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      form: {
        Component_Code: '',
        Com_Name: '',
        Code: '',
        InstallUnit_Ids: '',
        Area_Id: '',
        Sys_Project_Id: ''
      },
      componentCodeMode: 1,
      comNameMode: 1,
      codeMode: 1,
      gridCode: 'PartMaterialTraceabilityList',
      drawingType: '构件'
    }
  },
  computed: {
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    }
  },
  async mounted() {
    this.currentNode = {}

    await this.getTableConfig(this.gridCode)
    this.fetchTreeData()
  },
  methods: {
    async changeColumn() {
      await this.getTableConfig(this.gridCode)
    },
    handleReset() {
      this.$refs.form.resetFields()
      this.fetchData(1)
    },
    fetchData(page) {
      this.tbLoading = true
      page && (this.queryInfo.Page = page)

      const { Component_Code, Com_Name, Code, ...search } = this.form
      const searchForm = {}

      const modes = [
        { mode: this.componentCodeMode, exact: 'Comp_Codes', like: 'Comp_Code_Like', value: Component_Code },
        { mode: this.comNameMode, exact: 'SubAssembly_Codes', like: 'SubAssembly_Code_Like', value: Com_Name },
        { mode: this.codeMode, exact: 'Codes', like: 'Code_Like', value: Code }
      ]

      modes.forEach(({ mode, exact, like, value }) => {
        searchForm[mode === 1 ? like : exact] = value
      })
      GetPartWithParentPageList({
        ...this.queryInfo,
        ...search,
        ...searchForm
      }).then(async(res) => {
        if (res.IsSucceed) {
          const resData = res.Data.Data
          // 获取三类图纸数据
          const [partDrawing, componentDrawing, unitDrawing] = await Promise.all([
            this.getDrawingFileList(1, [...new Set(resData.map(v => v.Code).filter(Boolean))].join(',')), // 零件图纸 resData.map(v => v.Code).filter(Boolean).join(',')
            this.getDrawingFileList(2, [...new Set(resData.map(v => v.Component_Code).filter(Boolean))].join(',')), // 构件图纸
            this.getDrawingFileList(3, [...new Set(resData.map(v => v.Com_Name).filter(Boolean))].join(',')) // 部件图纸
          ])

          // 合并图纸数据到resData
          this.mergeDrawingData(resData, {
            type1: { data: partDrawing, key: 'Code' },
            type2: { data: componentDrawing, key: 'Component_Code' },
            type3: { data: unitDrawing, key: 'Com_Name' }
          })

          this.tbData = resData
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(_ => {
        this.pgLoading = false
        this.tbLoading = false
      })
    },
    /**
     * 将图纸数据合并到 resData（支持一对多匹配）
     * @param {Array} resData 原始数据
     * @param {Object} drawings 三类图纸数据 { type1, type2, type3 }
     */
    mergeDrawingData(resData, drawings) {
      // 合并零件图纸（Type=1）
      if (drawings.type1?.data?.length) {
        const partMap = this.groupBySteelName(drawings.type1.data)
        resData.forEach(item => {
          if (item.Code && partMap.has(item.Code)) {
            item.partDrawings = partMap.get(item.Code)
          }
        })
      }

      // 合并构件图纸（Type=2）
      if (drawings.type2?.data?.length) {
        const componentMap = this.groupBySteelName(drawings.type2.data)
        resData.forEach(item => {
          if (item.Component_Code && componentMap.has(item.Component_Code)) {
            item.componentDrawings = componentMap.get(item.Component_Code)
          }
        })
      }

      // 合并部件图纸（Type=3）
      if (drawings.type3?.data?.length) {
        const unitMap = this.groupBySteelName(drawings.type3.data)
        resData.forEach(item => {
          if (item.Com_Name && unitMap.has(item.Com_Name)) {
            item.unitDrawings = unitMap.get(item.Com_Name)
          }
        })
      }
    },
    /**
     * 按 SteelName 分组图纸数据
     * @param {Array} drawingData 图纸数据
     * @returns {Map} key: SteelName, value: 对应的图纸数组
     */
    groupBySteelName(drawingData) {
      const map = new Map()
      drawingData.forEach(drawing => {
        if (!map.has(drawing.SteelName)) {
          map.set(drawing.SteelName, [])
        }
        map.get(drawing.SteelName).push(drawing)
      })
      return map
    },
    // 获取图纸
    async getDrawingFileList(Type, Codes) {
      if (Codes === '' || Codes === undefined) return
      const res = await GetDrawingFileList({ Type, Codes, Sys_Project_Id: this.form.Sys_Project_Id })
      if (res.IsSucceed) {
        return res.Data
      } else {
        this.$message({
          message: res.Message,
          type: 'error'
        })
      }
    },

    getComponentInfo(row) {
      this.drawingType = '构件'
      const importDetailId = row.Component_Id
      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {
        if (res.IsSucceed) {
          const _data = res.Data?.[0]
          if (!row.componentDrawings && !_data.ExtensionName) {
            this.$message({
              message: '当前构件无图纸和模型',
              type: 'warning'
            })
            return
          }

          const drawingData = {
            'extensionName': _data.ExtensionName,
            'fileBim': _data.fileBim,
            'IsUpload': _data.IsUpload,
            'Code': row.Component_Code,
            'Sys_Project_Id': this.form.Sys_Project_Id
          }

          this.$nextTick((_) => {
            this.$refs.modelDrawingRef.dwgInit(drawingData)
          })
        }
      })
    },

    getPartInfo(row, type) {
      if (type === 1 && (!row.partDrawings || row.partDrawings.length === 0)) return
      if (type === 3 && (!row.unitDrawings || row.unitDrawings.length === 0)) return
      this.drawingType = type === 1 ? '零件' : '部件'
      const importDetailId = type === 1 ? row.Part_Aggregate_Id : row.BJ_Part_Aggregate_Id
      GetSteelCadAndBimId({ importDetailId: importDetailId }).then((res) => {
        if (res.IsSucceed) {
          const drawingData = {
            'extensionName': res.Data[0].ExtensionName,
            'fileBim': res.Data[0].fileBim,
            'IsUpload': res.Data[0].IsUpload,
            'Code': type === 1 ? row.Code : row.Com_Name,
            'Sys_Project_Id': this.form.Sys_Project_Id
          }
          this.$refs.modelDrawingRef.dwgInit(drawingData)
        }
      })
    },

    fetchTreeData() {
      GetProjectAreaTreeList({
        Status: this.statusType,
        MenuId: this.$route.meta.Id,
        Type: 0
      }).then((res) => {
        // const resAll = [
        //   {
        //     ParentNodes: null,
        //     Id: '-1',
        //     Code: '全部',
        //     Label: '全部',
        //     Level: null,
        //     Data: {},
        //     Children: []
        //   }
        // ]
        // const resData = resAll.concat(res.Data)
        if (res.Data.length === 0) {
          this.treeLoading = false
          return
        }
        const resData = res.Data
        resData.map((item) => {
          if (item.Children.length === 0) {
            item.Data.Is_Imported = false
          } else {
            item.Data.Is_Imported = item.Children.some((ich) => {
              return ich.Data.Is_Imported === true
            })

            item.Is_Directory = true
            item.Children.map((it) => {
              if (it.Children.length > 0) {
                it.Is_Directory = true
              }
            })
          }
          return item
        })
        this.treeData = resData
        if (Object.keys(this.currentNode).length === 0) {
          this.setKey()
        } else {
          this.handleNodeClick(this.currentNode)
        }
        this.treeLoading = false
      })
    },
    setKey() {
      const deepFilter = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          console.log(Data)
          if (Data.ParentId && !Children?.length) {
            console.log(Data, '????')
            this.currentNode = Data
            this.handleNodeClick(item)
            return
          } else {
            if (Children && Children.length > 0) {
              return deepFilter(Children)
            } else {
              this.handleNodeClick(item)
              return
            }
          }
        }
      }
      return deepFilter(this.treeData)
    },
    getInstallUnitIdNameList(id, data) {
      if (id === '') {
        this.installUnitIdNameList = []
      } else {
        GetInstallUnitIdNameList({ Area_Id: id }).then((res) => {
          this.installUnitIdNameList = res.Data
        })
      }
    },
    customFilterFun(value, data, node) {
      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [data.Data.Is_Deepen_Change ? '已变更' : data.Data.Is_Imported ? '已导入' : '未导入']
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [...status, data.Data.Is_Deepen_Change ? '已变更' : data.Data.Is_Imported ? '已导入' : '未导入']
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter(v => !!v)
      status = status.filter(v => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },
    handleNodeClick(data) {
      if (data.Children.length > 0) {
        return
      }
      this.currentNode = data
      this.expandedKey = data.Id
      this.form.Sys_Project_Id = data?.Data?.Sys_Project_Id
      this.form.Area_Id = data?.Data?.Id

      this.pgLoading = true
      this.getInstallUnitIdNameList(data.Id)
      this.fetchData(1)
      // this.getInstallUnitIdNameList(dataID, data)
      // this.fetchData()
      // this.getComponentSummaryInfo()
    },
    handleView(item) {
      this.title = '查看'
      this.currentComponent = 'MaterialInfo'
      this.dialogVisible = true
      this.$nextTick(_ => {
        console.log(77, item)
        this.$refs['content'].init(item.Part_Aggregate_Id)
      })
    },
    handleClose() {
      this.dialogVisible = false
    }

  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
.min900 {
  min-width: 900px;
  overflow: auto;
}

.app-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;

  .cs-left {
    display: flex;
    flex-direction: column;
    margin-right: 20px;

    .inner-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px 10px 16px 16px;
      border-radius: 4px;
      overflow: hidden;

      .tree-search {
        display: flex;

        .search-select {
          margin-right: 8px;
        }
      }

      .tree-x {
        overflow: hidden;
        margin-top: 16px;
        flex: 1;

        .cs-scroll {
          overflow-y: auto;
          @include scrollBar;
        }

        .el-tree {
          height: 100%;

          //::v-deep {
          //  .el-tree-node {
          //    min-width: 240px;
          //    width: min-content;
          //
          //    .el-tree-node__children {
          //      overflow: inherit;
          //    }
          //  }
          //}
        }
      }

    }
  }

  .cs-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding:16px;
    background:#fff;

    .tb-x {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 0;

      .tb-container {
        flex: 1;
        height: 0;
      }
    }

    .cs-bottom {
      padding: 8px 16px 8px 16px;
      position: relative;
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;

      .data-info {
        .info-x {
          margin-right: 20px;
        }
      }

      .pg-input {
        width: 100px;
        margin-right: 20px;
      }

      .pagination-container {
        text-align: right;
        margin: 0;
        padding: 0;

        ::v-deep .el-input--small .el-input__inner {
          height: 28px;
          line-height: 28px;
        }
      }

    }

  }

  .fourGreen {
    color: #00C361;
    font-style: normal;
  }

  .fourOrange {
    color: #FF9400;
    font-style: normal;
  }

  .fourRed {
    color: #FF0000;
    font-style: normal;
  }

  .cs-blue {
    color: #5AC8FA;
  }

  .orangeBg{
    background: rgba(255,148,0,0.1);
  }

  .redBg{
    background: rgba(252,107,127,0.1);
  }
  .greenBg{
    background: rgba(0, 195, 97, 0.10);
  }

  .cs-tag{
    margin-left: 8px;
    font-size: 12px;
    padding:2px 4px;
    border-radius: 1px;
  }

  .cs-tree-x {
    ::v-deep {
      .el-select {
        width: 100%;
      }
    }
  }
  .cs-divider{
    margin:16px 0 0 0;
  }
  .pagination-container{
    padding: 0;
    margin: 10px 0;
    text-align: right;
  }
  ::v-deep{
    .el-form-item{
      margin-bottom: 0;
    }
  }

}
.plm-custom-dialog {
  ::v-deep {
    .el-dialog .el-dialog__body {
      height: 70vh;
      overflow: auto;
    }
  }
}

.isPicActive {
  color: #298dff;
  cursor: pointer;
}
</style>

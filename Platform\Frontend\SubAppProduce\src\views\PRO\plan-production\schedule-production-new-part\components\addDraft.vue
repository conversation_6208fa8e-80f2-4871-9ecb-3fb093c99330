<template>
  <div class="contentBox">
    <div class="main-info">
      <div class="left">
        <ExpandableSection v-model="showExpand" v-loading="tbLoading" class="fff" :width="300">
          <div class="inner-wrapper">
            <div class="tree-search">
              <el-select
                v-model="statusType"
                clearable
                class="search-select"
                placeholder="请选择"
              >
                <el-option label="可排产" value="可排产" />
                <el-option label="排产完成" value="排产完成" />
                <el-option label="未导入" value="未导入" />
              </el-select>
              <el-input
                v-model.trim="projectName"
                placeholder="搜索..."
                size="small"
                clearable
                suffix-icon="el-icon-search"
              />
            </div>
            <el-divider class="cs-divider" />
            <div class="tree-x cs-scroll">
              <tree-detail
                ref="tree"
                icon="icon-folder"
                is-custom-filter
                :custom-filter-fun="customFilterFun"
                :loading="treeLoading"
                :tree-data="treeData"
                show-status
                show-detail
                :filter-text="filterText"
                :expanded-key="expandedKey"
                @handleNodeClick="handleNodeClick"
              >
                <template #csLabel="{showStatus,data}">
                  <span v-if="!data.ParentNodes" class="cs-blue">({{ data.Code }})</span>{{ data.Label }}
                  <template v-if="showStatus">
                    <span :class="['cs-tag',data.Data[statusCode]=='可排产' ? 'greenBg' : data.Data[statusCode]=='排产完成' ?'orangeBg':data.Data[statusCode]=='未导入'?'redBg':'']">
                      <i
                        v-if="data.Data[statusCode]"
                        :class="[data.Data[statusCode]=='可排产' ? 'fourGreen' : data.Data[statusCode]=='排产完成' ?'fourOrange':data.Data[statusCode]=='未导入'?'fourRed':'']"
                      >
                        {{ data.Data[statusCode] }}
                      </i>

                    </span>
                  </template>
                </template>

              </tree-detail>
            </div>
          </div>
        </ExpandableSection>
      </div>
      <div class="right">

        <el-form ref="form" :model="form" label-width="90px">
          <el-row>
            <template v-if="isCom">
              <el-col :span="10">
                <el-form-item label-width="70px" label="构件编号" prop="Comp_Codes">
                  <el-input
                    v-model="form.Comp_Code"
                    clearable
                    style="width: 45%"
                    placeholder="请输入(空格区分/多个搜索)"
                    type="text"
                  />
                  <el-input
                    v-model="form.Comp_CodeBlur"
                    clearable
                    style="width: 45%;margin-left: 16px"
                    placeholder="模糊查找(请输入关键字)"
                    type="text"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="构件类型" label-width="70px" prop="Type">
                  <el-tree-select
                    ref="treeSelectObjectType"
                    v-model="form.Type"
                    style="width: 100%"
                    class="cs-tree-x"
                    :select-params="treeSelectParams"
                    :tree-params="ObjectTypeList"
                    value-key="Id"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="规格" prop="Spec" label-width="50px">
                  <el-input v-model.trim="form.Spec" style="width: 100%" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
            </template>
            <template v-else>
              <el-col :span="12">
                <!--                <el-form-item label-width="70px" label="零件名称" prop="Part_Code">
                  <div class="cs-input-x">
                    <el-input
                      v-model="form.Part_Code"
                      placeholder="请输入(空格区分/多个搜索)"
                      clearable
                      class="w100"
                    />
                    <el-input
                      v-model="form.Part_CodeBlur"
                      clearable
                      class="w100"
                      style="margin-left: 10px;"
                      placeholder="模糊查找(请输入关键字)"
                      type="text"
                    />
                  </div>
                </el-form-item>
             -->
                <el-form-item prop="searchContent" label="构件名称">
                  <el-input
                    v-model="searchContent"
                    clearable
                    class="input-with-select w100"
                    placeholder="请输入(空格区分/多个搜索)"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="curSearch"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="精准查询" :value="1" />
                      <el-option label="模糊查询" :value="0" />
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="searchPartContent" label="零件名称">
                  <el-input
                    v-model="searchPartContent"
                    clearable
                    class="input-with-select w100"
                    placeholder="请输入(空格区分/多个搜索)"
                    size="small"
                  >
                    <el-select
                      slot="prepend"
                      v-model="curPartSearch"
                      placeholder="请选择"
                      style="width: 100px"
                    >
                      <el-option label="精准查询" :value="1" />
                      <el-option label="模糊查询" :value="0" />
                    </el-select>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="规格" prop="Spec">
                  <el-input
                    v-model="form.Spec"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="零件种类" prop="Type_Name">
                  <el-select
                    v-model="form.Type_Name"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in typeOption"
                      :key="item.Code"
                      :label="item.Name"
                      :value="item.Name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="5">
              <el-form-item label="批次" label-width="50px" prop="Create_UserName">
                <el-select
                  v-model="form.InstallUnit_Id"
                  filterable
                  clearable
                  multiple
                  style="width: 100%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in installUnitIdList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label-width="0">
                <el-button style="margin-left: 10px" @click="handleReset">重置</el-button>
                <el-button style="margin-left: 10px" type="primary" @click="handleSearch()">查询</el-button>
                <el-button :loading="addLoading" style="margin-left: 10px" type="primary" @click="addToList()">加入列表</el-button>
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>

        <div class="tb-wrapper">
          <vxe-table
            ref="xTable1"
            :empty-render="{name: 'NotData'}"
            show-header-overflow
            empty-text="暂无数据"
            height="auto"
            show-overflow
            :checkbox-config="{checkField: 'checked', checkMethod: checkCheckboxMethod}"
            :loading="tbLoading"
            :row-config="{isCurrent: true, isHover: true }"
            class="cs-vxe-table"
            align="left"
            stripe
            :data="fTable"
            resizable
            :edit-config="{trigger: 'click', mode: 'cell'}"
            :tooltip-config="{ enterable: true }"
            @checkbox-all="tbSelectChange"
            @checkbox-change="tbSelectChange"
          >
            <vxe-column fixed="left" type="checkbox" width="60" />
            <template v-for="item in columns">
              <vxe-column
                v-if="item.Code === 'Is_Component'"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <el-tag :type="row.Is_Component ? 'danger' : 'success'">{{
                    row.Is_Component ? "否" : "是"
                  }}</el-tag>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="['Part_Code','Comp_Code'].includes(item.Code)"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                  <el-tag v-if="row.Is_Change" style="margin-right: 8px;" type="danger">变</el-tag>
                  {{ row[item.Code] | displayValue }}
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="['Can_Schduling_Count'].includes(item.Code)"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <span v-if="showSc">{{ row.csCount||'' }}</span>
                  <span v-else>{{ row[item.Code] | displayValue }}</span>
                </template>
              </vxe-column>
              <vxe-column
                v-else-if="['Can_Schduling_Weight'].includes(item.Code)"
                :key="item.Code"
                :align="item.Align"
                :field="item.Code"
                :title="item.Display_Name"
                sortable
                :min-width="item.Width"
              >
                <template #default="{ row }">
                  <span v-if="showSc">{{ row.csCountWeight||'' }}</span>
                  <span v-else>{{ row[item.Code] | displayValue }}</span>
                </template>
              </vxe-column>
              <vxe-column
                v-else
                :key="item.Code"
                :align="item.Align"
                :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
                show-overflow="tooltip"
                sortable
                :field="item.Code"
                :title="item.Display_Name"
                :min-width="item.Width"
              />
            </template>
          </vxe-table>
        </div>
        <div class="data-info">
          <el-tag
            size="medium"
            class="info-x"
          >已选 {{ totalSelection.length }} 条数据
          </el-tag>
          <vxe-pager
            border
            background
            :loading="tbLoading"
            :current-page.sync="pageInfo.page"
            :page-size.sync="pageInfo.pageSize"
            :page-sizes="pageInfo.pageSizes"
            :total="pageInfo.total"
            :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
            size="small"
            @page-change="handlePageChange"
          />
        </div>
      </div>
    </div>
    <div class="button">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :disabled="!totalSelection.length"
        :loading="saveLoading"
        @click="handleSave(2)"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { GetCanSchdulingComps } from '@/api/PRO/production-task'
import { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'
import { v4 as uuidv4 } from 'uuid'
import { debounce, deepClone } from '@/utils'
import { tablePageSize } from '@/views/PRO/setting'
import { GetCompTypeTree } from '@/api/PRO/professionalType'
import { GetPartTypeList } from '@/api/PRO/partType'
import TreeDetail from '@/components/TreeDetail/index.vue'
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import { GetInstallUnitIdNameList, GetProjectAreaTreeList } from '@/api/PRO/project'
import { getUnique } from '../constant'
import { mapGetters } from 'vuex'
import { findAllParentNode } from '@/utils/tree'
import { GetStopList } from '@/api/PRO/production-task'
const SPLIT_SYMBOL = '$_$'

export default {
  components: { ExpandableSection, TreeDetail },
  props: {
    scheduleId: {
      type: String,
      default: ''
    },
    pageType: {
      type: String,
      default: 'com'
    },
    showDialog: {
      type: Boolean,
      default: false
    },

    installId: {
      type: String,
      default: ''
    },
    currentIds: {
      type: String,
      default: ''
    },

    isPartPrepare: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageInfo: {
        page: 1,
        pageSize: 500,
        pageSizes: tablePageSize,
        total: 0
      },
      form: {
        Comp_Code: '',
        Comp_CodeBlur: '',
        Part_CodeBlur: '',
        Part_Code: '',
        Type_Name: '',
        InstallUnit_Id: [],
        Spec: '',
        Type: ''
      },
      curSearch: 1,
      curPartSearch: 1,
      showExpand: true,
      searchContent: '',
      searchPartContent: '',
      statusType: '',
      projectName: '',
      expandedKey: '',
      statusCode: 'Part_Schdule_Status',
      isOwnerNull: true,
      tbLoading: false,
      treeLoading: false,
      addLoading: false,
      saveLoading: false,
      showSc: false,
      installUnitIdList: [],
      columns: [],
      fTable: [],
      tbConfig: {},
      TotalCount: 0,
      Page: 0,
      totalSelection: [],
      treeData: [],
      search: () => ({}),
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      areaId: '',
      typeOption: []
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    },
    ...mapGetters('schedule', ['addTbKeys'])
  },
  watch: {
    showDialog(newValue) {
      newValue && (this.saveLoading = false)
    }
  },
  mounted() {

  },
  methods: {
    initData() {
      console.log('initData')
      this.tbData = []
      this.getConfig()
      this.fetchTreeData()
      if (this.isCom) {
        this.getObjectTypeList()
      } else {
        this.getType()
      }
      this.search = debounce(this.fetchData, 800, true)
      this.setPageData()
    },
    handleNodeClick(data) {
      if (this.areaId === data.Id) {
        return
      }
      if (!data.ParentNodes || data.Children?.length > 0) {
        return
      }
      if (data?.Data[this.statusCode] === '未导入') {
        this.$message({
          message: '清单未导入，请联系深化人员导入清单',
          type: 'warning'
        })
        this.expandedKey = data.Id
        return
      }

      const setData = ({ Data }) => {
        this.areaId = Data.Id
        this.projectId = Data.Project_Id
        this.expandedKey = this.areaId

        const _arr = findAllParentNode(this.treeData, data.Id, true)
        this.nodeLabels = _arr.filter(v => !!v.ParentNodes).map(p => p.Label)

        // this.formInline.Finish_Date = ''
        // this.formInline.InstallUnit_Id = ''
        // this.formInline.Remark = ''
        this.fetchData()
        // this.getAreaInfo()
        this.getInstallUnitIdNameList()
      }

      setData(data)
    },
    fetchTreeData() {
      this.treeLoading = true
      GetProjectAreaTreeList({ MenuId: this.$route.meta.Id, projectName: this.projectName, type: this.isCom ? 1 : 2 }).then((res) => {
        if (res.IsSucceed) {
          if (res.Data.length === 0) {
            this.treeData = []
            this.treeLoading = false
            return
          }
          const resData = res.Data.map(item => {
            item.Is_Directory = true
            return item
          })
          this.treeData = resData
          console.log('setKey')
          this.setKey()
          this.treeLoading = false
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.treeData = []
          this.treeLoading = false
        }
      }).catch(() => {
        this.treeLoading = false
        this.treeData = []
      })
    },
    setKey() {
      const deepFilter = (tree) => {
        console.log('tree', tree)
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          console.log(Data)
          if (Data.ParentId && !Children?.length) {
            this.handleNodeClick(item)
            return
          } else {
            if (Children && Children?.length > 0) {
              return deepFilter(Children)
            }
          }
        }
      }
      return deepFilter(this.treeData)
    },
    customFilterFun(value, data, node) {
      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [data.Data[this.statusCode]]
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [...status, data.Data[this.statusCode]]
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter(v => !!v)
      status = status.filter(v => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some(s => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some(s => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },
    async getConfig() {
      let code = ''
      code = this.isCom
        ? 'PROComDraftEditTbConfig'
        : 'PROPartDraftEditTbConfig_new'
      await this.getTableConfig(code)
      // this.fetchData()
    },
    filterData(page) {
      console.log(22)
      const splitAndClean = (input) => input.trim().replace(/\s+/g, ' ').split(' ')

      if (this.curSearch === 1) {
        this.form.Comp_Code = this.searchContent
        this.form.Comp_CodeBlur = ''
      }
      if (this.curSearch === 0) {
        this.form.Comp_CodeBlur = this.searchContent
        this.form.Comp_Code = ''
      }
      if (this.curPartSearch === 1) {
        this.form.Part_CodeBlur = ''
        this.form.Part_Code = this.searchPartContent
      }
      if (this.curPartSearch === 0) {
        this.form.Part_Code = ''
        this.form.Part_CodeBlur = this.searchPartContent
      }

      const f = []
      for (const formKey in this.form) {
        if (this.form[formKey] || this.form[formKey] === false) {
          f.push(formKey)
        }
      }
      if (!f.length) {
        this.setPage()
        !page && (this.pageInfo.page = 1)
        this.pageInfo.total = this.tbData.length
        return
      }

      const checkMatch = (origin, comp) => {
        const _comp = comp.map(code => {
          const [key, value] = code.split('&&&')
          return key
        })
        const _origin = origin.map(code => {
          const [key, value] = code.split('&&&')
          return key
        })
        return _origin.some(item => {
          return _comp.some(value => item.includes(value))
        })
      }
      const checkExactMatch = (origin, comp) => {
        const _comp = comp.map(code => {
          const [key, value] = code.split('&&&')
          return key
        })
        const _origin = origin.map(code => {
          const [key, value] = code.split('&&&')
          return key
        })

        return _origin.some(item => _comp.includes(item))
      }

      const temTbData = this.tbData.filter(v => {
        v.checked = false
        const compCode = v.Component_Codes || []

        if (this.form.Comp_Code.trim()) {
          const compCodeArray = splitAndClean(this.form.Comp_Code)
          if (compCodeArray.length) {
            const flag = checkExactMatch(compCode, compCodeArray)
            console.log(887, compCode, compCodeArray, flag)
            if (!flag) return false
          }
        }

        if (this.form.Comp_CodeBlur.trim()) {
          const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)
          if (compCodeArray.length) {
            const flag = checkMatch(compCode, compCodeArray)
            if (!flag) return false
          }
        }

        if (this.form.Type && v.Type !== this.form.Type) {
          return false
        }

        if (this.form.Part_CodeBlur.trim()) {
          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)
          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {
            return false
          }
        }

        if (this.form.Part_Code.trim()) {
          const partCodeArray = splitAndClean(this.form.Part_Code)
          if (!partCodeArray.includes(v['Part_Code'])) {
            return false
          }
        }

        if (this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {
          return false
        }

        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {
          return false
        }

        if (this.form.Spec.trim() !== '') {
          const specArray = splitAndClean(this.form.Spec)
          if (!specArray.some(spec => v.Spec.includes(spec))) {
            return false
          }
        }
        if (this.searchContent.trim().length) {
          let csCount = 0

          v.componentMap = (v.Component_Codes || []).reduce((acc, code) => {
            const [key, value] = code.split('&&&')
            acc[key] = parseInt(value)
            if (this.curSearch === 1) {
              const compCodeArray = splitAndClean(this.form.Comp_Code)
              if (compCodeArray.length) {
                const flag = checkExactMatch([key], compCodeArray)
                if (flag) {
                  csCount += parseInt(value)
                }
              }
            } else {
              const compCodeArray = splitAndClean(this.form.Comp_CodeBlur)
              if (compCodeArray.length) {
                const flag = checkMatch([key], compCodeArray)
                console.log('pflag', key, compCodeArray, flag, value)
                if (flag) {
                  csCount += parseInt(value)
                }
              }
            }
            return acc
          }, {})
          this.$set(v, 'csCount', Math.min(csCount, v.Can_Schduling_Count))
          this.$set(v, 'csCountWeight', Math.min(v.Can_Schduling_Weight, v.csCount * v.Weight))

          v.searchcount = v.count
          v.searchcountMax = v.maxCount
          // const cs = v.Component_Codes || []
          // let min = 0
          // cs.forEach((element, idx) => {
          //   const [key, value] = element.split('&&&')
          //   min = v.componentMap[key]
          // })

          v.count = v.csCount
        } else {
          v.count = v.Can_Schduling_Count
        }

        // v.Can_Schduling_Count = v.csCount
        // v.Can_Schduling_Weight = v.csCountWeight

        return true
      })

      !page && (this.pageInfo.page = 1)
      this.pageInfo.total = temTbData.length
      this.setPage(temTbData)
      if (this.searchContent.trim().length) {
        this.showSc = true
      }
    },
    handleSearch() {
      this.totalSelection = []
      this.clearSelect()
      if (this.tbData?.length) {
        this.tbData.forEach(item => item.checked = false)
        this.filterData()
      }
      this.showSc = !!this.searchContent.trim().length
    },
    tbSelectChange(array) {
      this.totalSelection = this.tbData.filter(v => v.checked)
    },
    clearSelect() {
      this.$refs.xTable1.clearCheckboxRow()
      this.totalSelection = []
    },
    async fetchData() {
      this.handleReset()
      this.tbLoading = true
      if (this.isCom) {
        await this.getComTbData()
      } else {
        await this.getPartTbData()
      }
      this.initTbData()
      this.filterData()
      this.tbLoading = false
    },
    setPageData() {
      if (this.tbData?.length) {
        this.pageInfo.page = 1
        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)
        this.filterData()
      }
    },
    handleSave(type = 2) {
      if (type === 1) {
        this.addLoading = true
      } else {
        this.saveLoading = true
      }
      setTimeout(() => {
        this.totalSelection.forEach((item) => {
          const intCount = parseInt(item.count)
          if (this.searchContent.trim().length) {
            item.Schduled_Count = item.Can_Schduling_Count

            item.maxCount = item.Can_Schduling_Count
            item.chooseCount = intCount
            item.count = item.Can_Schduling_Count

            item.Can_Schduling_Count = 0
            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight
          } else {
            item.Schduled_Count += intCount
            item.Can_Schduling_Count -= intCount
            item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight
            item.maxCount = intCount
            item.chooseCount = intCount
            item.count = item.Can_Schduling_Count
          }

          item.checked = false
        })
        const cp = deepClone(this.totalSelection)

        // this.$emit('sendSelectList', cp)
        this.addLoading = false
        this.clearSelect()
        // this.setPage()
        this.setPageData()
        if (type === 2) {
          this.$emit('sendSelectList', cp)
          this.$emit('close')
          this.fTable = []
          this.tbData = []
        } else {
          this.$emit('addToTbList', cp)
        }
      }, 0)
    },
    initTbData() {
      // 设置文本框选择的排产数量,设置自定义唯一码
      const objKey = {}
      if (!this.tbData?.length) {
        this.tbData = []
        // this.backendTb = []
        return
      }
      console.log(998, JSON.parse(JSON.stringify(this.tbData)))
      // this.backendTb = deepClone(this.tbData)
      this.tbData = this.tbData.filter(item => {
        this.$set(item, 'count', item.Can_Schduling_Count)
        this.$set(item, 'maxCount', item.Can_Schduling_Count)
        item.uuid = getUnique(this.isCom, item)
        objKey[item.Type] = true
        // let csCount = 0
        // item.componentMap = (item.Component_Codes || []).reduce((acc, code) => {
        //   const [key, value] = code.split('&&&')
        //   acc[key] = parseInt(value)
        //   csCount += parseInt(value)
        //   return acc
        // }, {})
        // this.$set(item, 'csCount', csCount)
        // Object.keys(item.componentMap).forEach(key => {
        //   this.$set(item, key, item.componentMap[key])
        // })

        return !this.addTbKeys.includes(item.uuid)
      })
      //   .map((item) => {
      //   this.$set(item, 'count', item.Can_Schduling_Count)
      //   this.$set(item, 'maxCount', item.Can_Schduling_Count)
      //   // item.uuid = uuidv4()
      //   item.uuid = item.InstallUnit_Id + item.Part_Aggregate_Id
      //   objKey[item.Type] = true
      //
      //   const _selectList = this.selectTbData.filter(v => v.puuid)
      //   console.log('_selectList', _selectList)
      //   // _selectList.forEach((element, idx) => {
      //   //   if(element.puuid === item.uuid){
      //   //
      //   //   }
      //   // })
      //   return item
      // })

      // this.backendTb = deepClone(this.tbData)
    },
    async getComTbData() {
      // const { install, areaId } = this.$route.query
      const { Comp_Codes, ...obj } = this.form
      let codes = []
      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {
        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)
      }
      await GetCanSchdulingComps({
        Ids: this.currentIds,
        ...obj,
        Schduling_Plan_Id: this.scheduleId,
        Comp_Codes: codes,
        InstallUnit_Id: this.installId,
        Area_Id: this.areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            // 已排产赋值
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            // if (v.originalPath) {
            // v.isDisabled = true
            // }
            v.checked = false
            v.initRowIndex = idx
            v.Area_Name = this.nodeLabels.join('/')

            // v.technologyPathDisabled = !!v.Technology_Path
            return v
          })
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    /**
     * 分页
     */
    handlePageChange({ currentPage, pageSize }) {
      if (this.tbLoading) return
      this.pageInfo.page = currentPage
      this.pageInfo.pageSize = pageSize
      this.setPage()
      this.filterData(currentPage)
    },

    setPage(tb = this.tbData) {
      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)
    },

    async getPartTbData() {
      // const { install, areaId } = this.$route.query
      await GetCanSchdulingParts({
        Ids: this.currentIds,
        ...this.form,
        Schduling_Plan_Id: this.scheduleId,
        InstallUnit_Id: this.installId,
        Area_Id: this.areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            if (v.Comp_Import_Detail_Id) {
              v.Part_Used_Process = this.getPartUsedProcess(v)
            }
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            // v.isDisabled = !!v.originalPath
            v.checked = false
            v.initRowIndex = idx
            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false
            // v.technologyPathDisabled = !!v.Technology_Path
            if (!this.isPartPrepare) {
              v.Temp_Part_Used_Process = v.Part_Used_Process
            }
            return v
          })
          this.setPartColumn()
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })

      const submitObj = this.tbData.map(item => {
        return {
          Id: item.Part_Aggregate_Id,
          Type: 1
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          this.tbData.forEach(row => {
            if (stopMap.hasOwnProperty(row.Part_Aggregate_Id)) {
              this.$set(row, 'stopFlag', stopMap[row.Part_Aggregate_Id])
            }
          })
        }
      })
    },
    checkCheckboxMethod({ row }) {
      return !row.stopFlag
    },
    getPartUsedProcess(item) {
      if (item.Scheduled_Used_Process) {
        return item.Scheduled_Used_Process
      }
      if (item.Component_Technology_Path) {
        const list = item.Component_Technology_Path.split('/')
        if (list.includes(item.Part_Used_Process)) {
          return item.Part_Used_Process
        } else if (list.includes(item.Part_Type_Used_Process)) {
          return item.Part_Type_Used_Process
        }
      } else {
        if (item.Part_Used_Process) {
          return item.Part_Used_Process
        } else if (item.Part_Type_Used_Process) {
          return item.Part_Type_Used_Process
        }
      }

      return ''
    },
    setPartColumn() {
      // 纯零件
      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)
      console.log('this.isOwnerNull', this.isOwnerNull)
      if (this.isOwnerNull) {
        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')
        idx !== -1 && this.columns.splice(idx, 1)
      }
    },
    mergeData(list) {
      /*      console.log('list', list)
      console.log('this.backendTb', this.backendTb)
      list
        .forEach((element, index) => {
          const idx = this.backendTb.findIndex(
            (item) => element.puuid && item.uuid === element.puuid
          )
          console.log('idx', idx, this.backendTb[idx])
          console.log('index', index)
          if (idx !== -1) {
            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))
          }
        })

      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)
      console.log('this.tbData', JSON.parse(JSON.stringify(this.tbData)))

      this.filterData()*/
    },
    handleClose() {
      this.$emit('close')
    },
    // activeCellMethod({ row, column, columnIndex }) {
    //   return column.field === 'Schduling_Count'
    // },
    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)
          const list = Data.ColumnList || []
          this.columns = list.filter(v => v.Is_Display)
            .map(item => {
              if (item.Is_Frozen) {
                item.fixed = 'left'
              }
              return item
            })
          // this.columns.push({
          //   Display_Name: '排产数量',
          //   Code: 'Schduling_Count'
          // })
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    getObjectTypeList() {
      GetCompTypeTree({ professional: 'Steel' }).then((res) => {
        if (res.IsSucceed) {
          // this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleReset() {
      this.form.Type_Name = ''
      this.form.Comp_Code = ''
      this.form.Comp_CodeBlur = ''
      this.form.Type = ''
      this.form.Spec = ''
      this.form.InstallUnit_Id = []
      this.form.Part_CodeBlur = ''
      this.form.Part_Code = ''
      this.searchContent = ''
      this.searchPartContent = ''
      this.handleSearch()
    },
    getType() {
      GetPartTypeList({}).then(res => {
        if (res.IsSucceed) {
          this.typeOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    addToList() {
      if (!this.totalSelection.length) return
      this.handleSave(1)
    },
    getInstallUnitIdNameList(id) {
      if (!this.areaId) {
        this.installUnitIdList = []
      } else {
        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {
          this.installUnitIdList = res.Data || []
          // if (this.installUnitIdList.length) {
          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]
          // }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
@import "~@/styles/mixin.scss";
.cs-divider{
  margin:16px 0 0 0;
}
.contentBox {
  height: 75vh;
  display: flex;
  flex-direction: column;

  .main-info{
    display: flex;
    overflow: hidden;
    flex: 1;
    .left{
      height: 100%;
      margin-right: 16px;
      border: 1px solid #eee;
      .cs-tag{
        margin-left: 8px;
        font-size: 12px;
        padding:2px 4px;
        border-radius: 1px;
      }

      .inner-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 16px;
        border-radius: 4px;
        overflow: hidden;

        .tree-search {
          display: flex;

          .search-select {
            margin-right: 8px;
          }
        }

        .tree-x {
          overflow: hidden;
          margin-top: 16px;
          flex: 1;

          .el-tree {
            height: 100%;
          }
        }

        .cs-scroll {
          overflow-y: auto;
          @include scrollBar;
        }

      }
    }
    .right{
      overflow: hidden;
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #eee;
      padding:16px;
    }

  }

  .button {
    margin-top: 16px;
    display: flex;
    justify-content: end;
  }

  .tb-wrapper {
    flex: 1 1 auto;
  }

  .data-info{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
  }
}
.fourGreen {
  color: #00C361;
  font-style: normal;
}

.fourOrange {
  color: #FF9400;
  font-style: normal;
}

.fourRed {
  color: #FF0000;
  font-style: normal;
}

.cs-blue {
  color: #5AC8FA;
}

.orangeBg{
  background: rgba(255,148,0,0.1);
}

.redBg{
  background: rgba(252,107,127,0.1);
}
.greenBg{
  background: rgba(0, 195, 97, 0.10);
}
.cs-input-x{
  display: flex;
}
</style>

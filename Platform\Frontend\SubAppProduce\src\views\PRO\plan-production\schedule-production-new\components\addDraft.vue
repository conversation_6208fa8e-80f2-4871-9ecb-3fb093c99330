<template>
  <div class="contentBox">
    <el-form ref="form" :model="form" label-width="90px">
      <el-row>
        <el-col :span="7">
          <el-form-item label="构件编号" prop="Comp_Codes">
            <!--              <el-input
                v-model="form.Comp_Code"
                clearable
                style="width: 45%"
                placeholder="请输入(空格区分/多个搜索)"
                type="text"
              />
              <el-input
                v-model="form.Comp_CodeBlur"
                clearable
                style="width: 45%;margin-left: 16px"
                placeholder="模糊查找(请输入关键字)"
                type="text"
              />-->
            <el-input
              v-model="searchContent"
              clearable
              class="input-with-select w100"
              placeholder="请输入(空格区分/多个搜索)"
              size="small"
            >
              <el-select
                slot="prepend"
                v-model="curSearch"
                placeholder="请选择"
                style="width: 100px"
              >
                <el-option label="精准查询" :value="1" />
                <el-option label="模糊查询" :value="0" />
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="构件类型" prop="Type">
            <el-tree-select
              ref="treeSelectObjectType"
              v-model="form.Type"
              style="width: 100%"
              class="cs-tree-x"
              :select-params="treeSelectParams"
              :tree-params="ObjectTypeList"
              value-key="Id"
            />
            <!--              <el-select v-model="form.Type" placeholder="请选择" clearable @clear="filterData">
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in comTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>-->
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="规格" prop="Spec" label-width="50px">
            <el-input v-model.trim="form.Spec" placeholder="请输入" clearable />
          </el-form-item>
        </el-col>
        <el-col v-if="isVersionFour" :span="3">
          <el-form-item label="批次" label-width="50px" prop="Create_UserName">
            <el-select
              v-model="form.InstallUnit_Id"
              filterable
              clearable
              multiple
              style="width: 100%"
              placeholder="请选择"
            >
              <el-option
                v-for="item in installUnitIdList"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-button style="margin-left: 10px" @click="handleReset">重置</el-button>
          <el-button style="margin-left: 10px" type="primary" @click="handleSearch()">查询</el-button>
          <el-button :loading="addLoading" style="margin-left: 10px" type="primary" @click="addToList()">加入列表</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="tb-wrapper">
      <vxe-table
        ref="xTable1"
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        empty-text="暂无数据"
        height="auto"
        show-overflow
        :checkbox-config="{checkField: 'checked', checkMethod: checkCheckboxMethod}"
        :loading="tbLoading"
        :row-config="{isCurrent: true, isHover: true }"
        class="cs-vxe-table"
        align="left"
        stripe
        :data="fTable"
        resizable
        :edit-config="{trigger: 'click', mode: 'cell'}"
        :tooltip-config="{ enterable: true }"
        @checkbox-all="tbSelectChange"
        @checkbox-change="tbSelectChange"
      >
        <vxe-column fixed="left" type="checkbox" width="60" />
        <template v-for="item in columns">
          <vxe-column
            v-if="item.Code === 'Is_Component'"
            :key="item.Code"
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            sortable
            :min-width="item.Width"
          >
            <template #default="{ row }">
              <el-tag :type="row.Is_Component ? 'danger' : 'success'">{{
                row.Is_Component ? "否" : "是"
              }}</el-tag>
            </template>
          </vxe-column>
          <vxe-column
            v-else-if="['Part_Code','Comp_Code'].includes(item.Code)"
            :key="item.Code"
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
            sortable
            :min-width="item.Width"
          >
            <template #default="{ row }">
              <el-tag v-if="row.Is_Change" style="margin-right: 8px;" type="danger">变</el-tag>
              <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
              {{ row[item.Code] | displayValue }}
            </template>
          </vxe-column>
          <vxe-column
            v-else
            :key="item.Code"
            :align="item.Align"
            :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
            show-overflow="tooltip"
            sortable
            :field="item.Code"
            :title="item.Display_Name"
            :min-width="item.Width"
          />
        </template>
      </vxe-table>
    </div>
    <div class="data-info">
      <el-tag
        size="medium"
        class="info-x"
      >已选 {{ totalSelection.length }} 条数据
      </el-tag>
      <vxe-pager
        border
        background
        :loading="tbLoading"
        :current-page.sync="pageInfo.page"
        :page-size.sync="pageInfo.pageSize"
        :page-sizes="pageInfo.pageSizes"
        :total="pageInfo.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        size="small"
        @page-change="handlePageChange"
      />
    </div>
    <div class="button">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        :disabled="!totalSelection.length"
        :loading="saveLoading"
        @click="handleSave(2)"
      >保存</el-button>
    </div>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import { GetCanSchdulingComps, GetStopList } from '@/api/PRO/production-task'
import { GetCanSchdulingParts, GetPartList } from '@/api/PRO/production-part'
import { v4 as uuidv4 } from 'uuid'
import { debounce, deepClone } from '@/utils'
import { tablePageSize } from '@/views/PRO/setting'
import { GetCompTypeTree } from '@/api/PRO/professionalType'
import { GetPartTypeList } from '@/api/PRO/partType'
import { mapGetters } from 'vuex'
import { GetInstallUnitIdNameList } from '@/api/PRO/project'

export default {
  props: {
    scheduleId: {
      type: String,
      default: ''
    },
    pageType: {
      type: String,
      default: 'com'
    },
    showDialog: {
      type: Boolean,
      default: false
    },
    areaId: {
      type: String,
      default: ''
    },
    installId: {
      type: String,
      default: ''
    },
    currentIds: {
      type: String,
      default: ''
    },
    isPartPrepare: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageInfo: {
        page: 1,
        pageSize: 500,
        pageSizes: tablePageSize,
        total: 0
      },
      form: {
        Comp_Code: '',
        Comp_CodeBlur: '',
        Part_CodeBlur: '',
        Part_Code: '',
        Type_Name: '',
        InstallUnit_Id: '',
        Spec: '',
        Type: ''
      },
      searchContent: '',
      curSearch: 1,
      isOwnerNull: true,
      tbLoading: false,
      addLoading: false,
      saveLoading: false,
      columns: [],
      fTable: [],
      tbConfig: {},
      TotalCount: 0,
      Page: 0,
      multipleSelection: [],
      installUnitIdList: [],
      totalSelection: [],
      search: () => ({}),
      treeSelectParams: {
        placeholder: '请选择',
        clearable: true
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      typeOption: []
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    ...mapGetters('tenant', ['isVersionFour'])
  },
  watch: {
    showDialog(newValue) {
      newValue && (this.saveLoading = false)
    }
  },
  mounted() {
    this.getConfig()
    if (this.isCom) {
      this.getObjectTypeList()
    } else {
      this.getType()
    }
    this.search = debounce(this.fetchData, 800, true)
    this.getInstallUnitIdNameList()
  },
  methods: {
    async getConfig() {
      let code = ''
      code = this.isCom
        ? 'PROComDraftEditTbConfig'
        : 'PROPartDraftEditTbConfig_new'
      await this.getTableConfig(code)
      this.fetchData()
    },
    filterData(page) {
      if (this.curSearch === 1) {
        this.form.Comp_Code = this.searchContent
        this.form.Comp_CodeBlur = ''
      }
      if (this.curSearch === 0) {
        this.form.Comp_CodeBlur = this.searchContent
        this.form.Comp_Code = ''
      }

      const f = []
      for (const formKey in this.form) {
        if (this.form[formKey] || this.form[formKey] === false) {
          f.push(formKey)
        }
      }
      if (!f.length) {
        this.setPage()
        !page && (this.pageInfo.page = 1)
        this.pageInfo.total = this.tbData.length
        return
      }
      const temTbData = this.tbData.filter(v => {
        v.checked = false

        const splitAndClean = (input) => input.trim().replace(/\s+/g, ' ').split(' ')

        if (this.form.Comp_Code.trim()) {
          const compCodeArray = splitAndClean(this.form.Comp_Code)
          if (!compCodeArray.includes(v['Comp_Code'])) {
            return false
          }
        }

        if (this.form.Comp_CodeBlur.trim()) {
          const compCodeBlurArray = splitAndClean(this.form.Comp_CodeBlur)
          if (!compCodeBlurArray.some(code => v['Comp_Code'].includes(code))) {
            return false
          }
        }

        if (this.form.Type && v.Type !== this.form.Type) {
          return false
        }

        if (this.form.Part_CodeBlur.trim()) {
          const partCodeBlurArray = splitAndClean(this.form.Part_CodeBlur)
          if (!partCodeBlurArray.some(code => v['Part_Code'].includes(code))) {
            return false
          }
        }

        if (this.form.Part_Code.trim()) {
          const partCodeArray = splitAndClean(this.form.Part_Code)
          if (!partCodeArray.includes(v['Part_Code'])) {
            return false
          }
        }

        if (this.isVersionFour && this.form.InstallUnit_Id.length && !this.form.InstallUnit_Id.includes(v.InstallUnit_Id)) {
          return false
        }

        if (this.form.Type_Name !== '' && v.Type_Name !== this.form.Type_Name) {
          return false
        }

        if (this.form.Spec.trim() !== '') {
          const specArray = splitAndClean(this.form.Spec)
          if (!specArray.some(spec => v.Spec.includes(spec))) {
            return false
          }
        }

        return true
      })

      !page && (this.pageInfo.page = 1)
      this.pageInfo.total = temTbData.length
      this.setPage(temTbData)
    },
    handleSearch() {
      this.totalSelection = []
      this.clearSelect()
      if (this.tbData?.length) {
        this.tbData.forEach(item => item.checked = false)
        this.filterData()
      }
    },
    handleReset() {
      this.form.Type_Name = ''
      this.form.Comp_Code = ''
      this.form.Comp_CodeBlur = ''
      this.form.Type = ''
      this.form.Spec = ''
      this.searchContent = ''
      this.handleSearch()
    },
    handleSelect(data) {
      this.multipleSelection = data
    },
    tbSelectChange(array) {
      this.totalSelection = this.tbData.filter(v => v.checked)
    },
    clearSelect() {
      this.$refs.xTable1.clearCheckboxRow()
      this.totalSelection = []
    },
    async fetchData() {
      this.tbLoading = true
      if (this.isCom) {
        await this.getComTbData()
      } else {
        await this.getPartTbData()
      }
      this.initTbData()
      this.filterData()
      this.tbLoading = false
    },
    setPageData() {
      if (this.tbData?.length) {
        this.pageInfo.page = 1
        this.tbData = this.tbData.filter(v => v.Can_Schduling_Count > 0)
        this.filterData()
      }
    },
    handleSave(type = 2) {
      if (type === 1) {
        this.addLoading = true
      } else {
        this.saveLoading = true
      }
      setTimeout(() => {
        this.totalSelection.forEach((item) => {
          const intCount = parseInt(item.count)
          item.Schduled_Count += intCount
          item.Can_Schduling_Count -= intCount
          item.Can_Schduling_Weight = item.Can_Schduling_Count * item.Weight
          item.maxCount = item.Can_Schduling_Count
          item.chooseCount = intCount
          item.count = item.Can_Schduling_Count
          item.checked = false
        })
        const cp = deepClone(this.totalSelection)

        this.$emit('sendSelectList', cp)
        this.addLoading = false
        this.clearSelect()
        // this.setPage()
        this.setPageData()
        console.log('type', type)
        if (type === 2) {
          this.$emit('close')
        }
      }, 0)
    },
    initTbData() {
      // 设置文本框选择的排产数量,设置自定义唯一码
      const objKey = {}
      if (!this.tbData?.length) {
        this.tbData = []
        this.backendTb = []
        return
      }
      this.tbData.forEach((item) => {
        this.$set(item, 'count', item.Can_Schduling_Count)
        this.$set(item, 'maxCount', item.Can_Schduling_Count)
        item.uuid = uuidv4()
        objKey[item.Type] = true
      })
      this.backendTb = deepClone(this.tbData)
    },
    async getComTbData() {
      // const { install, areaId } = this.$route.query
      const { Comp_Codes, ...obj } = this.form
      let codes = []
      if (Object.prototype.toString.call(Comp_Codes) === '[object String]') {
        codes = Comp_Codes && Comp_Codes.split(' ').filter(v => !!v)
      }
      await GetCanSchdulingComps({
        Ids: this.currentIds,
        ...obj,
        Schduling_Plan_Id: this.scheduleId,
        Comp_Codes: codes,
        InstallUnit_Id: this.installId,
        Area_Id: this.areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            // 已排产赋值
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            // if (v.originalPath) {
            // v.isDisabled = true
            // }
            v.checked = false
            v.initRowIndex = idx
            // v.technologyPathDisabled = !!v.Technology_Path
            return v
          })
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      const submitObj = this.tbData.map(item => {
        return {
          Id: item.Comp_Import_Detail_Id,
          Type: 2
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          this.tbData.forEach(row => {
            if (stopMap.hasOwnProperty(row.Comp_Import_Detail_Id)) {
              this.$set(row, 'stopFlag', stopMap[row.Comp_Import_Detail_Id])
            }
          })
        }
      })
    },
    checkCheckboxMethod({ row }) {
      return !row.stopFlag
    },
    /**
     * 分页
     */
    handlePageChange({ currentPage, pageSize }) {
      if (this.tbLoading) return
      this.pageInfo.page = currentPage
      this.pageInfo.pageSize = pageSize
      this.setPage()
      this.filterData(currentPage)
    },

    setPage(tb = this.tbData) {
      this.fTable = tb.slice((this.pageInfo.page - 1) * this.pageInfo.pageSize, this.pageInfo.page * this.pageInfo.pageSize)
    },

    async getPartTbData() {
      // const { install, areaId } = this.$route.query
      await GetCanSchdulingParts({
        Ids: this.currentIds,
        ...this.form,
        Schduling_Plan_Id: this.scheduleId,
        InstallUnit_Id: this.installId,
        Area_Id: this.areaId
      }).then((res) => {
        if (res.IsSucceed) {
          this.pageInfo.total = res.Data.length
          this.tbData = res.Data.map((v, idx) => {
            v.originalPath = v.Scheduled_Technology_Path ? v.Scheduled_Technology_Path : ''
            v.Workshop_Id = v.Scheduled_Workshop_Id
            v.Workshop_Name = v.Scheduled_Workshop_Name
            if (v.Comp_Import_Detail_Id) {
              v.Part_Used_Process = this.getPartUsedProcess(v)
            }
            v.Technology_Path = v.Scheduled_Technology_Path || v.Technology_Path
            // v.isDisabled = !!v.originalPath
            v.checked = false
            v.initRowIndex = idx
            // v.partUsedProcessDisabled = this.isPartPrepare ? !!v.Part_Used_Process : false
            // v.technologyPathDisabled = !!v.Technology_Path
            if (!this.isPartPrepare) {
              v.Temp_Part_Used_Process = v.Part_Used_Process
            }
            return v
          })
          this.setPartColumn()
          this.setPage()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getPartUsedProcess(item) {
      if (item.Scheduled_Used_Process) {
        return item.Scheduled_Used_Process
      }
      if (item.Component_Technology_Path) {
        const list = item.Component_Technology_Path.split('/')
        if (list.includes(item.Part_Used_Process)) {
          return item.Part_Used_Process
        } else if (list.includes(item.Part_Type_Used_Process)) {
          return item.Part_Type_Used_Process
        }
      } else {
        if (item.Part_Used_Process) {
          return item.Part_Used_Process
        } else if (item.Part_Type_Used_Process) {
          return item.Part_Type_Used_Process
        }
      }

      return ''
    },
    setPartColumn() {
      // 纯零件
      this.isOwnerNull = this.tbData.every(v => !v.Comp_Import_Detail_Id)
      console.log('this.isOwnerNull', this.isOwnerNull)
      if (this.isOwnerNull) {
        const idx = this.columns.findIndex(v => v.Code === 'Component_Code')
        idx !== -1 && this.columns.splice(idx, 1)
      }
    },
    mergeData(list) {
      list
        .forEach((element) => {
          const idx = this.backendTb.findIndex(
            (item) => element.puuid && item.uuid === element.puuid
          )
          if (idx !== -1) {
            this.tbData.splice(idx, 0, deepClone(this.backendTb[idx]))
          }
        })

      this.tbData.sort((a, b) => a.initRowIndex - b.initRowIndex)

      this.filterData()
    },
    handleClose() {
      this.$emit('close')
    },
    // activeCellMethod({ row, column, columnIndex }) {
    //   return column.field === 'Schduling_Count'
    // },
    async getTableConfig(code) {
      await GetGridByCode({
        code
      }).then((res) => {
        const { IsSucceed, Data, Message } = res
        if (IsSucceed) {
          this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
          this.pageInfo.pageSize = Number(this.tbConfig.Row_Number)
          const list = Data.ColumnList || []
          this.columns = list.filter(v => v.Is_Display)
            .map(item => {
              if (item.Is_Frozen) {
                item.fixed = 'left'
              }
              return item
            })
          // this.columns.push({
          //   Display_Name: '排产数量',
          //   Code: 'Schduling_Count'
          // })
        } else {
          this.$message({
            message: Message,
            type: 'error'
          })
        }
      })
    },
    getObjectTypeList() {
      GetCompTypeTree({ professional: 'Steel' }).then((res) => {
        if (res.IsSucceed) {
          this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectObjectType.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    getType() {
      GetPartTypeList({}).then(res => {
        if (res.IsSucceed) {
          this.typeOption = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    addToList() {
      if (!this.totalSelection.length) return
      this.handleSave(1)
    },
    getInstallUnitIdNameList(id) {
      if (!this.areaId) {
        this.installUnitIdList = []
      } else {
        GetInstallUnitIdNameList({ Area_Id: this.areaId }).then(res => {
          this.installUnitIdList = res.Data || []
          // if (this.installUnitIdList.length) {
          //   this.form.InstallUnit_Id = [this.installUnitIdList[0].Id]
          // }
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
.contentBox {
  display: flex;
  flex-direction: column;

  .button {
    margin-top: 16px;
    display: flex;
    justify-content: end;
  }

  .tb-wrapper {
    flex: 1 1 auto;
    height: 50vh;
  }

  .data-info{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
  }
}
</style>

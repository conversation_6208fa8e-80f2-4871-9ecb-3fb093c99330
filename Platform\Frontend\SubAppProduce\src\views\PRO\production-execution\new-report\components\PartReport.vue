<template>
  <div class="inner-wrapper">
    <div class="cs-top">
      <span class="cs-title">
        报工数量：{{ reportNum }}      报工重量：{{ reportWeight }}（t）
      </span>
      <el-form v-if="isNest" ref="form" :model="form" label-width="80px">
        <el-form-item required label="炉批号">
          <el-input v-model.trim="form.Lot" placeholder="请输入" clearable="" />
        </el-form-item>
      </el-form>
    </div>
    <div class="tb-wrapper">
      <vxe-table
        :empty-render="{name: 'NotData'}"
        show-header-overflow
        :loading="tbLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-text="拼命加载中"
        empty-text="暂无数据"
        class="cs-vxe-table"
        height="100%"
        align="left"
        stripe
        :data="fTable"
        resizable
        :edit-config="{trigger: 'click', mode: 'cell'}"
        :tooltip-config="{ enterable: true}"
      >
        <template v-for="item in columns">
          <vxe-column
            :key="item.Code"
            :min-width="item.Width"
            width="auto"
            show-overflow="tooltip"
            sortable
            :fixed="['Target_Team_Id','Can_Receive_Count','Processing_Count'].includes(item.Code)?'right':''"
            :align="item.Align"
            :field="item.Code"
            :title="item.Display_Name"
          >
            <template v-if="item.Code==='Processing_Count'" #default="{ row }">
              <vxe-input v-model.number="row.Processing_Count" min="0" :max="row.processCountMax" placeholder="请输入" type="integer" @change="reportNumChange" />
            </template>
            <template v-else #default="{ row }">
              <span> {{ row[item.Code] | displayValue }}</span>
            </template>
          </vxe-column>

        </template>
      </vxe-table>
    </div>
    <Pagination
      :total="total"
      max-height="100%"
      :page-sizes="tablePageSize"
      :page.sync="queryInfo.Page"
      :limit.sync="queryInfo.PageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="handlePageChange"
    />
    <footer>
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" :loading="btnLoading" @click="submit">确定</el-button>
    </footer>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { tablePageSize } from '@/views/PRO/setting'
import { GetGridByCode } from '@/api/sys'
import { SimplifiedProcessing } from '@/api/PRO/production-task'
import { mapGetters } from 'vuex'

export default {
  components: {
    Pagination
  },
  props: {
    isNest: {
      type: Boolean,
      default: false
    },

  },
  data() {
    return {
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      tablePageSize: tablePageSize,
      tbLoading: false,
      btnLoading: false,

      fTable: [],
      total: 0,
      columns: [],
      reportNum: 0,
      reportWeight: 0,
      form: {
        Lot: ''
      }
    }
  },
  computed: {
    ...mapGetters('factoryInfo', ['Is_Skip_Warehousing_Operation', 'Is_Part_Prepare', 'Nested_Must_Before_Processing'])
  },
  async mounted() {
    await this.getTableConfig('PROProductionPartNewReport')
  },
  methods: {
    setPage(tb = this.tbData) {
      this.fTable = tb.slice((this.queryInfo.Page - 1) * this.queryInfo.PageSize, this.queryInfo.Page * this.queryInfo.PageSize)
    },
    getTotalInfo() {
      let _n = 0
      let _w = 0
      this.tbData
        .forEach((element, idx) => {
          _n += element.Processing_Count
          _w += element.Processing_Count * element.Weight
        })
      this.reportNum = _n
      this.reportWeight = (_w / 1000).toFixed(5) / 1
    },
    onCancel() {
      this.$emit('close')
    },

    async getFactoryInfo() {
      await this.$store.dispatch('factoryInfo/getWorkshop')
    },

    async init(tbData) {
      console.log(tbData)
      this.tbLoading = true
      await this.getFactoryInfo()
      this.tbData = tbData.map(item => {
        let min = 0
        if (item.Prepare_Count === null) {
          min = item.Ready_Process_Count
        } else {
          min = this.Is_Part_Prepare ? Math.min(item.Prepare_Count, item.Ready_Process_Count) : item.Ready_Process_Count
        }
        if (item.Is_Nest&&this.Nested_Must_Before_Processing) {
          item.processCountMax = Math.min(item.Nesting_Amount || 0, item.Ready_Process_Count)
          item.Processing_Count = item.processCountMax
        } else {
          item.processCountMax = item.Ready_Process_Count
          item.Processing_Count = min
        }
        return item
      })

      this.total = this.tbData.length
      this.getTotalInfo()
      this.setPage()
      setTimeout(() => {
        this.tbLoading = false
      }, 500)
    },
    submit() {
      if (this.isNest && !this.form.Lot) {
        this.$message({
          message: '请输入炉批号',
          type: 'warning'
        })
        return
      }

      this.btnLoading = true
      console.log(this.tbData)
      const list = this.tbData
        .map((element, idx) => {
          const obj = {
            Task_Id: element.Task_Id,
            Processing_Count: element.Processing_Count,
            Target_Team_Id: element.Target_Team_Id,
            Next_Task_Id: element.Next_Task_Id,
            Lot: this.form.Lot,
            Type: 1
          }
          return obj
        }).filter(v => v.Processing_Count !== 0)
      if (!list.length) {
        this.$message({
          message: '数量不能为空',
          type: 'warning'
        })
        this.btnLoading = false
        return
      }

      SimplifiedProcessing(list).then(res => {
        if (res.IsSucceed) {
          this.btnLoading = false
          this.$message({
            message: '操作成功',
            type: 'success'
          })
          this.$emit('close')
          this.$emit('refresh')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
          this.btnLoading = false
        }
      })
    },
    handlePageChange({ page, limit }) {
      if (this.tbLoading) return
      this.queryInfo.Page = page
      this.queryInfo.PageSize = limit
      this.setPage()
    },
    reportNumChange() {
      this.getTotalInfo()
    },
    getTableConfig(code) {
      return new Promise((resolve) => {
        GetGridByCode({
          code
        }).then(res => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message({
                message: '表格配置不存在',
                type: 'error'
              })
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            this.columns = (Data.ColumnList.filter(v => v.Is_Display) || []).map(item => {
              item.Is_Resizable = true
              item.Is_Sortable = true
              return item
            })
            this.queryInfo.PageSize = +Data.Grid.Row_Number || tablePageSize[0]
            resolve(this.columns)
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.inner-wrapper{
  height: 100%;
  display: flex;
  flex-direction: column;
}
.cs-title{
  display: inline-block;
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 18px;
}
.tb-wrapper{
  flex:1;
  overflow: hidden;
}
.pagination-container {
  text-align: right;
  padding: 16px;
  margin: 0;
}
footer{
  text-align: right;
}
.cs-top{
  display: flex;
}

</style>

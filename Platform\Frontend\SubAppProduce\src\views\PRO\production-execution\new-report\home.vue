<template>
  <div class="app-container abs100">
    <div
      v-loading="pgLoading"
      element-loading-text="加载中"
      class="h100 app-wrapper"
    >
      <ExpandableSection v-model="showExpand" :width="300" class="cs-left fff">
        <div class="inner-wrapper">
          <div class="cs-search">
            <el-row>
              <el-col :span="24" class="team-select">
                <el-select
                  v-model="form.Working_Team_Id"
                  clearable
                  placeholder="请选择"
                  @change="handleTeamChange"
                >
                  <i slot="prefix">
                    <img src="@/assets/PRO/icon-search.png" alt="">
                  </i>
                  <el-option
                    v-for="item in teamOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24" :gutter="8">
              <el-col :span="10">
                <el-select v-model="statusType" clearable placeholder="请选择">
                  <el-option label="所有状态" value="" />
                  <el-option label="待报工" value="待报工" />
                  <el-option label="报工完成" value="报工完成" />
                  <el-option label="排产未完成" value="排产未完成" />
                </el-select>
              </el-col>
              <el-col :span="14">
                <el-input
                  v-model.trim="projectName"
                  placeholder="搜索..."
                  size="small"
                  clearable
                  suffix-icon="el-icon-search"
                />
              </el-col>
            </el-row>
          </div>

          <div class="cs-tree cs-scroll">
            <tree-detail
              ref="tree"
              :default-expand-all="expandAll"
              icon="icon-folder"
              is-custom-filter
              :custom-filter-fun="customFilterFun"
              :loading="treeLoading"
              :tree-data="treeData"
              show-status
              show-detail
              :filter-text="filterText"
              :expanded-key="expandedKey"
              :can-node-click="false"
              @handleNodeClick="handleNodeClick"
              @saveSortFinish="saveSortFinish"
            >
              <template #csLabel="{ showStatus, data }">
                <span
                  v-if="!data.ParentNodes"
                  class="cs-blue"
                >({{ data.Code }})</span>{{ data.Label }}
                <template v-if="showStatus">
                  <i
                    v-if="data.Data[statusKey]"
                    :class="[
                      data.Data[statusKey] == '报工完成'
                        ? 'fourGreen'
                        : data.Data[statusKey] == '待报工'
                          ? 'fourOrange'
                          : data.Data[statusKey] == '排产未完成'
                            ? 'fourRed'
                            : '',
                    ]"
                  >
                    <span>({{ data.Data[statusKey] }})</span>
                  </i>
                </template>
              </template>
            </tree-detail>
          </div>
        </div>
      </ExpandableSection>
      <div class="cs-right fff">
        <div v-show="taskList.length > 0" class="cs-top-wapper">
          <div
            v-if="IsShowBtn"
            class="btn"
            :class="{ disabled: disableLeftBtn }"
            @click="clickMove('left')"
          >
            <i class="el-icon-arrow-left" />
          </div>
          <div
            ref="middleWapper"
            class="middle-wapper"
            @mousedown.prevent="handleMouseDown"
            @mouseleave.prevent="handleMouseLeave"
            @mouseup.prevent="handleMouseUp"
            @mousemove.prevent="handleMouseMove"
          >
            <div
              ref="boxWapper"
              class="box-wapper"
              :style="{ transform: `translateX(${offset}px)` }"
            >
              <div
                v-for="(item, index) in taskList"
                :key="index"
                :ref="'task_' + item.Task_Code"
                :class="[
                  'item',
                  form.Task_Code === item.Task_Code ? 'active' : '',
                ]"
                @click="handleTask(item)"
              >
                <div v-if="item.Return_Count > 0" class="flag">退</div>
                <div v-if="item.Is_Nest" class="flag2">
                  <span class="flag2-txt">套</span>
                </div>
                <div class="content">
                  <div class="title">
                    <span class="name"> {{ item.Task_Code }}</span>
                    <!--                    <span v-if="isCom && item.Part_Completion_Rate !== null" class="precent">
                      {{ item.Part_Completion_Rate > 1 ? 100 : Math.round(item.Part_Completion_Rate * 100) }}%
                    </span>-->
                    <span
                      v-if="checkShowP(item.Need_Part_Amount)"
                      class="precent"
                    >{{ item.percentage }}</span>
                  </div>
                  <div class="detail">
                    <div class="left">
                      <div class="info">要求：{{ item.Demand_Date }}</div>
                      <div class="info">
                        总量：{{ item.Total_Count }}/{{
                          parseFloat((item.Total_Weight / 1000).toFixed(3))
                        }}t
                      </div>
                    </div>

                    <div
                      v-if="isCom && checkShowP(item.Need_Part_Amount)"
                      class="right"
                      @click.stop="viewPart(item)"
                    >
                      查看零件
                    </div>
                  </div>
                </div>
                <i
                  v-if="form.Task_Code === item.Task_Code"
                  class="el-icon-caret-bottom"
                />
              </div>
            </div>
          </div>
          <div
            v-if="IsShowBtn"
            class="btn"
            :class="{ disabled: disableRightBtn }"
            @click="clickMove('right')"
          >
            <i class="el-icon-arrow-right" />
          </div>
        </div>

        <div class="cs-middle-wapper">
          <div class="cs-middle-wapper-left">
            <el-button
              type="primary"
              :disabled="!multipleSelection.length || multipleSelection.some(item=>item.stopFlag)"
              @click="handleReport()"
            >报工</el-button>
            <el-checkbox
              v-if="!isCom"
              v-model="showNestPart"
              class="cs-checkbox"
              @change="showNestChange"
            >显示套料零件</el-checkbox>
          </div>

          <el-form ref="form" :model="form" label-width="80px" inline>
            <el-form-item v-if="!isCom" label="排版编号">
              <el-input
                v-model="form.Nesting_Result_Name"
                placeholder="请输入"
                clearable
              />
            </el-form-item>

            <el-form-item label="生产状态">
              <el-select
                v-model="form.Production_Status"
                placeholder="请选择生产状态"
                style="width: 140px"
                clearable
              >
                <el-option label="未完成" value="未完成" />
                <el-option label="已完成" value="已完成" />
              </el-select>
            </el-form-item>

            <el-form-item :label="isCom ? '构件名称' : '零件名称'">
              <el-input
                v-model="form.Code_Like"
                :placeholder="isCom ? '请输入构件名称' : '请输入零件名称'"
                clearable
              />
            </el-form-item>
            <el-form-item label="规格" label-width="50px">
              <el-input
                v-model="form.Spec_Like"
                style="width: 140px"
                placeholder="请输入规格"
                clearable
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="refresh">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="cs-bottom-wapper">
          <div class="fff tb-x">
            <vxe-table
              :empty-render="{ name: 'NotData' }"
              show-header-overflow
              :loading="tbLoading"
              element-loading-spinner="el-icon-loading"
              element-loading-text="拼命加载中"
              empty-text="暂无数据"
              class="cs-vxe-table"
              height="100%"
              align="left"
              stripe
              :data="tbData"
              resizable
              :tooltip-config="{ enterable: true }"
              :checkbox-config="{
                checkField: 'checked',
                trigger: 'row',
                checkMethod: checCheckboxkMethod3,
              }"
              @checkbox-all="multiSelectedChange"
              @checkbox-change="multiSelectedChange"
            >
              <vxe-column fixed="left" type="checkbox" />
              <template v-for="item in columns">
                <vxe-column
                  :key="item.Code"
                  :min-width="item.Width"
                  width="auto"
                  :fixed="
                    ['Comp_Code', 'Part_Code'].includes(item.Code) ? 'left' : ''
                  "
                  show-overflow="tooltip"
                  sortable
                  :align="item.Align"
                  :field="item.Code"
                  :title="item.Display_Name"
                >
                  <template
                    v-if="
                      item.Code === 'Comp_Code' || item.Code === 'Part_Code'
                    "
                    #default="{ row }"
                  >
                    <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
                    <el-tag
                      v-if="row.Is_Change"
                      style="margin-right: 6px"
                      type="danger"
                      class="cs-tag"
                    >变</el-tag>
                    <el-link
                      v-if="row.DwgCount > 0"
                      type="primary"
                      @click.stop="handleDwg(row)"
                    >
                      {{ row[item.Code] | displayValue }}</el-link>
                    <span v-else> {{ row[item.Code] | displayValue }}</span>
                  </template>
                  <template
                    v-else-if="item.Code === 'Prepare_Count'"
                    #default="{ row }"
                  >
                    <el-link
                      v-if="typeof row.Prepare_Count === 'number'"
                      type="primary"
                      @click.stop="handleQitao(row)"
                    >
                      {{ row[item.Code] || 0 }}</el-link>
                    <span v-else> {{ row[item.Code] | displayValue }}</span>
                  </template>
                  <template
                    v-else-if="item.Code === 'Production_Status'"
                    #default="{ row }"
                  >
                    <span
                      :class="
                        row.Production_Status === '未完成'
                          ? 'fourRed'
                          : row.Production_Status === '已完成'
                            ? 'fourGreen'
                            : ''
                      "
                    >{{ row.Production_Status }}</span>
                  </template>
                  <template
                    v-else-if="item.Code === 'Working_Process_Name'"
                    #default="{ row }"
                  >
                    <el-link type="primary" @click="getData(row)">
                      {{ row[item.Code] }}</el-link>
                  </template>
                  <template v-else #default="{ row }">
                    <span> {{ row[item.Code] | displayValue }}</span>
                  </template>
                </vxe-column>
              </template></vxe-table>
          </div>
          <div class="data-info">
            <el-tag
              size="medium"
              class="info-x"
            >已选 {{ multipleSelection.length }} 条数据
            </el-tag>
            <Pagination
              :total="total"
              :page-sizes="tablePageSize"
              :page.sync="queryInfo.Page"
              :limit.sync="queryInfo.PageSize"
              @pagination="pageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-dialogDrag
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="width"
      class="plm-custom-dialog"
      top="10vh"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :is-nest="isNest"
        :tid="tid"
        :comp-code="compCode"
        @close="handleClose"
        @refresh="refresh"
      />
    </el-dialog>

    <ProcessDialog ref="process" :show-search="false" />
  </div>
</template>

<script>
import ExpandableSection from '@/components/ExpandableSection/index.vue'
import Pagination from '@/components/Pagination'
import PartDetail from './components/PartDetail'
import { tablePageSize } from '@/views/PRO/setting'
import getTbInfo from '@/mixins/PRO/get-table-info'
import ComReport from './components/ComReport'
import PartReport from './components/PartReport.vue'
import TreeDetail from '@/components/TreeDetail/index.vue'
import Qitao from './components/Qitao'
import { GetProjectAreaTreeList } from '@/api/PRO/project'
import { GetTeamListByUser } from '@/api/PRO/technology-lib'
import {
  GetCompTaskPageList,
  GetCompTaskPartCompletionStock,
  GetDwg
} from '@/api/PRO/production-task'
import {
  GetCompTaskList,
  GetSimplifiedPartTaskList,
  GetSimplifiedPartTaskPageList
} from '@/api/PRO/production-report-new'
import { GetStopList } from '@/api/PRO/production-task'
import { parseOssUrl } from '@/utils/file'
import { mapGetters } from 'vuex'
import scroll from './mixin/scroll'
import { timeFormat } from '@/utils/timeFormat'
import { GetProcessingProgressTask } from '@/api/PRO/processingprogress'
import ProcessDialog from '@/views/PRO/basic-information/production-scheduling/process/processdialog.vue'
import numeral from 'numeral'
const SPLIT_SYMBOL = '$_$'
export default {
  components: {
    ExpandableSection,
    Pagination,
    ComReport,
    TreeDetail,
    Qitao,
    PartDetail,
    ProcessDialog,
    PartReport
  },
  mixins: [getTbInfo, scroll],
  inject: ['pageType'],
  data() {
    return {
      tid: '',
      compCode: '',
      projectName: '',
      statusType: '待报工',
      expandedKey: '',
      treeLoading: false,
      treeData: [],
      showExpand: true,
      teamOptions: [],
      value: '',
      offset: 0,
      itemWidth: 275 + 12, // item width + margin
      taskList: [],
      form: {
        Nesting_Result_Name: '',
        Working_Team_Id: '',
        Code_Like: '',
        Task_Code: '',
        Sys_Project_Id: '',
        Area_Id: '',
        InstallUnit_Id: '',
        Production_Status: '',
        Spec_Like: ''
      },
      tbLoading: false,
      showNestPart: false,
      columns: [],
      tbData: [],
      tbConfig: {},
      queryInfo: {
        Page: 1,
        PageSize: tablePageSize[0]
      },
      total: 0,
      tablePageSize: tablePageSize,
      dialogVisible: false,
      currentComponent: '',
      dialogTitle: '',
      width: '80%',
      IsShowBtn: false,
      multipleSelection: [],
      pgLoading: false,
      isNest: false,
      disableLeftBtn: true,
      disableRightBtn: false
    }
  },
  computed: {
    isCom() {
      return this.pageType === 'com'
    },
    statusKey() {
      return this.pageType === 'com'
        ? 'Comp_Produce_Status'
        : 'Part_Produce_Status'
    },
    filterText() {
      return this.projectName + SPLIT_SYMBOL + this.statusType
    },
    expandAll() {
      return process.env.NODE_ENV !== 'development'
    },
    ...mapGetters('factoryInfo', [
      'Is_Skip_Warehousing_Operation',
      'Nested_Must_Before_Processing',
      'Is_Part_Prepare'
    ])
  },

  async mounted() {
    this.pgLoading = true
    window.addEventListener('resize', this.checkResize)
    this.checkResize() // initial check
    this.getTableConfig(
      this.isCom ? 'PROProductionComNewConfig' : 'PROProductionPartNewConfig'
    )
    this.getFactoryInfo()
    await this.getProcessTeam()
    await this.fetchTreeData()
    // await this.getTaskList()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkResize)
  },
  methods: {
    getData(row) {
      this.$nextTick((_) => {
        this.$refs['process'].handleOpen({
          Code: row.Task_Code,
          Type: this.isCom ? '0' : '1',
          Task_Id: row.Task_Id,
          ProcessName: row.Working_Process_Name,
          TeamName: row.Working_Team_Name,
          Schduling_Code: row?.Task_Code.split('-')[0],
          TeamId: this.form.Working_Team_Id
        })
      })
    },
    async getFactoryInfo() {
      await this.$store.dispatch('factoryInfo/getWorkshop')
      console.log(this.Is_Part_Prepare, '====')
    },
    async fetchTreeData() {
      console.log('fetchTreeData')
      this.treeLoading = true
      await GetProjectAreaTreeList({
        MenuId: this.$route.meta.Id,
        projectName: this.projectName,
        type: this.isCom ? 3 : 4
      }).then((res) => {
        if (res.Data.length === 0) {
          this.treeLoading = false
          return
        }
        const resData = res.Data.map((item) => {
          item.Is_Directory = true

          return item
        })
        this.treeData = resData
        // this.expandedKey = resData[0]?.Children[0]?.Id
        // this.form.Area_Id = this.expandedKey
        // this.form.Sys_Project_Id = resData[0].Data.Sys_Project_Id
        this.$nextTick((_) => {
          this.$refs.tree.filterRef(this.filterText)
          const result = this.setKey()
          if (!result) {
            this.pgLoading = false
          }
        })
        this.treeLoading = false
      })
    },
    setKey() {
      const deepFilter = (tree) => {
        for (let i = 0; i < tree.length; i++) {
          const item = tree[i]
          const { Data, Children } = item
          const node = getNode(Data.Id)
          if (Data.ParentId && !Children?.length && node.visible) {
            this.handleNodeClick(item)
            return true
          } else {
            if (Children?.length) {
              const shouldStop = deepFilter(Children)
              if (shouldStop) return true
            }
          }
        }
        return false
      }
      const getNode = (key) => {
        return this.$refs['tree'].getNodeByKey(key)
      }
      return deepFilter(this.treeData)
    },
    handleNodeClick(data) {
      this.expandedKey = data.Id
      this.currentNodeData = data
      this.areaId = data.Id
      this.form.Area_Id = data.Id
      this.form.Sys_Project_Id = data.Data.Sys_Project_Id
      this.multipleSelection = []
      this.getTaskList()
    },

    saveSortFinish() {
      this.$refs.tree.filterRef(this.filterText)
    },

    customFilterFun(value, data, node) {
      const arr = value.split(SPLIT_SYMBOL)
      const labelVal = arr[0]
      const statusVal = arr[1]
      if (!value) return true
      let parentNode = node.parent
      let labels = [node.label]
      let status = [data.Data[this.statusKey]]
      let level = 1
      while (level < node.level) {
        labels = [...labels, parentNode.label]
        status = [...status, data.Data[this.statusKey]]
        parentNode = parentNode.parent
        level++
      }
      labels = labels.filter((v) => !!v)
      status = status.filter((v) => !!v)
      let resultLabel = true
      let resultStatus = true
      if (this.statusType) {
        resultStatus = status.some((s) => s.indexOf(statusVal) !== -1)
      }
      if (this.projectName) {
        resultLabel = labels.some((s) => s.indexOf(labelVal) !== -1)
      }
      return resultLabel && resultStatus
    },

    // 获取班组
    async getProcessTeam() {
      await GetTeamListByUser({
        type: this.isCom ? 1 : 2 // 0:全部，工艺类型1：构件工艺，2：零件工艺
      }).then((res) => {
        this.teamOptions = res.Data.map((item) => {
          return {
            value: item.Id,
            label: item.Name
          }
        })
      })
      this.form.Working_Team_Id = this.teamOptions[0]?.value || ''
    },
    handleTeamChange(value) {
      this.form.Working_Team_Id = value
      this.getTaskList()
    },
    // 获取任务单列表
    async getTaskList() {
      this.form.Task_Code = ''
      this.pgLoading = true
      const requestFn = this.isCom
        ? GetCompTaskList
        : GetSimplifiedPartTaskList

      try {
        const res = await requestFn(this.form)
        if (res.IsSucceed) {
          this.taskList = (res.Data || []).map((v) => {
            v.Demand_Date = timeFormat(v.Demand_Date)
            v.percentage = 0
            return v
          })
          this.isNest = this.taskList.some((v) => !!v.Is_Nest)
          this.form.Task_Code = this.taskList[0]?.Task_Code || ''
          if (this.taskList.length > 0) {
            const cur = this.taskList[0]
            if (this.checkShowP(cur.Need_Part_Amount)) {
              this.getPercentDetail()
            }
          }
        } else {
          this.taskList = []
          this.$message.error(res.Message)
        }

        if (this.taskList.length > 0) {
          await this.fetchData(1)
        } else {
          this.tbData = []
        }
      } catch (error) {
      } finally {
        this.checkResize() // initial check
        this.pgLoading = false
      }
    },
    handleTask(item) {
      this.form.Task_Code = item.Task_Code
      this.scrollToActiveItem(item)
      this.multipleSelection = []
      this.form.Code_Like = ''
      this.form.Production_Status = ''
      this.form.Spec_Like = ''
      this.isNest = item.Is_Nest
      this.fetchData(1)
    },
    scrollToActiveItem(item) {
      this.$nextTick(() => {
        const itemElement = this.$refs[`task_${item.Task_Code}`][0]
        console.log(itemElement)
        if (itemElement) {
          itemElement.scrollIntoView({ behavior: 'smooth', inline: 'center' })
        }
      })
    },
    refresh() {
      this.multipleSelection = []
      this.fetchData(1)
    },
    async fetchData(page) {
      page && (this.queryInfo.Page = page)
      const form = { ...this.form, ...this.queryInfo }
      if (form.Working_Team_Id === 'all') {
        const ids = this.teamOptions
          .map((v) => v.value)
          .filter((s) => s !== 'all')
          .toString()
        form.Working_Team_Id = ids
      }
      if (!this.isCom) {
        form.Show_Nest_Part = this.showNestPart
      }
      this.tbLoading = true
      const requestFn = this.isCom
        ? GetCompTaskPageList
        : GetSimplifiedPartTaskPageList
      await requestFn(form)
        .then((res) => {
          if (res.IsSucceed) {
            this.tbData = (res?.Data?.Data || []).map((v) => {
              v.checked = false
              return v
            })
            this.total = res.Data.TotalCount
            this.getStopList(this.tbData)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
          this.tbLoading = false
        })
        .finally((e) => {
          this.tbLoading = false
        })
    },
    async getStopList(list) {
      const key = 'Id'
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Type: this.isCom ? 2 : 1 // 1：零件，3：部件，2：构件
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },

    // 查看图纸
    handleDwg(row) {
      GetDwg({
        Task_Id: row.Task_Id
      }).then((res) => {
        if (res.IsSucceed) {
          const fileurl = res?.Data?.length && res.Data[0].File_Url
          window.open(
            'http://dwgv1.bimtk.com:5432/?CadUrl=' + parseOssUrl(fileurl),
            '_blank'
          )
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // 查看齐套弹框
    handleQitao(row) {
      this.dialogTitle = '零件齐套'
      this.currentComponent = 'Qitao'
      this.tid = row.Task_Id
      this.compCode = row.Comp_Code
      this.$nextTick((_) => {
        this.dialogVisible = true
      })
    },
    // 查看零件
    viewPart(item) {
      this.dialogTitle = '零件明细表'
      this.currentComponent = 'PartDetail'
      this.dialogVisible = true
      const obj = {
        Task_Code: item.Task_Code,
        Working_Team_Id: this.form.Working_Team_Id,
        Sys_Project_Id: item.Sys_Project_Id,
        Need_Use_Part: item.Need_Use_Part
      }
      this.$nextTick((_) => {
        this.$refs['content'].init(obj)
      })
    },
    checCheckboxkMethod3({ row }) {
      if (this.isCom) {
        if (typeof row.Prepare_Count === 'number' && this.Is_Part_Prepare) {
          return row.Ready_Process_Count > 0 && row.Prepare_Count > 0
        } else {
          return row.Ready_Process_Count > 0
        }
      } else {
        return row.Ready_Process_Count > 0
      }
    },
    multiSelectedChange(array) {
      this.multipleSelection = array.records
    },

    // 任务单左右移动方法
    clickMove(direction) {
      const middleWapperWidth = this.$refs.middleWapper?.offsetWidth
      const boxWapperWidth = this.$refs.boxWapper?.scrollWidth
      console.log(middleWapperWidth, boxWapperWidth)

      if (middleWapperWidth < boxWapperWidth) {
        if (direction === 'left') {
          this.offset = Math.min(this.offset + this.itemWidth, 0)
        } else if (direction === 'right') {
          const maxOffset = middleWapperWidth - boxWapperWidth
          console.log(maxOffset, this.offset - this.itemWidth)
          this.offset = Math.max(this.offset - this.itemWidth, maxOffset)
        }
      }
      // 更新按钮的禁用状态
      this.disableLeftBtn = this.offset === 0
      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth
      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)
    },
    checkResize() {
      const middleWapperWidth = this.$refs.middleWapper.offsetWidth
      const boxWapperWidth = this.$refs.boxWapper.scrollWidth
      // console.log(middleWapperWidth, boxWapperWidth)

      if (middleWapperWidth >= boxWapperWidth) {
        this.offset = 0
        this.IsShowBtn = false
      } else {
        const maxOffset = middleWapperWidth - boxWapperWidth
        this.offset = Math.max(this.offset, maxOffset)
        this.IsShowBtn = true
      }
      // 更新按钮的禁用状态
      this.disableLeftBtn = this.offset === 0
      this.disableRightBtn = this.offset === middleWapperWidth - boxWapperWidth
      console.log(this.offset, this.disableLeftBtn, this.disableRightBtn)
    },
    showNestChange(val) {
      this.refresh()
    },
    handleReport() {
      if (this.isCom) {
        this.currentComponent = 'ComReport'
        this.dialogTitle = '报工'
        this.$nextTick((_) => {
          this.dialogVisible = true
          this.$nextTick((_) => {
            this.$refs['content'].init(
              JSON.parse(JSON.stringify(this.multipleSelection))
            )
          })
        })
      } else {
        this.dialogTitle = '零件报工'
        this.currentComponent = 'PartReport'
        this.dialogVisible = true
        this.$nextTick((_) => {
          this.$refs['content'].init(
            JSON.parse(JSON.stringify(this.multipleSelection))
          )
        })
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    checkShowP(num) {
      if (typeof num !== 'number') {
        return false
      }
      return true
    },
    getPercentDetail() {
      GetCompTaskPartCompletionStock({
        Working_Team_Id: this.form.Working_Team_Id,
        Task_Code: this.taskList.map((v) => v.Task_Code)
      }).then((res) => {
        if (res.IsSucceed) {
          const list = res.Data
          const taskMap = {}
          this.taskList.forEach((item) => {
            taskMap[item.Task_Code] = item
          })
          list.forEach((item) => {
            const cur = taskMap[item.Task_Code]
            if (cur.Need_Part_Amount === -1) {
              cur.percentage = '100%'
            } else {
              const p = numeral(item.Part_Stock_Count).divide(
                cur.Need_Part_Amount
              )
              let result = '100%'
              if (p.value() <= 1) {
                result = p.format('0.[00]%')
              }
              cur.percentage = result
            }
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "~@/styles/mixin.scss";
// @import "~@/styles/tabs.scss";
* {
  box-sizing: border-box;
}
.app-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;

  .cs-left {
    display: flex;
    flex-direction: column;
    margin-right: 20px;

    .inner-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px 16px;
      border-radius: 4px;
      overflow: hidden;
      .cs-search {
        border-bottom: 1px solid #e2e4e9;
        padding-bottom: 17px;
        .team-select {
          margin-bottom: 12px;
          .el-select {
            width: 100%;
          }

          ::v-deep {
            .el-input__inner {
              border: 1px solid #298dff;
              color: #298dff;
              font-weight: bold;
            }
            .el-input__prefix {
              display: flex;
              justify-content: center;
              align-items: center;

              img {
                width: 16px;
                height: 16px;
              }
            }

            .el-select__caret {
              color: #298dff;
            }
          }
        }
      }
      .cs-tree {
        margin-top: 22px;
        flex: 1;
        height: 0;

        .cs-scroll {
          overflow-y: auto;
          @include scrollBar;
        }

        .el-tree {
          height: 100%;
        }
      }
    }
  }

  .cs-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 16px 16px;

    .cs-top-wapper {
      display: flex;
      justify-content: space-between;
      // align-items: center;
      .btn {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        background: #cfcfcf;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-weight: bold;
        font-size: 12px;
        cursor: pointer;
        margin-top: calc((96px / 2) - 10px);
      }
      .btn.disabled {
        opacity: 0.5;
        // pointer-events: none;
        cursor: not-allowed !important;
      }

      .middle-wapper::-webkit-scrollbar {
        height: 0; /* 将滚动条的宽度设为0 */
        background: transparent; /* 使滚动条完全透明 */
      }

      .middle-wapper {
        flex: 1;
        width: 0;
        overflow-x: auto;
        position: relative;
        height: calc(96px + 10px);

        .box-wapper {
          position: absolute;
          display: flex;
          flex-wrap: no-wrap;
          height: 100%;
          transition: transform 0.3s ease;

          .item {
            width: 275px;
            height: calc(100% - 10px);
            border-radius: 4px 4px 4px 4px;
            border: 1px solid #e2e4e9;
            margin-left: 12px;
            position: relative;
            cursor: pointer;
            .content {
              width: 100%;
              height: 100%;
              display: flex;
              padding: 12px 12px;
              flex-direction: column;
              // align-items: center;
              // justify-content: space-between;
              // background: linear-gradient( 91deg, #298DFF 0%, #57C2FF 100%);
              .detail {
                flex: 1;
                height: 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
              }
              .title {
                margin-bottom: 8px;
                display: flex;
                align-items: center;
              }
              .name {
                font-weight: bold;
                font-size: 16px;
              }
              .precent {
                height: 17px;
                padding: 0 4px;
                background: #00cfaa;
                border-radius: 9px 9px 9px 9px;
                color: #ffffff;
                font-size: 12px;
                margin-left: 8px;
                line-height: 17px;
                text-align: center;
              }
              .info {
                font-size: 14px;
                color: #999999;
                margin-bottom: 2px;
              }

              .right {
                // width: 80px;
                padding: 5px 8px;
                border-radius: 4px 4px 4px 4px;
                text-align: center;

                font-size: 14px;
                cursor: pointer;
                background: #e9f3ff;
                color: #298dff;
              }
            }

            .flag {
              position: absolute;
              top: 0;
              right: 0;
              width: 35px;
              height: 35px;
              background: url("~@/assets/PRO/flag.png") no-repeat;
              font-size: 12px;
              color: #ffffff;
              display: flex;
              justify-content: flex-end;
              align-items: flex-start;
              padding-right: 4px;
              padding-top: 6px;
            }
            i {
              position: absolute;
              bottom: -10px;
              left: calc((100% - 16px) / 2);
              color: #298dff;
            }
            .flag2 {
              position: absolute;
              bottom: 0;
              right: 0;
              width: 0;
              height: 0;
              border-left: 35px solid transparent;
              border-top: 35px solid transparent;
              border-bottom: 35px solid #e6a23c;
              font-size: 12px;
              color: #ffffff;
              .flag2-txt {
                position: absolute;
                right: 3px;
                bottom: -33px;
              }
            }
          }
          .active {
            .content {
              color: #ffffff;
              background: linear-gradient(91deg, #298dff 0%, #57c2ff 100%);
              .info {
                color: #ffffff;
              }
            }
            .right {
              background: #ffffff;
            }
          }
          .item:last-child {
            margin-right: 12px;
          }
        }
      }
    }

    .cs-middle-wapper {
      display: flex;
      justify-content: space-between;
      margin: 12px 0;
      align-items: center;
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .cs-bottom-wapper {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;
      .tb-x {
        flex: 1;
        height: 0;
      }

      .pagination-container {
        text-align: right;
        padding: 16px;
        margin: 0;
      }

      .data-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}

.plm-custom-dialog {
  ::v-deep {
    .el-dialog__body {
      height: 70vh;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
  }
}

.fourGreen {
  color: #00c361;
  font-style: normal;
}

.fourOrange {
  color: #ff9400;
  font-style: normal;
}

.fourRed {
  color: #ff0000;
  font-style: normal;
}

.cs-blue {
  color: #5ac8fa;
}
.cs-checkbox {
  margin-left: 16px;
  line-height: 30px;
}
.cs-middle-wapper-left{
  display: flex;
  flex-wrap: nowrap;
}
</style>

<template>
  <div class="dialog_wapper">
    <el-dialog
      v-if="shawDialog"
      ref="content"
      :title="title"
      :visible.sync="shawDialog"
      width="60%"
      class="plm-custom-dialog"
      style="margin-top: -5vh"
      @close="handleClose"
    >
      <div class="select_Wapper">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label="整改单号" prop="Code">
            <el-input :value="form.Code" type="text" disabled />
          </el-form-item>
          <el-form-item label="质检单号" prop="Number">
            <el-input :value="form.Number" type="text" disabled />
          </el-form-item>
          <el-form-item label="质检对象" prop="Check_Object_Type">
            <el-input :value="form.Check_Object_Type" type="text" disabled />
          </el-form-item>
          <el-form-item label="质检类型" prop="Check_Type">
            <el-input :value="form.Check_Type" type="text" disabled />
          </el-form-item>
          <el-form-item label="总不合格数" prop="AllUnqualified_Count">
            <el-input :value="form.AllUnqualified_Count" type="text" disabled />
          </el-form-item>
          <el-form-item label="整改人" prop="Rectifier_name">
            <el-input :value="form.Rectifier_name" type="text" disabled />
          </el-form-item>
          <el-form-item label="整改时限" prop="Rectify_Date">
            <el-input :value="form.Rectify_Date" type="text" disabled />
          </el-form-item>
          <el-form-item label="整改状态" prop="Status">
            <el-input :value="form.Status" type="text" disabled />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="!isCheck" style="margin: 20px 0">
        <el-button
          type="primary"
          :disabled="!form.Check_Object_Type"
          @click="addInfo"
        >添 加</el-button>
        <el-button
          type="danger"
          :disabled="selectList.length == 0"
          @click="handeldelete"
        >删 除
        </el-button>
      </div>
      <div v-loading="loading" class="fff cs-z-tb-wrapper">
        <DynamicDataTable
          ref="table"
          :config="tbConfig"
          :columns="columns"
          :data="tbData"
          :total="pageInfo.TotalCount"
          :page="pageInfo.Page"
          stripe
          class="cs-plm-dy-table"
          border
          @gridPageChange="gridPageChange"
          @gridSizeChange="gridSizeChange"
          @multiSelectedChange="multiSelectedChange"
        >
          <template #Code="{ row }">
            <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
            {{ row.Code | displayValue }}
          </template>
          <template #SteelName="{ row }">
            <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
            {{ row.SteelName | displayValue }}
          </template>
        </DynamicDataTable>
      </div>
      <div v-if="rectificationState !== -1" class="rectification-content">
        <el-form
          ref="form2"
          :model="form2"
          :rules="rules2"
          label-width="100px"
          class="demo-form2"
        >
          <el-form-item
            v-if="rectificationState === 2"
            label="复核状态"
            prop="IsPass"
          >
            <el-select v-model="form2.IsPass" placeholder="请选择">
              <el-option label="合格" :value="true" />
              <el-option label="不合格" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="rectificationState === 1 ? '整改内容' : '复核内容'"
            prop="Suggestion"
          >
            <el-input
              v-model="form2.Suggestion"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              style="width: 70%"
            />
          </el-form-item>
          <el-form-item label="上传附件" prop="fileList2">
            <OSSUpload
              class="upload-demo"
              drag
              action="alioss"
              accept="image/*"
              :file-list="fileList2"
              multiple
              :limit="5"
              :on-success="
                (response, file, fileList2) => {
                  uploadSuccess(response, file, fileList2);
                }
              "
              :on-remove="uploadRemove"
              :on-preview="handlePreview2"
              :on-exceed="uploadExceed"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">文件上传数量最多为5个</div>
            </OSSUpload>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="rectificationState !== -1" class="dialog-footer">
        <el-button @click="handleClose()">取 消</el-button>
        <el-button
          type="primary"
          :loading="btnLoading"
          @click="submit()"
        >提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetGridByCode } from '@/api/sys'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  GetDictionaryDetailListByCode,
  GetNodeList
} from '@/api/PRO/factorycheck'
import { AddLanch, GetEditById } from '@/api/PRO/qualityInspect/start-Inspect'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import {
  BatchManageSaveCheck,
  SaveTesting,
  GetCheckingEntity,
  SaveFeedBack
} from '@/api/PRO/qualityInspect/quality-management'
import OSSUpload from '@/views/plm/components/ossupload'
import { parseTime } from '@/utils'
import { GetStopList } from '@/api/PRO/production-task'

export default {
  components: {
    DynamicDataTable,
    OSSUpload
  },
  directives: { elDragDialog },
  data() {
    return {
      Check_Style: 1, // 1全检0抽检
      btnLoading: false,
      isCheck: false, // 是否质检
      isSee: false, // 是否查看
      chooseTitle: '',
      Code: '',
      loading: false,
      tbConfig: {
        Is_Select: true
      },
      columns: [],
      tbData: [],
      pageInfo: {
        Page: 1,
        TotalCount: 0,
        PageSize: 10
      },
      selectList: [],
      gridCode: 'pro2_bitch_steel_list',
      form: {
        Check_Object_Type: '', // 质检对象
        Check_Node_Id: '', // 质检节点
        Check_Type: null, // 质检类型
        Sheet_Result: '',
        Code: '',
        Number: '',
        AllUnqualified_Count: 0,
        Rectifier_name: '',
        Rectify_Date: '',
        Status: ''
      },
      CheckTypeList: [
        {
          Name: '质量',
          Id: 1
        },
        {
          Name: '探伤',
          Id: 2
        }
      ], // 质检类型
      CheckNodeList: [], // 质检节点
      CheckObjectData: [], // 质检对象
      shawDialog: false,
      title: '新建质检单',
      disable: false, // 编辑禁用
      plm_Factory_Sheets: [], // 质检和构件信息
      isSee: false,
      rectificationState: 1, // 1整改 2复核 -1查看
      form2: {
        Sheet_Id: '',
        IsPass: '',
        Suggestion: '',
        fileList2: []
      },
      rules2: {
        IsPass: [{ required: true, message: '请复核', trigger: 'change' }]
        // Suggestion: [{ required: true, message: "请填写", trigger: "blur" }],
      },
      fileList: [],
      fileList2: [],
      Attachments: []
    }
  },
  mounted() {
    console.log('11111')
    this.getCheckType()
  },
  methods: {
    // 从所有构零件里拿质检的构零件
    handelInfo(val, Check_Style) {
      console.log(val, 'val')
      console.log(Check_Style, 'Check_Style')
      val.forEach((item) => {
        this.tbData.push(item)
      })
      this.getCount()
      this.Check_Style = Check_Style // 判断是抽检还是全检
    },
    handleClose() {
      this.shawDialog = false
      this.tbData = []
      this.form = {
        Check_Object_Type: '', // 质检对象
        Check_Node_Id: '', // 质检节点
        Check_Type: null, // 质检类型
        Sheet_Result: '',
        Code: '',
        Number: '',
        AllUnqualified_Count: 0,
        Rectifier_name: '',
        Rectify_Date: '',
        Status: ''
      }
      this.form2 = {
        Sheet_Id: '',
        IsPass: '',
        Suggestion: '',
        fileList2: []
      }
      this.disable = false
    },
    init(code, row, isCheck, isSee, rectificationState) {
      this.detail = row
      console.log('code', code)
      console.log('rowsss', row)
      this.isCheck = isCheck
      this.isSee = isSee
      console.log('isSee', isSee)
      this.form2.Sheet_Id = row.SheetId
      this.rectificationState = rectificationState

      console.log(this.isCheck, 'isCheck')
      if (row) {
        console.log(row)
        this.disable = true
        this.title = '整改单'
        if (row.Check_Object_Type == '构件') {
          this.gridCode = 'pro2_bitch_steel_list'
          this.check_object_id = '0'
        } else if (row.Check_Object_Type == '零件') {
          this.gridCode = 'pro2_bitch_part_list'
          this.check_object_id = '1'
        } else if (row.Check_Object_Type == '部件') {
          this.gridCode = 'pro2_bitch_part_list'
          this.check_object_id = '3'
        }
        // this.EditInfo(row);
        // this.getCheckType();
        this.getCheckingEntity(row)
      } else {
        // this.title = "整改单";
      }
      this.Code = code
      // console.log(code);
      this.shawDialog = true
      this.getGridByCode()
    },
    getCheckingEntity(row) {
      GetCheckingEntity({ sheetId: row.SheetId }).then((res) => {
        if (res.IsSucceed) {
          this.plm_Factory_Sheets = res.Data[0]
          this.form.Check_Object_Type =
            this.plm_Factory_Sheets.Check_Object_Type
          this.form.Check_Type = this.plm_Factory_Sheets.Check_Type
          this.form.Sheet_Result = this.plm_Factory_Sheets.Check_Result
          // this.changeObject(this.form.Check_Object_Type); //通过质检对象类型找质检节点Id
          this.form.Check_Node_Id = this.plm_Factory_Sheets.Check_Node_Id
          this.form.SheetId = this.plm_Factory_Sheets.SheetId
          this.form.Code = this.plm_Factory_Sheets.Code
          this.form.Number = this.plm_Factory_Sheets.Number
          this.form.Rectifier_name = this.plm_Factory_Sheets.Rectifier_name
          this.form.Rectify_Date = this.plm_Factory_Sheets.Rectify_Date ? parseTime(
            new Date(this.plm_Factory_Sheets.Rectify_Date),
            '{y}-{m}-{d}'
          ) : ''
          this.form.Status = this.plm_Factory_Sheets.Status
          // if (this.plm_Factory_Sheets.Check_Type === "质量") {
          //   this.form.Check_Type = 1;
          // } else {
          //   this.form.Check_Type = 2;
          // }
          // this.form.Check_Type = this.plm_Factory_Sheets.Check_Type;
          if (row.Check_Object_Type === '构件') {
            this.tbData = res.Data
          } else if (row.Check_Object_Type === '零件') {
            this.tbData = res.Data.map((item) => {
              item.Project_Name = item.ProjectName
              item.Area_Name = item.AreaPosition
              item.InstallUnit_Name = item.SetupPosition
              item.Code = item.SteelName
              item.Spec = item.SteelSpec
              item.Length = item.SteelLength
              item.Num = item.SteelAmount
              return item
            })
          } else if (row.Check_Object_Type === '部件') {
            this.tbData = res.Data.map((item) => {
              item.Project_Name = item.ProjectName
              item.Area_Name = item.AreaPosition
              item.InstallUnit_Name = item.SetupPosition
              item.Code = item.SteelName
              item.Spec = item.SteelSpec
              item.Length = item.SteelLength
              item.Num = item.SteelAmount
              return item
            })
          }
          this.tbData = this.tbData.filter((item) => {
            return item.Unqualified_Count
          })
          this.form.AllUnqualified_Count = 0
          this.tbData.forEach((item) => {
            this.form.AllUnqualified_Count += item.Unqualified_Count
          })
          this.getStopList()
          // this.getCount();
          // res.Data.forEach()
          // this.tbData = res.Data;
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    async getStopList() {
      const submitObj = this.tbData.map(item => {
        return {
          Id: item.Id,
          Type: item.Check_Object_Type === '构件' ? 2 : item.Check_Object_Type === '部件' ? 3 : 1 // 1：零件，3：部件，2：构件

        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          this.tbData.forEach(row => {
            if (stopMap[row.Id]) {
              this.$set(row, 'stopFlag', stopMap[row.Id])
            }
          })
        }
      })
    },
    uploadSuccess(response, file, fileList) {
      console.log('on-success')
      console.log(response, 'response')
      console.log(file, 'file')
      console.log(fileList, 'fileList')
      // this.fileList = fileList;
      console.log(this.fileList, 'this.fileList')
      console.log(this.fileList2, 'this.fileList2')
      console.log(this.form.fileList2, 'this.form.fileList2')
      this.Attachments = []
      fileList.map((item) => {
        const imgObj = { File_Name: '', File_Url: '' }
        imgObj.File_Name = item.name
        if (item.hasOwnProperty('response')) {
          // imgObj.File_Url = item.response.Data.split("*")[0];
          imgObj.File_Url = item.response.encryptionUrl
        } else {
          imgObj.File_Url = item.url
        }
        this.Attachments.push(imgObj)
      })
      console.log(this.Attachments, 'this.Attachments')
    },
    uploadExceed(files, fileList) {
      console.log(this.fileList, '已超过')
      this.$message({
        type: 'warning',
        message: '已超过文件上传最大数量'
      })
    },
    uploadRemove(file, fileList) {
      console.log('on-remove')
      console.log(file, 'file')
      console.log(fileList, 'fileList')
      // this.fileList = fileList;
      console.log(this.fileList, 'this.fileList')
      this.Attachments = []
      fileList.map((item) => {
        const imgObj = { File_Name: '', File_Url: '' }
        imgObj.File_Name = item.name
        if (item.hasOwnProperty('response')) {
          // imgObj.File_Url = item.response.Data.split("*")[0];
          imgObj.File_Url = item.response.encryptionUrl
        } else {
          imgObj.File_Url = item.url
        }
        this.Attachments.push(imgObj)
      })
      console.log(this.Attachments, 'this.Attachments')
    },
    handlePreview2(file) {
      console.log('on-preview')
      console.log(file, 'file')
      let fielUrl = null
      if (file.hasOwnProperty('response')) {
        // fielUrl = file.response.Data.split("*")[0];
        fielUrl = file.response.encryptionUrl
      } else {
        fielUrl = file.url
      }
      console.log(fielUrl, 'fielUrl')
      window.open(fielUrl, '_blank')
    },
    getNode(type) {
      this.CheckObjectData.find((v) => {
        return v.Display_Name == type
      })
    },
    getCheckType() {
      GetDictionaryDetailListByCode({ dictionaryCode: 'Quality_Code' })
        .then((res) => {
          if (res.IsSucceed) {
            this.CheckObjectData = res.Data
          } else {
            this.$message({
              type: 'error',
              message: res.Message
            })
          }
        })
        .catch(() => {
          console.log('sdfd')
        })
    },
    changeObject(val) {
      console.log(val, 'val')
      this.tbData = []
      const checkObj = this.CheckObjectData.find((v) => {
        return v.Id == val
      })?.Display_Name
      this.chooseTitle = checkObj
      switch (checkObj) {
        case '构件':
          this.check_object_id = '0'
          this.gridCode = 'pro2_bitch_steel_list'
          break
        case '零件':
          this.check_object_id = '1'
          this.gridCode = 'pro2_bitch_part_list'
          break
        case '物料':
          this.check_object_id = '2'
          break
        case '部件':
          this.check_object_id = '3'
          this.gridCode = 'pro2_bitch_part_list'
          break
        default:
          this.check_object_id = '0'
      }
      this.getGridByCode()
      this.getNodeList(val)
      this.qualityItem = this.CheckObjectData.find((item) => {
        return item.Id == val
      })
      console.log(this.qualityItem, 'this.qualityItem111111111')
      this.$emit('qualityItemChange', this.qualityItem)
    },
    getNodeList(val) {
      GetNodeList({ check_object_id: val }).then((res) => {
        if (res.IsSucceed) {
          this.$nextTick(() => {
            this.CheckNodeList = res.Data
          })
        } else {
          this.$message({
            type: 'error',
            message: 'res.Message'
          })
        }
      })
    },
    changeCheckNode(val) {
      this.CheckTypeList = []
      const checkTypeId = this.CheckNodeList.find((v) => {
        return v.Id === val
      }).Check_Type
      console.log(checkTypeId)
      if (checkTypeId == '-1') {
        this.CheckTypeList = [
          {
            Name: '质量',
            Id: '1'
          },
          {
            Name: '探伤',
            Id: '2'
          }
        ]
      } else if (checkTypeId == '1') {
        this.CheckTypeList = [
          {
            Name: '质量',
            Id: '1'
          }
        ]
      } else if (checkTypeId == '2') {
        this.CheckTypeList = [
          {
            Name: '探伤',
            Id: '2'
          }
        ]
      }
      console.log(this.form.Check_Type)
    },
    getGridByCode() {
      this.loading = true
      GetGridByCode({ Code: this.gridCode + ',' + this.Code })
        .then((res) => {
          if (res.IsSucceed) {
            this.setGrid(res.Data.Grid)
            this.setCols(res.Data.ColumnList)
          }
        })
        .then(() => {
          this.loading = false
        })
    },
    setGrid(grid) {
      const isSelectObj = {
        Is_Select: false
      }
      // if (this.rectificationState === -1) {
      //   isSelectObj.Is_Select = false
      // }

      this.tbConfig = Object.assign({}, grid, isSelectObj)

      this.pageInfo.PageSize = parseInt(this.tbConfig.Row_Number)
      this.$nextTick(() => {
        console.log('thiss.tbData', this.tbConfig)
      })
    },
    setCols(cols) {
      if (this.check_object_id === '3') {
        cols.map((item) => {
          if (item.Code === 'Code') {
            item.Display_Name = '部件名称'
          }
          return item
        })
      }
      this.columns = cols
    },

    setGridData(data) {
      this.tbData = data.Data
      this.pageInfo.TotalCount = data.TotalCount
      this.TotalAmount = data.TotalAmount
      this.TotalWeight = data.TotalWeight
    },

    gridPageChange({ page }) {
      this.pageInfo.Page = Number(page)
      this.fetchData()
    },
    gridSizeChange({ size }) {
      this.tbConfig.Row_Number = Number(size)
      this.pageInfo.PageSize = Number(size)
      this.pageInfo.Page = 1
      this.fetchData()
    },

    multiSelectedChange(item) {
      this.selectList = item
      console.log(item)
    },
    addInfo() {
      console.log(111)
      const selectId = []
      this.tbData.forEach((item) => {
        selectId.push(item.Id)
      })

      this.$emit(
        'BitchopenDialog',
        this.check_object_id,
        this.chooseTitle,
        selectId
      )
    },
    handeldelete() {
      this.selectList.forEach((item) => {
        const repeat = this.tbData.find((v) => {
          return v.Id === item.Id
        })
        const index = this.tbData.indexOf(repeat)
        this.tbData.splice(index, 1)
      })
    },
    submit() {
      const hasStopFlag = this.tbData.some((item) => {
        return item.stopFlag
      })
      if (hasStopFlag) {
        this.$message({
          message: '存在停用数据，请先处理',
          type: 'error'
        })
        return
      }
      const { Sheet_Id, IsPass, Suggestion } = this.form2
      const submitObj = {
        plm_Sheetreply: {
          Sheet_Id: Sheet_Id || '',
          IsPass: IsPass,
          Suggestion: Suggestion || ''
        },
        _Attachments: this.Attachments
      }
      if (this.rectificationState !== 2) {
        delete submitObj.plm_Sheetreply['IsPass']
      }
      console.log(submitObj, 'submitObj')
      this.$refs.form2.validate((valid) => {
        if (valid) {
          this.btnLoading = true
          SaveFeedBack(submitObj).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '提交成功',
                type: 'success'
              })
              this.$emit('refresh')
              this.handleClose()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
            this.btnLoading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog_wapper {
  // min-height: 500px;
  .cs-z-tb-wrapper {
    height: 400px;
    // border-bottom: 1px solid #eee;
  }
}
.select_Wapper {
  border-bottom: 1px solid #eee;
  ::v-deep {
    .el-form-item {
      display: inline-block;
      .el-form-item__content {
        .el-select {
          width: 220px;
        }
      }
    }
  }
}
// .input_Wapper {
//   padding-top: 10px;
//   ::v-deep {
//     .el-form-item {
//       display: inline-block;
//       .el-form-item__content {
//         .el-input {
//           width: 100px;
//         }
//       }
//     }
//   }
// }
.dialog-footer {
  text-align: right;
  position: relative;
  bottom: -15px;
  right: 35px;
}
.rectification-content {
  padding-top: 18px;
}
</style>

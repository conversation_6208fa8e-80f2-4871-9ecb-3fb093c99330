<template>
  <div class="abs100">
    <div class="app-container h100">
      <div class="top-btn" @click="toBack"><el-button>返回</el-button></div>
      <div class="h100 wrapper-c">
        <div class="wrapper-top">
          <div class="top-title">
            <span>项目名称：{{ form.Short_Name || "—" }}</span>
            <span>质检单号：{{ form.Number || "—" }}</span>
          </div>
        </div>
        <div class="wrapper-main">
          <div class="basic-information">
            <header>构件信息</header>
            <el-form
              ref="form"
              :inline="true"
              :model="form"
              class="demo-form-inline"
              :rules="rules"
              style="padding-left: 20px"
              label-width="80px"
            >
              <el-form-item label="名称" prop="SteelName">
                <el-input v-model="form.SteelName" type="text" disabled />
              </el-form-item>
              <el-form-item label="质检对象" prop="Check_Object_Type">
                <el-input
                  :value="
                    form.Check_Object_Type == 0
                      ? '构件'
                      : form.Check_Object_Type == 1
                        ? '零件'
                        : '部件'
                  "
                  type="text"
                  disabled
                />
              </el-form-item>
              <el-form-item label="质检节点" prop="Check_Node_Name">
                <el-input v-model="form.Check_Node_Name" type="text" disabled />
              </el-form-item>
              <el-form-item label="质检类型" prop="Check_Type">
                <el-input
                  :value="form.Check_Type == 1 ? '质量' : '探伤'"
                  type="text"
                  disabled
                />
              </el-form-item>
              <el-form-item label="质检结果" prop="Sheet_Result">
                <el-input
                  v-if="CheckFeeback.length !== 0 || isSee"
                  :value="form.Sheet_Result"
                  type="text"
                  disabled
                />
                <el-select
                  v-else
                  v-model="form.Sheet_Result"
                  placeholder="请选择"
                  clearable
                >
                  <el-option label="合格" value="合格" />
                  <el-option label="不合格" value="不合格" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="form.Sheet_Result !== '合格'"
                label="整改时限"
                prop="Rectify_Date"
              >
                <el-date-picker
                  v-if="!isSee"
                  v-model="form.Rectify_Date"
                  align="right"
                  type="date"
                  placeholder="选择日期"
                />
                <el-input
                  v-else
                  :value="form.Rectify_Date"
                  type="text"
                  disabled
                />
              </el-form-item>
              <el-form-item
                v-if="form.Sheet_Result !== '合格'"
                label="整改人"
                prop="Rectifier_Id"
              >
                <el-select
                  v-if="!isSee"
                  v-model="form.Rectifier_Id"
                  placeholder="请选择"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.Id"
                    :value="item.Id"
                    :label="item.Display_Name"
                  />
                </el-select>
                <el-input
                  v-else
                  :value="form.Rectifier_Name"
                  type="text"
                  disabled
                />
              </el-form-item>
              <el-form-item
                v-if="form.Sheet_Result !== '合格'"
                label="参与人"
                prop="Participant_Id"
              >
                <el-select
                  v-if="!isSee"
                  v-model="form.Participant_Id"
                  placeholder="请选择"
                  clearable
                  filterable
                  @change="changeCategory"
                >
                  <el-option
                    v-for="item in userList"
                    :key="item.Id"
                    :value="item.Id"
                    :label="item.Display_Name"
                  />
                </el-select>
                <el-input
                  v-else
                  :value="form.Participant_Name"
                  type="text"
                  disabled
                />
              </el-form-item>
            </el-form>
          </div>
          <div class="inspection-type">
            <header>检查类型</header>
            <div v-if="CheckFeeback.length !== 0" class="radio-items">
              <div
                v-for="item of CheckFeeback"
                :key="item.CheckId"
                class="radio-item"
              >
                <span>{{ item.CheckName }}</span>
                <!-- <el-radio v-model="item.isPass" :label="true">合格</el-radio>
                <el-radio v-model="item.isPass" :label="false">不合格</el-radio> -->
                <el-radio-group
                  v-model="item.isPass"
                  :disabled="isSee"
                  @change="changePass"
                >
                  <el-radio :label="true">合格</el-radio>
                  <el-radio :label="false">不合格</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-else class="no-radio-items">暂无检查类型</div>
          </div>
          <div class="detailed-drawings">
            <header>深化图纸</header>
            <div v-if="steelCadList.length !== 0" class="deep-img">
              <div
                v-for="(dwg, index) in steelCadList"
                :key="index"
                class="dwg_ico"
                @click="openDwg(dwg)"
              />
            </div>
            <div v-else class="font-cad">无cad</div>
          </div>
          <div class="inspection-description">
            <header>质检描述</header>
            <el-input
              v-model="form.Suggestion"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              style="width: 50%"
              :disabled="isSee"
            />
          </div>
          <div v-if="form.Sheet_Result !== '合格'" class="rectification">
            <header>整改问题</header>
            <el-input
              v-model="form.Rectify_Description"
              type="textarea"
              :rows="4"
              placeholder="请输入内容"
              style="width: 50%"
              :disabled="isSee"
            />
          </div>
          <div class="img-up">
            <header>图片上传</header>
            <OSSUpload
              v-if="!isSee"
              class="upload-demo"
              drag
              action="alioss"
              accept="image/*"
              :file-list="fileList"
              multiple
              :limit="5"
              :on-success="
                (response, file, fileList) => {
                  uploadSuccess(response, file, fileList);
                }
              "
              :on-remove="uploadRemove"
              :on-preview="handlePreview"
              :on-exceed="uploadExceed"
              :disabled="isSee"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">文件上传数量最多为5个</div>
            </OSSUpload>
            <div v-else>
              <el-table :data="fileList" border style="width: 100%">
                <el-table-column prop="name" label="文件名" width="300">
                  <template slot-scope="{ row }">
                    <div
                      style="
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                    >
                      {{ row.name || "-" }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="83" align="center">
                  <template slot-scope="{ row }">
                    <el-button
                      type="text"
                      size="small"
                      @click="handlePreview(row)"
                    >下载</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="check-items">
            <header>检查项</header>
            <template v-if="CheckFeeback.length !== 0">
              <div
                v-for="item of CheckFeeback"
                :key="item.CheckId"
                class="check-table"
              >
                <div class="check-table-title">
                  检查类型：{{ item.CheckName }}
                </div>
                <el-table :data="item.Check_Item" style="width: 50%" stripe>
                  <el-table-column
                    prop="Check_Content"
                    label="检查项"
                    width="180"
                  />
                  <el-table-column
                    prop="Eligibility_Criteria"
                    label="合格标准"
                    width="180"
                  />
                  <el-table-column prop="Actual_Measurement" label="检查内容">
                    <template slot-scope="{ row }">
                      <div>
                        <el-input
                          v-model="row.Actual_Measurement"
                          :disabled="isSee"
                          type="text"
                          style="
                            width: 80%;
                            border: 1px solid #eee;
                            border-radius: 4px;
                          "
                          @blur="
                            (e) => {
                              inputBlur(e, row.Actual_Measurement, row);
                            }
                          "
                        />
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
            <div v-else class="no-check-table">暂无检查项</div>
          </div>
        </div>
        <div v-if="!isSee" class="submit-btn">
          <el-button
            type="primary"
            :loading="btnLoading"
            @click="submit"
          >提交</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetProfessionalType } from '@/api/plm/material'
import { GetSheetDwg } from '@/api/PRO/qualityInspect/quality-management'
import {
  EntityQualityManagement,
  GetFactoryPeoplelist,
  SaveTesting,
  GetEditById
} from '@/api/PRO/qualityInspect/quality-management'
import OSSUpload from '@/views/plm/components/ossupload'
import { closeTagView, parseTime } from '@/utils'
import { pathToDrawing } from '@/utils/pathToDrawing'
export default {
  name: 'PROQualityManagement',
  components: {
    OSSUpload
  },
  data() {
    return {
      btnLoading: false,
      form: {
        SteelName: '',
        Check_Object_Type: '',
        Check_Object_Type_Id: '',
        Check_Node_Id: '',
        Check_Node_Name: '',
        Check_Type: '',
        Sheet_Result: '合格',
        Rectifier_Id: '',
        Rectifier_Name: '',
        Rectify_Date: '',
        Participant_Id: '',
        Participant_Name: '',
        Participant_Ids: [],
        Rectify_Description: '',
        Suggestion: '',
        dateValue: '',
        Short_Name: '',
        Number: ''
      },
      rules: {
        SteelName: [
          { required: true, message: '必填字段不能为空', trigger: 'blur' }
        ],
        Check_Node_Name: [
          { required: true, message: '必填字段不能为空', trigger: 'blur' }
        ],
        Check_Object_Type: [
          { required: true, message: '必填字段不能为空', trigger: 'blur' }
        ],
        Check_Type: [
          { required: true, message: '必填字段不能为空', trigger: 'blur' }
        ],
        Sheet_Result: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        // Rectify_Date: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ],
        Rectifier_Id: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        dateValue: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      comType: [],
      form2: {},
      CheckFeeback: [],
      textarea1: '',
      textarea2: '',
      fileList: [],
      Attachments: [],
      steelCadList: [],
      isEdit: true,
      checkDateList: [],
      userList: [],
      Group_Ids: [],
      isAllPass: true,
      isSee: false,
    }
  },
  created() {
    // console.log(this.$route.query.isSee, "this.$route.query.isSee");
    this.isSee = Boolean(this.$route.query.isSee)
    this.getFactoryPeoplelist()
  },
  methods: {
    getFactoryPeoplelist() {
      GetFactoryPeoplelist().then((res) => {
        if (res.IsSucceed) {
          this.userList = res.Data
          this.getEntityQualityManagement(this.$route.query.sheetId)
        }
      })
      // console.log(this.comType, "this.comType");
    },
    getEntityQualityManagement(sheetId) {
      EntityQualityManagement({
        sheetid: sheetId
        // SheetId: sheetId,
      }).then((res) => {
        if (res.IsSucceed) {
          if (!this.isSee) {
            if (res.Data.Source_Type === 0) {
              this.form.Rectifier_Id = ''
            } else {
              this.form.Rectifier_Id = res.Data.Create_UserId
            }

            this.checkDateList = res.Data
            this.form.Short_Name = this.checkDateList.Short_Name
            this.form.Number = this.checkDateList.Number
            this.form.SteelName = this.checkDateList.SteelName
            this.form.Check_Object_Type = this.checkDateList.Check_Object_Type
            this.form.Check_Object_Type_Id =
              this.checkDateList.Check_Object_Type_Id
            this.form.Check_Node_Id = this.checkDateList.Check_Node_Id
            this.form.Check_Node_Name = this.checkDateList.Check_Node_Name
            this.form.Check_Type = this.checkDateList.Check_Type
            this.Group_Ids = this.checkDateList.Group_Ids
            const _checkFeebacks = res.Data.CheckFeebacks || []
            if (_checkFeebacks.length !== 0) {
              this.CheckFeeback = _checkFeebacks.map((item) => {
                item.isPass = true
                // Actual_Measurement
                if (item.Check_Item.length !== 0) {
                  item.Check_Item.map((item2) => {
                    item2.Actual_Measurement = ''
                    item2.Check_Type_Id = item.CheckId
                    item2.CheckName = item.CheckName
                  })
                }
                return item
              })
            }
            this.CheckFeeback = JSON.parse(JSON.stringify(this.CheckFeeback)) // ?
            // console.log(res.Data.CheckFeeback, "res.Data.CheckFeeback2");
            console.log(this.CheckFeeback, 'this.CheckFeeback')
          } else {
            this.form.Rectifier_Id = res.Data.Rectifier_Id
            this.checkDateList = res.Data
            this.form.Short_Name = this.checkDateList.Short_Name
            this.form.Number = this.checkDateList.Number
            this.form.SteelName = this.checkDateList.SteelName
            this.form.Check_Object_Type = this.checkDateList.Check_Object_Type
            this.form.Check_Object_Type_Id =
              this.checkDateList.Check_Object_Type_Id
            this.form.Check_Node_Id = this.checkDateList.Check_Node_Id
            this.form.Check_Node_Name = this.checkDateList.Check_Node_Name
            this.form.Check_Type = this.checkDateList.Check_Type
            this.form.Sheet_Result = this.checkDateList.Sheet_Result
            if (this.checkDateList.Rectify_Date) {
              this.form.Rectify_Date = parseTime(
                new Date(this.checkDateList.Rectify_Date),
                '{y}-{m}-{d}'
              )
            } else {
              this.form.Rectify_Date = ''
            }

            // this.form.Rectify_Date = this.checkDateList.Rectify_Date;
            this.form.Participant_Ids = this.checkDateList.Participant_Ids
            this.form.Rectify_Description =
              this.checkDateList.Rectify_Description
            this.form.Suggestion = this.checkDateList.Suggestion
            const checkTypeResultJson =
              this.checkDateList?.Check_Type_Result_Json
            this.CheckFeeback = JSON.parse(checkTypeResultJson) || []
            console.log(this.CheckFeeback, '1this.CheckFeeback查看')
            if (this.form.Sheet_Result === '不合格') {
              const temp1 = this.userList.find((item) => {
                return item.Id === this.form.Rectifier_Id
              })
              this.form.Rectifier_Name = temp1.Display_Name
              const temp2 = this.userList.find((item) => {
                return item.Id === this.form.Participant_Ids[0]
              })
              this.form.Participant_Name = temp2?.Display_Name
            }
            if (this.checkDateList.Attachments.length !== 0) {
              this.checkDateList.Attachments.map((item) => {
                const imgObj1 = { name: '', url: '' }
                imgObj1.name = item.File_Name
                imgObj1.url = item.File_Url
                this.fileList.push(imgObj1)
              })
            }
            console.log(this.fileList)
          }

          this.getSheetDwg(this.$route.query.sheetId)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    changePass(e) {
      console.log(e, 'eeee')
      const isAllPass = this.CheckFeeback.every((item) => {
        return item.isPass
      })
      console.log(this.isAllPass, 'this.isAllPass')
      if (isAllPass) {
        this.form.Sheet_Result = '合格'
      } else {
        this.form.Sheet_Result = '不合格'
      }
    },
    changeCategory(e) {
      console.log(e, 'e22222')
      if (e) {
        this.form.Participant_Ids.push(e)
      }
    },
    uploadSuccess(response, file, fileList) {
      console.log('on-success')
      console.log(response, 'response')
      console.log(file, 'file')
      console.log(fileList, 'fileList')
      // this.fileList = fileList;
      console.log(this.fileList, 'this.fileList')
      this.Attachments = []
      fileList.map((item) => {
        const imgObj = { File_Name: '', File_Url: '' }
        imgObj.File_Name = item.name
        if (item.hasOwnProperty('response')) {
          // imgObj.File_Url = item.response.Data.split("*")[0];
          imgObj.File_Url = item.response.encryptionUrl
        } else {
          imgObj.File_Url = item.url
        }
        this.Attachments.push(imgObj)
      })
      console.log(this.Attachments, 'this.Attachments')
    },
    uploadExceed(files, fileList) {
      console.log(this.fileList, '已超过')
      this.$message({
        type: 'warning',
        message: '已超过文件上传最大数量'
      })
    },
    uploadRemove(file, fileList) {
      console.log('on-remove')
      console.log(file, 'file')
      console.log(fileList, 'fileList')
      // this.fileList = fileList;
      console.log(this.fileList, 'this.fileList')
      this.Attachments = []
      fileList.map((item) => {
        const imgObj = { File_Name: '', File_Url: '' }
        imgObj.File_Name = item.name
        if (item.hasOwnProperty('response')) {
          // imgObj.File_Url = item.response.Data.split("*")[0];
          imgObj.File_Url = item.response.encryptionUrl
        } else {
          imgObj.File_Url = item.url
        }
        this.Attachments.push(imgObj)
      })
      console.log(this.Attachments, 'this.Attachments')
    },
    handlePreview(file) {
      console.log('on-preview')
      console.log(file, 'file')
      let fielUrl = null
      if (file.hasOwnProperty('response')) {
        // fielUrl = file.response.Data.split("*")[0];
        fielUrl = file.response.encryptionUrl
      } else {
        fielUrl = file.url
      }
      console.log(fielUrl, 'fielUrl')
      window.open(fielUrl, '_blank')
    },
    inputBlur(e, e1, row) {
      // console.log(e, "inputBlur");
      console.log(e1, 'inputBlur1')
      console.log(row, 'inputBlur1row')
    },
    getSheetDwg(sheetId) {
      if (this.form.Check_Object_Type === 0) {
        console.log('this.form.Check_Object_Type')
      }
      GetSheetDwg({ id: sheetId }).then((res) => {
        if (res.IsSucceed) {
          this.steelCadList = res.Data || [] // 编辑时有多张，包括修改后的
        }
      })
    },
    openDwg(dwg) {
      pathToDrawing({
        File_Url: dwg.url,
        File_Name: this.form.SteelName,
        canSave: false,
        canvasId: dwg.canvasId,
        viewMode: true
      })
    },
    submit() {
      this.btnLoading = true
      const {
        SteelName,
        Check_Object_Type,
        Check_Node_Id,
        Check_Type,
        Sheet_Result,
        Check_Object_Type_Id,
        Rectify_Date,
        Rectifier_Id,
        Participant_Ids,
        Rectify_Description,
        Suggestion
      } = this.form
      const submitObj = {
        Sheets: [
          {
            SteelName: SteelName,
            Check_Object_Type: Check_Object_Type,
            Check_Object_Type_Id: Check_Object_Type_Id,
            Check_Node_Id: Check_Node_Id,
            Check_Type: Check_Type,
            Sheet_Result: Sheet_Result,
            Check_Type_Result_Json: JSON.stringify(this.CheckFeeback),
            SheetId: this.$route.query.sheetId,
            Rectify_Date: Rectify_Date
              ? parseTime(Rectify_Date, '{y}-{m}-{d}')
              : '',
            Rectifier_Id: Rectifier_Id,
            Participant_Ids: Participant_Ids,
            Rectify_Description: Rectify_Description,
            Suggestion: Suggestion,
            Group_Ids: this.Group_Ids || []
          }
        ],
        _Attachments: this.Attachments,
        plm_Factory_Check: []
      }
      let tempData = []
      this.CheckFeeback.map((item) => {
        tempData = [...tempData, ...item.Check_Item]
      })
      tempData.forEach((item) => {
        const {
          Id,
          Check_Content,
          Eligibility_Criteria,
          Actual_Measurement,
          Check_Type_Id
        } = item
        const tempItem = {
          Item_Id: Id || '',
          Check_Name: Check_Content || '',
          Eligibility_Criteria: Eligibility_Criteria || '',
          Actual_Measurement: Actual_Measurement || '',
          Check_Type_Id: Check_Type_Id || ''
        }
        console.log(tempItem, 'tempItem')
        submitObj.plm_Factory_Check.push(tempItem)
      })
      console.log(submitObj, 'submitObj')
      this.$refs.form.validate((valid) => {
        if (valid) {
          SaveTesting(submitObj).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '质检成功',
                type: 'success'
              })
              // this.$emit("close");
              // this.$emit("refresh");
              closeTagView(this.$store, this.$route)
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
            this.btnLoading = false
          })
        } else {
          this.btnLoading = false
          console.log('error submit!!')
          return false
        }
      })
    },
    toBack() {
      if (this.isSee) {
        closeTagView(this.$store, this.$route)
      } else {
        this.$confirm('此操作不会保存编辑数据，是否退出？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            closeTagView(this.$store, this.$route)
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            })
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper-c {
  background: #fff;
  // padding: 16px;
  overflow: auto;
  // position: relative;
}
.app-container {
  position: relative;
  padding: 50px 20px 20px 20px;
  .top-btn {
    position: absolute;
    top: 12px;
    left: 20px;
    z-index: 99;
    .el-button {
      background-color: #f7f8f9;
    }
  }
  .wrapper-top {
    width: 100%;
    position: relative;
    .top-title {
      position: absolute;
      background-color: #fff;
      width: 100%;
      // display: flex;
      // justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #eee;
      span {
        // font-weight: bold;
        color: #333;
        font-size: 16px;
        font-weight: 500;
        padding-right: 20px;
      }
    }
  }
  .wrapper-main {
    // padding-left: 30px
    padding-top: 50px;
    padding-bottom: 64px;
    header {
      padding-bottom: 16px;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }
    .basic-information {
      padding: 24px 0 0 16px;
      border-bottom: 1px solid #eee;
      ::v-deep .el-input {
        width: 250px;
        margin-right: 30px;
      }
      ::v-deep .el-icon-circle-close {
        color: #d0d3db;
      }
    }
    .inspection-type {
      padding: 24px 0 24px 16px;
      border-bottom: 1px solid #eee;
      .radio-items {
        padding-left: 30px;
        width: 900px;
        display: flex;
        flex-wrap: wrap;
        font-size: 14px;
        color: #333;
        .radio-item {
          padding-bottom: 24px;
          margin-right: 38px;
          span {
            margin-right: 24px;
          }
          .el-radio {
            margin-right: 16px;
          }
        }
      }
      .no-radio-items {
        font-size: 14px;
        padding: 0 16px 0px 16px;
        color: #333;
      }
    }
    .detailed-drawings {
      padding: 24px 0 24px 16px;
      border-bottom: 1px solid #eee;
      .deep-img {
        width: 100%;
        height: 150px;
      }
      .dwg_ico {
        width: 150px;
        height: 150px;
        background: url("./image/dwg_ico.jpg") no-repeat;
        background-size: 100%;
        cursor: pointer;
        float: left;
        margin-right: 10px;
      }
      .font-cad {
        font-size: 14px;
        color: #333;
        padding: 0 16px 0px 16px;
      }
    }
    .inspection-description {
      display: flex;
      header {
        font-weight: 500;
        padding-right: 6px;
      }
      padding: 24px 0 24px 16px;
      // border-bottom: 1px solid #eee;
    }
    .rectification {
      display: flex;
      header {
        font-weight: 500;
        padding-right: 6px;
      }
      padding: 24px 0 24px 16px;
      // border-bottom: 1px solid #eee;
    }
    .img-up {
      display: flex;
      header {
        font-weight: 500;
        padding-right: 6px;
      }
      padding: 24px 0 24px 16px;
      border-bottom: 1px solid #eee;
    }
    .check-items {
      padding: 24px 0 24px 16px;
      border-bottom: 1px solid #eee;
      .check-table {
        background-color: rgba(245, 246, 248, 0.5);
        margin-bottom: 16px;
        .check-table-title {
          font-size: 14px;
          padding: 16px;
          color: #333;
        }
      }
      .no-check-table {
        font-size: 14px;
        padding: 0 16px 0px 16px;
        color: #333;
      }
    }
  }
  .submit-btn {
    padding: 16px 0 16px 16px;
    position: absolute;
    bottom: 20px;
    background-color: #fff;
    width: calc(100% - 40px);
  }
}
</style>

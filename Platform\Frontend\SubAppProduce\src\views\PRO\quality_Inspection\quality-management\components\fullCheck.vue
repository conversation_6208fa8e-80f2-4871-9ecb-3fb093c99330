<template>
  <div class="wrapper-main">
    <div ref="searchDom" class="search_wrapper">
      <el-form ref="form" :model="form.Feedmodel" label-width="80px">
        <el-row>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="单据状态" prop="Status">
              <el-select
                v-model="form.Feedmodel.Status"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option label="草稿" :value="0" />
                <el-option label="待整改" :value="1" />
                <el-option label="待复核" :value="2" />
                <el-option label="待质检" :value="4" />
                <el-option label="已完成" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="质检结果" prop="Sheet_result">
              <el-select
                v-model="form.Feedmodel.Sheet_result"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option label="合格" value="合格" />
                <el-option label="不合格" value="不合格" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="质检对象" prop="Check_Object_Type_Id">
              <el-select
                v-model="form.Feedmodel.Check_Object_Type_Id"
                filterable
                clearable
                placeholder="请选择"
                @change="qualityListChange"
                @clear="qualityListClear"
              >
                <el-option
                  v-for="item in qualityList"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="质检节点" prop="Check_Node_Id">
              <el-select
                v-model="form.Feedmodel.Check_Node_Id"
                filterable
                clearable
                placeholder="请选择"
                :disabled="!Boolean(form.Feedmodel.Check_Object_Type_Id)"
                @change="nodeChange"
              >
                <el-option
                  v-for="item in nodeList"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" :lg="6" :xl="6">
            <el-form-item label="项目名称" prop="Project_Id">
              <el-select
                v-model="form.Feedmodel.Project_Id"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.Id"
                  :label="item.Short_Name"
                  :value="item.Sys_Project_Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :lg="5" :xl="6">
            <el-form-item label="报检时间" prop="Check_Time">
              <el-date-picker
                v-model="form.Feedmodel.Check_Time"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5" :lg="5" :xl="6">
            <el-form-item label="名称" prop="SteelName">
              <el-input
                v-model="form.Feedmodel.SteelName"
                type="text"
                placeholder="请输入（空格间隔筛选多个）"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="4" :lg="4" :xl="3">
            <el-form-item label="来源" prop="Source_Type">
              <el-select
                v-model="form.Feedmodel.Source_Type"
                filterable
                clearable
                placeholder="请选择"
              >
                <el-option label="手动创建" value="手动创建" />
                <el-option label="业务推送" value="业务推送" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :lg="4" :xl="3">
            <el-form-item label-width="16px">
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button
                @click="
                  $refs['form'].resetFields();
                  handleSearch();
                "
              >重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table_warrap" style="flex: 1">
      <div class="table_content">
        <div
          style="
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
          "
        >
          <div>
            <el-button type="primary" @click="addCheck('add')">新增</el-button>
            <el-button @click="addCheck()">批量新增</el-button>
            <el-button
              :disabled="selectList.length == 0"
              @click="allPass()"
            >批量合格</el-button>
            <el-button
              type="success"
              :disabled="selectList.length == 0"
              :loading="downloadLoading"
              @click="handleDownload()"
            >
              {{ downloadLoading ? `下载中 (${downloadCompleted}/${downloadTotal})` : '导出标注图纸' }}
            </el-button>
            <ExportCustomReport code="inspection_manage" style="margin-left: 10px" :ids="selectList.map(i=>i.Id)"></ExportCustomReport>
          </div>
        </div>
        <el-main
          v-loading="loading"
          class="no-v-padding cs-main"
          style="padding: 0;"
        >
          <DynamicDataTable
            ref="table"
            :config="tbConfig"
            :columns="columns"
            :data="tbData"
            :total="total"
            :page="pageInfo.Page"
            stripe
            class="cs-plm-dy-table"
            border
            @gridPageChange="handlePageChange"
            @gridSizeChange="handleSizeChange"
            @multiSelectedChange="multiSelectedChange"
          >
            <template slot="Number" slot-scope="{ row }">
              <div>{{ row.Number || "-" }}</div>
            </template>
            <template slot="Rectify_Date" slot-scope="{ row }">
              <div>{{ row.Rectify_Date || "-" }}</div>
            </template>
            <template slot="Rectifier_name" slot-scope="{ row }">
              <div>{{ row.Rectify_Date ? row.Rectifier_name : "-" }}</div>
            </template>
            <template slot="Partcipant_name" slot-scope="{ row }">
              <div>{{ row.Partcipant_name || "-" }}</div>
            </template>
            <template slot="SteelName" slot-scope="{ row }">
              <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
              <span>{{ row.SteelName || "-" }}</span>
            </template>
            <template slot="Check_Result" slot-scope="{ row }">
              <span v-if="!row.Check_Result">-</span>
              <template v-else>
                <el-tag v-if="row.Check_Result==='合格'" type="success">{{ row.Check_Result }}</el-tag>
                <el-tag v-else type="danger">{{ row.Check_Result }}</el-tag>
              </template>
            </template>
            <template slot="Status" slot-scope="{ row }">
              <span v-if="row.Status === '已完成'" class="by-dot by-dot-success">
                {{ row.Status || "-" }}
              </span>
              <span v-else-if="row.Status === '待复核' || row.Status === '待整改'" class="by-dot by-dot-primary">
                {{ row.Status || "-" }}
              </span>
              <span v-else-if="row.Status === '待质检' || row.Status === '草稿'" class="by-dot by-dot-info">
                {{ row.Status || "-" }}
              </span>
              <span v-else>
                {{ row.Status || "-" }}
              </span>
            </template>
            <template slot="Pick_Date" slot-scope="{ row }">
              <div>{{ row.Pick_Date || "-" }}</div>
            </template>
            <template slot="Code" slot-scope="{ row }">
              <div>
                <el-button
                  v-if="row.Code"
                  type="text"
                  @click="handleRecord(row)"
                >查看</el-button>
                <div v-else>-</div>
              </div>
            </template>
            <template slot="op" slot-scope="{ row, index }">
              <div v-if="row.Status === '草稿'">
                <!-- <el-button
                  :index="index"
                  type="text"
                  @click="handleEdit(row.Id)"
                  >编辑</el-button
                > -->
                <el-button
                  :index="index"
                  type="text"
                  @click="handleSub(row.SheetId)"
                >提交</el-button>
                <el-button
                  :index="index"
                  type="text"
                  @click="handleDel(row.SheetId)"
                >删除</el-button>
              </div>
              <el-button
                v-else-if="row.Status === '待质检'"
                :index="index"
                type="text"
                @click="handleCheck(row.SheetId)"
              >质检</el-button>
              <el-button
                v-else
                :index="index"
                type="text"
                @click="handleInfo(row.SheetId)"
              >查看</el-button>
            </template>
          </DynamicDataTable>
        </el-main>
      </div>
    </div>
    <el-dialog
      v-if="dialogVisible"
      ref="content"
      v-el-drag-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="480px"
      class="plm-custom-dialog"
      @close="handleClose"
    >
      <component
        :is="currentComponent"
        ref="content"
        :select-row="selectRow"
        @openDialog="openDialog"
        @close="handleClose"
        @refresh="fetchData"
        @qualityItemChange="qualityItemChange"
        @handleCheck="handleCheck"
      />
    </el-dialog>

    <el-dialog
      v-if="dialogVisible2"
      ref="content2"
      v-el-drag-dialog
      :title="dialogTitle2"
      :visible.sync="dialogVisible2"
      width="66%"
      class="plm-custom-dialog"
      @close="handleClose2"
    >
      <component
        :is="currentComponent2"
        ref="content2"
        :quality-item="qualityItem"
        @close="handleClose2"
        @getSelectRow="getSelectRow"
        @bitchaddCom="bitchaddCom"
      />
    </el-dialog>
    <el-dialog
      ref="content3"
      v-el-drag-dialog
      title="操作记录"
      :visible.sync="dialogVisible3"
      width="50%"
      class="plm-custom-dialog"
      @close="handleClose3"
    >
      <!-- <rectification-Dialog ref="content3" /> -->
      <component :is="currentComponent3" ref="content3" @close="handleClose3" />
    </el-dialog>
    <selected-Dialog
      ref="selectRef"
      @BitchopenDialog="BitchopenDialog"
      @refresh="fetchData"
      @qualityItemChange="qualityItemChange"
    />
  </div>
</template>

<script>
import addComponent from './add/addComponent.vue'
import rectificationDialog from './rectification/rectificationDialog.vue'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import addDialog from './add/addDialog.vue'
import elDragDialog from '@/directive/el-drag-dialog'
import {
  DelLanch,
  GetCanvasSheetDwg,
  GetDictionaryDetailListByCode,
  GetNodeList,
  GetPageQualityManagement,
  RectificationRecord,
  SavePass,
  SubmitLanch
} from '@/api/PRO/qualityInspect/quality-management'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { GetProjectPageList } from '@/api/PRO/pro-schedules'
import getTbInfo from '@/mixins/PRO/get-table-info-pro2'
import addRouterPage from '@/mixins/add-router-page'
import selectedDialog from './add/selectedDialog'
import { parseTime } from '@/utils'
import { GetStopList } from '@/api/PRO/production-task'
import { pathToDrawing } from '@/utils/pathToDrawing'
import ExportCustomReport from "@/components/ExportCustomReport/index.vue";

export default {
  directives: { elDragDialog },
  components: {
    ExportCustomReport,
    DynamicDataTable,
    addDialog,
    addComponent,
    rectificationDialog,
    selectedDialog
  },
  mixins: [addRouterPage, getTbInfo],
  data() {
    return {
      code: '',
      TypeId: '',
      typeOption: '',
      dialogVisible: false,
      dialogVisible2: false,
      dialogVisible3: false,
      loading: false,
      downloadLoading: false,
      downloadTotal: 0,
      downloadCompleted: 0,
      messageListener: null,
      dialogTitle: '',
      dialogTitle2: '',
      Ismodal: true,
      dialogData: {},
      currentComponent: '',
      currentComponent2: '',
      currentComponent3: 'rectificationDialog',
      tbConfig: {
        Op_Width: 100
      },
      Data: [],
      form: {
        Feedmodel: {
          Status: '',
          Sheet_result: '',
          Check_Object_Type_Id: '',
          Check_Node_Id: '',
          Project_Id: '',
          Source_Type: '',
          SteelName: '',
          Check_Style: 1,
          Check_Time: [],
          BeginDate: null,
          EndDate: null
        },
        PageInfo: {
          Page: 1,
          PageSize: 20
        }
      },
      selectList: [],
      qualityList: [],
      qualityItem: {},
      selectRow: {},
      nodeList: [],
      projectList: [],
      Date_Time: '',
      columns: [],
      tbData: [],
      pageInfo: {
        Page: 1,
        TotalCount: 0,
        PageSize: 20
      },
      total: 0,
      gridCode: 'pro_start_inspect',
      searchHeight: 0,
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ],
        code: ''
      },
      addPageArray: [
        {
          path: this.$route.path + '/check',
          hidden: true,
          component: () =>
            import(
              '@/views/PRO/quality_Inspection/quality-management/components/Detail'
            ),
          name: 'PROQualityTesting',
          meta: { title: '质检' }
        },
        {
          path: this.$route.path + '/check',
          hidden: true,
          component: () =>
            import(
              '@/views/PRO/quality_Inspection/quality-management/components/Detail'
            ),
          name: 'PROQualityInfo',
          meta: { title: '查看' }
        }
      ]
    }
  },
  created() {
    this.getFactoryTypeOption()
    this.getDictionaryDetailListByCode()
    this.getProjectPageList()
    this.setupPostMessageListener()
  },
  mounted() {
    // this.getTypeList();
    this.searchHeight = this.$refs.searchDom.offsetHeight + 200
    console.log('this.searchHeight', this.searchHeight)
  },
  beforeDestroy() {
    // 清理postMessage监听器
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener)
    }
  },
  methods: {
    handleDownload() {
      this.downloadLoading = true
      this.downloadTotal = 0
      this.downloadCompleted = 0

      GetCanvasSheetDwg({
        ids: this.selectList.map(i => i.SheetId)
      }).then(res => {
        this.downloadTotal = res.Data.length
        if (this.downloadTotal === 0) {
          this.downloadLoading = false
          this.$message.warning('没有可下载的图纸')
          return
        }

        res.Data.forEach((item, index) => {
          const path = pathToDrawing({
            File_Url: item.url,
            File_Name: this.getFileNameFromUrl(item.url),
            canSave: false,
            canvasId: item.canvasId,
            autoStart: true,
            exportMode: true
          })
          setTimeout(() => {
            this.createCustomIframe(path, item.canvasId)
          }, index * 100)
        })
      }).catch(error => {
        this.downloadLoading = false
        this.$message.error('获取图纸信息失败')
        console.error('下载失败:', error)
      })
    },
    getFileNameFromUrl(url) {
      // 先去掉链接中 "?" 后面的参数部分
      const urlWithoutParams = url.split('?')[0]
      // 分割路径，取最后一个 "/" 后面的部分（即文件名）
      const fileName = urlWithoutParams.split('/').pop()
      return fileName
    },
    createCustomIframe(src, canvasId) {
      // 创建iframe元素
      const iframe = document.createElement('iframe')
      // 设置iframe的源地址
      iframe.src = src
      // 设置iframe的宽度和高度
      iframe.width = '1920px'
      iframe.height = '1080px'
      iframe.style.position = 'fixed'
      iframe.style.left = '-1000vw'
      iframe.style.top = '0'
      iframe.style.zIndex = '-1'
      iframe.id = `download-iframe-${canvasId}-${Date.now()}`

      // 将iframe添加到文档中（这里添加到body元素中）
      document.body.appendChild(iframe)

      console.log(`创建iframe: ${iframe.id}, 路径: ${src}`)
    },
    setupPostMessageListener() {
      this.messageListener = (event) => {
        // 检查消息类型
        if (event.data && event.data.type === 'PDF_DOWNLOADED') {
          this.downloadCompleted++
          console.log(`PDF下载完成: ${this.downloadCompleted}/${this.downloadTotal}`)

          // 如果所有PDF都下载完成，关闭loading
          if (this.downloadCompleted >= this.downloadTotal) {
            this.downloadLoading = false
            this.$message.success(`质检图纸下载完成，共${this.downloadTotal}个文件`)

            // 延迟清理iframe
            setTimeout(() => {
              this.cleanupIframes()
            }, 2000)
          }
        }
      }

      // 添加事件监听器
      window.addEventListener('message', this.messageListener)
    },
    cleanupIframes() {
      // 清理所有下载相关的iframe
      const iframes = document.querySelectorAll('iframe[id^="download-iframe-"]')
      iframes.forEach(iframe => {
        console.log(`清理iframe: ${iframe.id}`)
        iframe.remove()
      })
    },

    async getFactoryTypeOption() {
      await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProfessionalType = res.Data
          this.code = this.ProfessionalType[0].Code
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      await this.getTableConfig(
        `pro_check_order_list,${this.ProfessionalType[0].Code}`
      )
      this.fetchData()
    },
    fetchData() {
      this.loading = true
      // const form = { ...this.form };
      // delete form.Feedmodel["SteelName"];
      const form = JSON.parse(JSON.stringify(this.form))
      if (form.Feedmodel.SteelName) {
        form.Feedmodel.SteelName = form.Feedmodel.SteelName.trim().replaceAll(
          ' ',
          '\n'
        )
      }

      if (form.Feedmodel.Check_Time && form.Feedmodel.Check_Time.length === 2) {
        form.Feedmodel.BeginDate = form.Feedmodel.Check_Time[0]
        form.Feedmodel.EndDate = form.Feedmodel.Check_Time[1]
      } else {
        form.Feedmodel.BeginDate = null
        form.Feedmodel.EndDate = null
      }

      // console.log(form, "form");
      GetPageQualityManagement(form).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map((v) => {
            v.Rectify_Date = v.Rectify_Date
              ? parseTime(new Date(v.Rectify_Date), '{y}-{m}-{d}')
              : v.Rectify_Date
            v.Pick_Date = v.Pick_Date
              ? parseTime(new Date(v.Pick_Date), '{y}-{m}-{d}')
              : v.Pick_Date
            v.Id = v.SheetId // 解决全选框打勾问题
            return v
          })
          this.total = res.Data.TotalCount
          this.loading = false
          this.getStopList(this.tbData)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    async getStopList(list) {
      const key = 'Check_Object_Id'
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Type: item.Check_Object_Type === '构件' ? 2 : item.Check_Object_Type === '零件' ? 3 : 1
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },
    // 获取质检对象
    getDictionaryDetailListByCode() {
      GetDictionaryDetailListByCode({
        dictionaryCode: 'Quality_Code'
      }).then((res) => {
        if (res.IsSucceed) {
          this.qualityList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    qualityListChange(e) {
      if (e) {
        this.form.Feedmodel.Check_Node_Id = ''
        this.getNodeList()
      }
    },
    qualityListClear(e) {
      this.form.Feedmodel.Check_Node_Id = ''
      this.nodeList = []
      // this.$refs.form.resetFields();
    },
    // 获取质检节点
    getNodeList() {
      GetNodeList({
        check_object_id: this.form.Feedmodel.Check_Object_Type_Id,
        Check_Style: 1
      }).then((res) => {
        if (res.IsSucceed) {
          this.nodeList = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    nodeChange(e) {
      if (e) {
        this.nodeList.find((item) => {
          if (item.Id === e) {
            this.form.Feedmodel.checkType = item.Check_Type
          }
        })
      }
    },
    // 获取项目列表
    getProjectPageList() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projectList = res.Data.Data
        }
      })
    },
    // rectificationInfo(id) {
    //   // console.log(id, "id");
    //   this.dialogVisible3 = true;
    //   // console.log(this.dialogVisible3, "dialogVisible3");
    // },
    handleRecord(row) {
      // this.recordSheetId = sheetId;
      // this.dialogVisible3 = true;
      // console.log(sheetId, "sheetId333333333");
      this.getrectificationRecord(row)
    },
    getrectificationRecord(row) {
      RectificationRecord({ sheetid: row.SheetId }).then((res) => {
        if (res.IsSucceed) {
          if (res.Data.length === 0) {
            this.$message({
              type: 'error',
              message: '暂无操作记录'
            })
          } else {
            // this.$refs.content3.init(res.Data)
            this.dialogVisible3 = true
            this.$nextTick((_) => {
              this.$refs['content3'].init(row)
            })
          }
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    handleSearch() {
      this.form.PageInfo.Page = 1
      this.fetchData()
    },
    changesearchDate(val) {
      console.log(val)
    },
    allPass() {
      // console.log(this.selectList, "this.selectList");
      const ids = []
      this.selectList.forEach((item) => {
        ids.push(item.SheetId)
      })
      console.log(ids, 'ids')
      this.$confirm('质检该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          SavePass({ ids }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '质检成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    // 将选中的零构件保存到质检单里
    bitchaddCom(val) {
      // console.log(val,"val");
      this.$nextTick((_) => {
        // 1 保证是从全检页面添加
        this.$refs['selectRef'].handelInfo(val, 1)
      })
    },
    // setGrid(grid) {
    //   this.tbConfig = Object.assign({}, grid, {});
    //   this.pageInfo.PageSize = parseInt(this.tbConfig.Row_Number);
    // },

    // setCols(cols) {
    //   this.columns = cols;
    //   console.log(cols);
    // },

    // setGridData(data) {
    //   this.tbData = data.Data;
    //   this.pageInfo.TotalCount = data.TotalCount;
    //   this.TotalAmount = data.TotalAmount;
    //   this.TotalWeight = data.TotalWeight;
    // },

    // gridPageChange({ page }) {
    //   this.pageInfo.Page = Number(page);
    //   this.fetchData();
    // },
    // gridSizeChange({ size }) {
    //   this.tbConfig.Row_Number = Number(size);
    //   this.pageInfo.PageSize = Number(size);
    //   this.pageInfo.Page = 1;
    //   this.fetchData();
    // },

    multiSelectedChange(item) {
      this.selectList = item
    },
    qualityItemChange(e) {
      this.qualityItem = e
    },
    getSelectRow(e) {
      this.selectRow = e
    },
    generateComponent(title, component, flag) {
      if (flag && flag == 2) {
        this.dialogTitle2 = title
        this.currentComponent2 = component
        this.dialogVisible2 = true
        // this.width = "480px";
      } else {
        this.dialogTitle = title
        this.currentComponent = component
        this.dialogVisible = true
        // this.width = "480px";
      }
    },
    openDialog() {
      if (this.qualityItem.Display_Name === '构件') {
        this.generateComponent('添加构件', 'addComponent', 2)
      } else if (this.qualityItem.Display_Name === '零件') {
        this.generateComponent('添加零件', 'addComponent', 2)
      } else {
        this.generateComponent('添加部件', 'addComponent', 2)
      }
      this.$nextTick((_) => {
        this.$refs['content2'].init()
      })
    },
    BitchopenDialog(Id, title, Ids) {
      console.log(Id, 'Id')
      console.log(title, 'title')
      console.log(Ids, 'Ids')
      this.generateComponent(`添加${title}`, 'addComponent', 2)
      this.$nextTick((_) => {
        this.$refs['content2'].init(Id, '批量', Ids)
      })
    },
    addCheck(type) {
      if (type === 'add') {
        this.$store.dispatch('qualityCheck/changeRadio', true)
        this.generateComponent('新增', 'addDialog')
        this.$nextTick((_) => {
          this.$refs['content'].init('新增')
        })
      } else {
        this.$store.dispatch('qualityCheck/changeRadio', false)
        console.log(this.code, 'this.code1')
        this.$nextTick((_) => {
          this.$refs['selectRef'].init(this.code)
        })
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleClose2() {
      this.dialogVisible2 = false
    },
    handleClose3() {
      this.dialogVisible3 = false
    },
    handleEdit(id) {
      // this.$router.push({
      //   name: "PROShipSentEdit",
      //   query: { pg_redirect: "PROShipSent", id },
      // });
    },
    handleSub(SheetId) {
      console.log(SheetId, 'id')
      this.$confirm('提交该草稿, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          SubmitLanch({
            sheetid: SheetId
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '提交成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    handleDel(SheetId) {
      console.log(SheetId, 'id')
      this.$confirm('删除该草稿, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          DelLanch({
            sheetids: SheetId
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => {})
    },
    handleCheck(sheetId) {
      this.$router.push({
        name: 'PROQualityTesting',
        query: { pg_redirect: this.$route.name, sheetId, isSee: false }
      })
    },
    handleInfo(sheetId) {
      this.$router.push({
        name: 'PROQualityInfo',
        query: { pg_redirect: this.$route.name, sheetId, isSee: true }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";
.wrapper-main {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.search_wrapper {
  padding: 16px 16px 0;
  box-sizing: border-box;
  ::v-deep .el-form-item {
    .el-form-item__content {
      & > .el-input {
        width: 100%;
      }
      & > .el-select {
        width: 100%;
      }
    }
    .el-date-editor--daterange.el-input__inner {
      width: 100%;
    }
  }
}

.table_warrap {
  border-top: 16px solid #f8f8f8;
  .table_content {
    padding: 16px 16px 0;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
::v-deep .pagination {
  justify-content: flex-end !important;
  margin-top: 12px !important;
  .el-input--small .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}

.by-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  &:before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #f56c6c;
    border-radius: 50%;
    margin-right: 5px;
  }
}
.by-dot-success {
  color: #67c23a;
  &:before {
    background: #67c23a;
  }
}
.by-dot-primary {
  color: #409eff;
  &:before {
    background: #409eff;
  }
}
.by-dot-fail {
  color: #ff0000;
  &:before {
    background: #ff0000;
  }
}
.by-dot-info {
  &:before {
    background: #909399;
  }
}
.cs-main{
  flex: 1;
}
</style>

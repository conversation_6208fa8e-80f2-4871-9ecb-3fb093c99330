<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <top-header>
        <template #left>
          <div class="cs-header">
            <!-- <el-button
              circle
              icon="el-icon-arrow-left"
              size="mini"
              @click="toBack"
            /> -->
            <el-button @click="toBack">返回</el-button>
            <!-- <strong class="title">{{
              isEdit === true ? "编辑发货单" : "新增发货单"
            }}</strong> -->
          </div>
        </template>
        <template #right>
          <!-- <el-button type="primary" :loading="btnLoading">打印发货单</el-button> -->
          <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
        </template>
      </top-header>
      <!-- <title-info :title="projectName" /> -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="发货单号" prop="receiveNum">
              <el-input
                v-model="form.receiveNum"
                :disabled="autoGenerate || (!isDraft && isEdit)"
                :placeholder="autoGenerate ? '自动生成':'请输入'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货人" prop="receiveName">
              <el-input
                v-model="form.receiveName"
                clearable=""
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货人电话" prop="Receiver_Tel">
              <el-input
                v-model="form.Receiver_Tel"
                clearable=""
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="车辆信息" prop="License">
              <el-select
                v-model="form.License"
                clearable
                :disabled="((pageStatus>=2)||pageStatus===1)&&isEdit"
                placeholder="请选择"
                style="width: 70%"
                filterable
                @change="carChange"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.License"
                  :label="item.detail"
                  :value="item.License"
                />
              </el-select>
              <el-button
                style="margin-left: 10px"
                type="text"
                @click="handleEditCar"
              >新增车辆</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货部门" prop="Depart_Id">
              <el-select
                v-if="isProductweight===true"
                v-model="form.Depart_Id"
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in pickDepartmentList"
                  :key="item.Id"
                  :label="item.Display_Name"
                  :value="item.Id"
                />
              </el-select>
              <el-tree-select
                v-else
                ref="treeSelectDepart"
                v-model="form.Depart_Id"
                :select-params="{
                  clearable: true,
                }"
                class="cs-tree-x"
                :tree-params="treeParamsDepart"
                @select-clear="departClear"
                @node-click="departChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货人" prop="issueName">
              <el-input v-model="form.issueName" clearable="" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货时间" prop="Out_Date">
              <el-date-picker
                v-model="form.Out_Date"
                style="width: 100%"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="装车班" prop="Loadings">
              <SelectDepartment v-model="form.Loadings" @change="form.LoadingsPersonnel = ''" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="装车班人员" prop="LoadingsPersonnel ">
              <SelectDepartmentUser v-model="form.LoadingsPersonnel" :department-id="form.Loadings" :disabled="!form.Loadings" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="车次" prop="Trips ">
              <el-input v-model="form.Trips" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="专业" prop="ProfessionalTypeName">
              <el-input v-model="form.ProfessionalTypeName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货地址" prop="Address">
              <el-input v-model="form.Address" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货单位" prop="ReceivingUnit">
              <el-input v-model="form.ReceivingUnit" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物流费" prop="Logistics_Fee">
              <el-input-number
                v-model="form.Logistics_Fee"
                :min="0"
                :precision="2"
                style="width: 100%"
                class="cs-number-btn-hidden"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关联合同" prop="Contract_Id">
              <el-select
                v-model="form.Contract_Id"
                clearable
                filterable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="item in contractOptions"
                  :key="item.Id"
                  :label="item.ContractName"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input
                v-model="form.Remarks"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :maxlength="1000"
                show-word-limit
                type="textarea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="附件" class="factory-img">
              <OSSUpload
                class="upload-demo"
                action="alioss"
                :limit="10"
                :multiple="true"
                :on-success="
                  (response, file, fileList) => {
                    uploadSuccess(response, file, fileList)
                  }
                "
                :on-remove="uploadRemove"
                :on-preview="handlePreview"
                :on-exceed="handleExceed"
                :file-list="fileListData"
                :show-file-list="true"
                :disabled="false"
              >
                <el-button
                  type="primary"
                  :disabled="false"
                >上传文件</el-button>
              </OSSUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div v-if="isEdit">
        <h4> 过磅信息</h4>
        <el-form ref="form" inline :model="weightform" label-width="80px">
          <el-form-item label="皮重">
            {{ weightform.Tare_Weight }}kg
          </el-form-item>
          <el-form-item label="理重">
            {{ weightform.Reason_Weight }}kg
          </el-form-item>
          <el-form-item label="磅重">
            {{ weightform.Pound_Weight }}kg
          </el-form-item>
          <el-form-item label="净重" prop="region">
            <span :class="{'cs-red':showRed}">{{ netWeight }}
              <span v-if="showRed">（{{ getNum(netWeight,weightform.Reason_Weight)>0 ?'高于':'低于' }}理重{{ Math.abs(+getNum(netWeight, weightform.Reason_Weight)) }}kg）</span>
            </span>
          </el-form-item>
          <el-form-item label="过磅备注" prop="region">
            {{ plm_ProjectSendingInfo.Pound_Remark }}
          </el-form-item>
          <el-form-item label="附件">
            <template v-for="(item,idx) in weightFileInfo">
              <el-link
                :key="idx"
                :href="item.url"
                target="_blank"
              >{{ item.name }}</el-link>
              <el-divider v-if="idx!==weightFileInfo.length -1" :key="idx" direction="vertical" />
            </template>
          </el-form-item>
        </el-form>
      </div>

      <top-header style="margin-bottom: 10px">
        <template #left>
          <div class="cs-header" style="margin-bottom: 20px">
            <el-radio-group v-model="radio" size="small" @change="radioChange">
              <el-radio-button
                label="pro_component_out_detail_list"
              >构件</el-radio-button>
              <el-radio-button
                label="pro_package_out_detail_list"
              >打包件</el-radio-button>
              <el-radio-button
                label="PROShipUnitPart"
              >部件</el-radio-button>
              <el-radio-button
                label="PROShipPart"
              >零件</el-radio-button>
            </el-radio-group>
          </div>
        </template>
        <template #right>
          <div v-if="sendNumber" class="statistics-container">
            <div class="statistics-item" style="margin-right: 0">
              <span>发货序号：</span>
              <span>{{ sendNumber }}</span>
            </div>
          </div>
          <el-button
            v-if="!isSub"
            :disabled="!selectList.length"
            size="mini"
            type="danger"
            @click="handleDelete"
          >删除</el-button>
          <el-button
            v-if="!isSub"
            size="mini"
            type="primary"
            @click="handleAdd"
          >添加</el-button>
        </template>
      </top-header>

      <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
        <dynamic-data-table
          ref="dyTable"
          class="cs-plm-dy-table"
          :columns="columns"
          :config="tbConfig"
          :data="tabTypeCode === 2 ? tbData2 : tabTypeCode === 1 ? tbData :tabTypeCode === 3 ? tbData3 :tbData4"
          :page="form.PageInfo.Page"
          :sum-values="sums"
          :select-width="70"
          :total="total"
          border
          stripe
          @checkSelectable="checkSelectable"
          @multiSelectedChange="multiSelectedChange"
        >
          <template slot="Code" slot-scope="{ row }">
            <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
            <span>{{ row.Code }}</span>
          </template>
          <template slot="Part_Code" slot-scope="{ row }">
            <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
            <span>{{ row.Part_Code }}</span>
          </template>
          <template slot="S_Count" slot-scope="{ row }">
            <div v-if="!Is_Pack && !isSub">
              <el-input
                v-model="row.S_Count"
                type="text"
                :readonly="row.Wait_Stock_Count == 1"
                style="width: 50px; border: 1px solid #eee; border-radius: 4px"
                @blur="
                  (e) => {
                    inputBlur(e, row.S_Count, row);
                  }
                "
              />
            </div>
            <div v-else>{{ row.S_Count }}</div>
          </template>
          <template slot="PackageSn" slot-scope="{ row }">
            <div style="color: #298dff; cursor: pointer;" @click="handleDetail(row)">{{ row.PackageSn }}</div>
          </template>
          <!--          <template slot="AllWeight" slot-scope="{ row }">-->
          <!--            {{ row.S_Count * row.Netweight }}-->
          <!--          </template>-->
          <!--  <template slot="Unique_Code" slot-scope="{ row }">
            {{ row.C_Type === "打包件" ? row.Unique_Code : "-" }}
          </template>
          <template slot="op" slot-scope="{ row, index }">
            <el-button
              v-if="row.C_Type === '打包件'"
              :index="index"
              type="text"
              @click="handleInfo(row)"
              >查看</el-button
            >
          </template> -->
        </dynamic-data-table>
      </div>

      <el-dialog
        v-if="dialogVisible"
        v-dialogDrag
        class="plm-custom-dialog"
        :title="title"
        :visible.sync="dialogVisible"
        :width="width"
        :top="topDialog"
        @close="close"
      >
        <component
          :is="currentComponent"
          ref="content"
          :dialog-visible="dialogVisible"
          :project-id="projectId"
          :sys-project-id="form.ProjectId"
          :add-radio="addradio"
          :tab-type-code="tabTypeCode"
          :is-pack="Is_Pack"
          @addCarData="addCarData"
          @close="close"
          @reCount="getTotal"
          @refresh="fetchData"
          @selectList="addSelectList"
        />
      </el-dialog>
      <check-info ref="info" />
    </div>
  </div>
</template>

<script>
import TitleInfo from './TitleInfo.vue'
import AddDialog from './AddDialog.vue'
import packDetail from './packDetail.vue'
import {
  AddProjectSendingInfo,
  GetProjectsendinginEntity,
  EditProjectSendingInfo
} from '@/api/PRO/component-stock-out'
import { GetFirstLevelDepartsUnderFactory } from '@/api/PRO/material-warehouse/material-inventory-reconfig.js'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'
import CarDialog from './CarDialog.vue'
import { GetCurCarPageList } from '@/api/PRO/car'
import TopHeader from '@/components/TopHeader/index.vue'
import CheckInfo from '@/views/PRO/Component/GetPackingDetail/index.vue'
import { closeTagView, parseTime } from '@/utils'
import { GetProjectPageList, GetProjectEntity } from '@/api/PRO/pro-schedules'
import { GetGridByCode, GetOssUrl } from '@/api/sys'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { GetCompanyDepartTree } from '@/api/sys'
import numeral from 'numeral'
import OSSUpload from '@/views/PRO/components/ossupload.vue'
import { getFileNameFromUrl } from '@/utils/file'
import { mapGetters } from 'vuex'
import { GetContractList } from '@/api/plm/production'
import { GetStopList } from '@/api/PRO/production-task'
import SelectDepartmentUser from '@/components/Select/SelectDepartmentUser/index.vue'
import SelectDepartment from '@/components/Select/SelectDepartment/index.vue'

const TAB_TYPE = {
  com: 1,
  package: 2,
  unitPart: 3,
  part: 4
}
export default {
  components: {
    SelectDepartment, SelectDepartmentUser,
    OSSUpload,
    TitleInfo,
    AddDialog,
    TopHeader,
    DynamicDataTable,
    CarDialog,
    CheckInfo,
    packDetail
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageStatus: undefined,
      radio: 'pro_component_out_detail_list',
      addradio: 'pro_waiting_out_list',
      Is_Pack: false,
      isClicked: false,
      isSub: false, // 是否提交过
      width: '40%',
      topDialog: '1vh',
      btnLoading: false,
      currentComponent: '',
      title: '',
      loading: false,
      dialogVisible: false,
      sendNumber: '',
      form: {
        ProjectId: '',
        Out_Date: new Date(),
        Remarks: '',
        Contact_UserName: '',
        Mobile: '',
        License: '',
        Address: '',
        receiveName: '',
        issueName: '',
        Receiver_Tel: '',
        Area_Id: '',
        projectName: '',
        receiveNum: '',
        ProfessionalTypeName: '',
        Depart_Id: '',
        Logistics_Fee: undefined,
        Contract_Id: '',
        PageInfo: {
          ParameterJson: [],
          Page: 1,
          PageSize: 20
        },
        Loadings: '',
        LoadingsPersonnel: '',
        Trips: '',
        ReceivingUnit: ''
      },
      // 发货部门
      treeParamsDepart: {
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          disabled: 'disabled',
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      pickDepartmentList: [], // 品重发货部门
      plm_ProjectSendingInfo: {},
      produced_Components: [],
      weightFileInfo: [],
      projectSendingInfo_Item: [],
      Itemdetail: [],
      PackagesList: [],
      ProfessionalType: [],
      fileListArr: [],
      carOptions: [],
      projects: '',
      Id: '',
      projectId: '',
      planTime: '',
      showDialog: false,
      tbConfig: {
        Pager_Align: 'center'
      },
      columns: [],
      tbData: [],
      tbData2: [],
      tbData3: [],
      tbData4: [],
      total: 0,
      tbLoading: false,
      autoGenerate: true,
      selectList: [],
      fileListData: [],
      sums: [],
      rules: {
        Out_Date: [
          { required: true, message: '请选择发货时间', trigger: 'change' }
        ],
        receiveNum: [{ required: false, message: '请输入发货单号', trigger: 'blur' }],
        // receiveName: [{ required: true, message: '请输入', trigger: 'blur' }],
        // Receiver_Tel: [{ required: true, message: '请输入', trigger: 'blur' }],
        Depart_Id: [{ required: true, message: '请选择', trigger: 'change' }],
        issueName: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      old_Component_Ids: [],
      weightform: {
        Tare_Weight: 0, // 皮重
        Reason_Weight: 0, // 理重
        Pound_Weight: 0, // 磅重
        Net_Weight: 0, // 净重
        Weigh_Warning_Threshold: 0
      },
      isProductweight: null,
      tabTypeCode: 1,
      isDraft: false,
      contractOptions: []
    }
  },
  provide() {
    return {
      isVersionFour: this.isVersionFour
    }
  },
  computed: {
    netWeight() {
      if (!this.weightform.Pound_Weight || !this.weightform.Tare_Weight) return 0
      return this.weightform.Pound_Weight - this.weightform.Tare_Weight
    },
    showRed({ netWeight }) {
      return Math.abs(netWeight - this.weightform.Reason_Weight) >= this.weightform.Weigh_Warning_Threshold
    },
    ...mapGetters('tenant', ['isVersionFour'])
  },
  watch: {
    'tbData.length': {
      handler() {
        this.getTotal()
      }
    }

  },
  async created() {
    this.tabTypeCode = TAB_TYPE.com
    this.isSub = this.$route.query.isSub === '1' || false
    await this.getSettingProductweight()
    this.getFactoryDepartmentData()
    this.getDepartmentTree()
    this.getFactoryTypeOption()
    this.getAllCarList()
  },
  async mounted() {
    if (this.isEdit) {
      const {
        autoGenerate
      } = JSON.parse(decodeURIComponent(this.$route.query.p))
      this.autoGenerate = autoGenerate
      await this.getInfo()
    } else {
      const {
        Name,
        Id,
        Code,
        Address,
        autoGenerate,
        Sys_Project_Id
      } = JSON.parse(decodeURIComponent(this.$route.query.p))
      // this.projectName = Name;
      this.autoGenerate = autoGenerate
      this.projectId = Id
      this.Project_Code = Code
      this.form.projectName = Name
      this.form.Address = Address
      this.form.ProjectId = Sys_Project_Id
      this.form.issueName = this.$store.state.user.name
      this.getProjectEntity(this.projectId)
      this.rules.receiveNum[0].required = !autoGenerate
    }
    this.getContractList()
  },
  methods: {
    async getSettingProductweight() {
      const res = await GetPreferenceSettingValue({ code: 'Productweight' })
      if (res.Data === 'true') {
        this.isProductweight = true
      }
    },

    // 获取工厂数据
    getFactoryDepartmentData() {
      GetFirstLevelDepartsUnderFactory({ FactoryId: localStorage.getItem('CurReferenceId') }).then(res => {
        if (res.IsSucceed) {
          this.pickDepartmentList = res.Data
          const depId = localStorage.getItem('DepartmentId')
          const cur = this.pickDepartmentList.find((item) => item.Id === depId)
          if (cur) {
            this.form.Depart_Id = cur.Id
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },

    getDepartmentTree() {
      GetCompanyDepartTree({ isAll: true }).then((res) => {
        if (res.IsSucceed) {
          const tree = res.Data
          this.setDisabledTree(tree)
          this.treeParamsDepart.data = tree
          this.$nextTick(_ => {
            this.$refs.treeSelectDepart?.treeDataUpdateFun(tree)
            const arr = this.getFlattenedSelectableItems(tree)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getFlattenedSelectableItems(root) {
      if (!root || !root.length) return []
      const result = []
      const flatten = (items) => {
        if (!items || !items.length) return
        items.forEach((item) => {
          const { Children } = item
          if (item.Data?.Is_Company !== true && item.Data?.Type !== '1') {
            result.push(item)
          }
          if (Children && Children.length > 0) {
            flatten(Children)
          }
        })
      }
      flatten(root)
      const cur = result.find((item) => item.Id === localStorage.getItem('DepartmentId'))
      if (cur && !this.isEdit) {
        this.form.Depart_Id = cur.Id
      }
    },
    setDisabledTree(root) {
      if (!root) return
      root.forEach((element) => {
        const { Children } = element
        if (element.Data.Is_Company === true || element.Data.Type === '1') {
          element.disabled = true
        } else {
          element.disabled = false
        }
        if (Children.length > 0) {
          this.setDisabledTree(Children)
        }
        // if (Children && Children.length) {
        //   element.disabled = true
        // } else {
        //   element.disabled = false
        //   this.setDisabledTree(Children)
        // }
      })
    },
    departClear() {

    },
    departChange() {

    },
    getNum(a, b) {
      return numeral(a).subtract(b).format('0.[000]')
    },
    async getFactoryTypeOption() {
      await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProfessionalType = res.Data
          this.form.ProfessionalTypeName = this.ProfessionalType[0].Name
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      await this.getTableConfig(
        `pro_component_out_detail_list,${this.ProfessionalType[0].Code}`
      )
    },
    getProjectEntity(Id) {
      GetProjectEntity({ Id }).then((res) => {
        if (res.IsSucceed) {
          const Consignee = res.Data.Contacts.find((item) => {
            return item.Type == 'Consignee'
          })
          this.form.receiveName = Consignee?.Name || ''
          this.form.Receiver_Tel = Consignee?.Tel || ''
          this.form.Trips = res.Data.Trips || ''
        }
      })
    },
    getProjectPageList() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projects = res.Data.Data
        }
      })
    },
    toBack() {
      this.$confirm('此操作不会保存编辑数据，是否退出？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          closeTagView(this.$store, this.$route)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    radioChange(e) {
      if (e === 'pro_component_out_detail_list') {
        this.addradio = 'pro_waiting_out_list'
        this.Is_Pack = false
        this.tabTypeCode = TAB_TYPE.com
        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)
      } else if (e === 'pro_package_out_detail_list') {
        this.addradio = 'pro_waiting_out_list_package'
        this.Is_Pack = true
        this.tabTypeCode = TAB_TYPE.package
        this.getTableConfig(`${e},${this.ProfessionalType[0].Code}`)
      } else if (e === 'PROShipUnitPart') {
        this.addradio = 'PROShipAddUnitPart'
        this.tabTypeCode = TAB_TYPE.unitPart
        this.getTableConfig(`${e}`)
        this.Is_Pack = false
      } else if (e === 'PROShipPart') {
        this.addradio = 'PROShipAddPart'
        this.Is_Pack = false
        this.tabTypeCode = TAB_TYPE.part
        this.getTableConfig(`${e}`)
      }
    },
    inputBlur(e, e1, row) {
      console.log('blur', e1, row, row.Wait_Stock_Count)
      if (e1 < 1 || e1 > row.Wait_Stock_Count) {
        row.S_Count = row.Wait_Stock_Count
      } else {
        row.S_Count = Number(e1)
      }
      row.AllWeight = this.getAllWeight(row)
      this.Itemdetail.find((item) => {
        if (item.Component_Id == row.Id) {
          item.SteelAmount = row.S_Count
        }
      })
    },
    handleSubmit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return
        }
        this.isClicked = true

        const formAttachment = []
        if (this.fileListArr.length > 0) {
          this.fileListArr.forEach(item => {
            formAttachment.push(
              item.response && item.response.encryptionUrl
                ? item.response.encryptionUrl
                : item.encryptionUrl
            )
          })
        }

        const submitObj = {
          plm_ProjectSendingInfo: {
            // Id:this.Id,
            Code: this.form.receiveNum,
            Attachment: formAttachment.toString(),
            ProjectId: this.projectId,
            Consignee: this.form.receiveName,
            ConsigneeTel: this.form.Receiver_Tel,
            Depart_Id: this.form.Depart_Id,
            MakerName: this.form.issueName,
            VehicleNo: this.form.License,
            DriverName: this.form.Contact_UserName,
            Telephone: this.form.Mobile,
            SendDate: parseTime(this.form.Out_Date, '{y}-{m}-{d} {h}:{i}:{s}'),
            Remarks: this.form.Remarks,
            ProjectName: this.form.projectName,
            TypeId: this.ProfessionalType[0].Code,
            Address: this.form.Address,
            Logistics_Fee: this.form.Logistics_Fee,
            Contract_Id: this.form.Contract_Id,
            Loadings: this.form.Loadings,
            LoadingsPersonnel: this.form.LoadingsPersonnel,
            Trips: this.form.Trips,
            ReceivingUnit: this.form.ReceivingUnit,
          },
          projectSendingInfo_Item: [],
          PartList: []
        }
        // 只获取新的表格数据
        const tempData = [...this.tbData, ...this.tbData2, ...this.tbData3, ...this.tbData4]
        tempData.filter((item) => {
          if (item.PackageSn) {
            const {
              Id,
              Stock_Count,
              PackageSn,
              Warehouse_Id,
              Location_Id,
              Netweight,
              AllWeight,
              DIM,
              Volume,
              AllAmount,
              Import_Detail_Id,
              Model_Ids,
              isOld
            } = item
            const tempItem = {
              PackageSn: PackageSn || '',
              SteelAmount: Stock_Count || 1,
              Warehouse_Id: Warehouse_Id || '',
              Location_Id: Location_Id || '',
              SteelWeight: Netweight || '',
              Import_Detail_Id,
              Model_Ids,
              DIM,
              Volume,
              AllWeight,
              AllAmount,
              isOld
            }
            if (!isOld) {
              submitObj.projectSendingInfo_Item.push(tempItem)
            }
          } else if (item.Code) {
            const {
              Id,
              S_Count,
              Stock_Count,
              Warehouse_Id,
              Import_Detail_Id,
              Model_Ids,
              Location_Id,
              Netweight,
              AllWeight,
              isOld
            } = item
            const tempItem = {
              Component_Id: Id || '',
              SteelAmount: S_Count || '',
              Warehouse_Id: Warehouse_Id || '',
              Location_Id: Location_Id || '',
              SteelWeight: Netweight || '',
              Import_Detail_Id,
              Model_Ids,
              isOld,
              AllWeight
            }
            if (!isOld) {
              delete tempItem.isOld
              submitObj.projectSendingInfo_Item.push(tempItem)
            }
          } else if (item.Part_Code) {
            const {
              Part_Produced_Id,
              Part_Code,
              Area_Name,
              InstallUnit_Name,
              S_Count,
              Spec,
              Length,
              Weight,
              Part_Grade
            } = item
            const tempItem = {
              Part_Produced_Id: Part_Produced_Id,
              Part_Code,
              Area_Name,
              InstallUnit_Name,
              Amount: S_Count,
              Spec,
              Length,
              Weight,
              Part_Grade
            }
            submitObj.PartList.push(tempItem)
          }
        })

        this.btnLoading = true
        if (this.isEdit) {
          // 获取更新后的表单数据
          // submitObj.entity.Id = this.$route.query.id;
          this.plm_ProjectSendingInfo.Code = this.form.receiveNum
          this.plm_ProjectSendingInfo.Consignee = this.form.receiveName
          this.plm_ProjectSendingInfo.ConsigneeTel = this.form.Receiver_Tel
          this.plm_ProjectSendingInfo.VehicleNo = this.form.License
          this.plm_ProjectSendingInfo.DriverName = this.form.Contact_UserName
          this.plm_ProjectSendingInfo.Telephone = this.form.Mobile
          this.plm_ProjectSendingInfo.Depart_Id = this.form.Depart_Id
          this.plm_ProjectSendingInfo.MakerName = this.form.issueName
          this.plm_ProjectSendingInfo.Address = this.form.Address
          this.plm_ProjectSendingInfo.Contract_Id = this.form.Contract_Id
          this.plm_ProjectSendingInfo.Logistics_Fee = this.form.Logistics_Fee
          this.plm_ProjectSendingInfo.Loadings = this.form.Loadings
          this.plm_ProjectSendingInfo.LoadingsPersonnel = this.form.LoadingsPersonnel
          this.plm_ProjectSendingInfo.Trips = this.form.Trips
          this.plm_ProjectSendingInfo.ReceivingUnit = this.form.ReceivingUnit
          this.plm_ProjectSendingInfo.SendDate = parseTime(
            this.form.Out_Date,
            '{y}-{m}-{d} {h}:{i}:{s}'
          )
          this.plm_ProjectSendingInfo.Remarks = this.form.Remarks

          const formAttachment = []
          if (this.fileListArr.length > 0) {
            this.fileListArr.forEach(item => {
              formAttachment.push(
                item.response && item.response.encryptionUrl
                  ? item.response.encryptionUrl
                  : item.encryptionUrl
              )
            })
          }
          this.plm_ProjectSendingInfo.Attachment = formAttachment.toString()
          submitObj.plm_ProjectSendingInfo = this.plm_ProjectSendingInfo
          // 获取新的表格数据
          // submitObj.projectSendingInfo_Item =
          //   submitObj.projectSendingInfo_Item.filter((item) => {
          //     return !item.isOld;
          //   });
          // 添加新老表格数据
          submitObj.projectSendingInfo_Item = [
            ...this.Itemdetail,
            ...this.PackagesList,
            ...submitObj.projectSendingInfo_Item
          ]
          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {
            this.$message({
              message: '不能保存空发货单',
              type: 'error'
            })
          } else {
            this.loading = true
            EditProjectSendingInfo(submitObj).then((res) => {
              if (res.IsSucceed) {
                this.$message({
                  message: '编辑成功',
                  type: 'success'
                })
                closeTagView(this.$store, this.$route)
                this.$router.replace({
                  name: 'PROShipSent',
                  query: {
                    refresh: 1
                  }
                })
              } else {
                this.isClicked = false
                this.$message({
                  message: res.Message,
                  type: 'error'
                })
              }
              this.loading = false
              this.btnLoading = false
            })
          }
        } else {
          if (submitObj.projectSendingInfo_Item.length === 0 && submitObj.PartList.length === 0) {
            this.$message({
              message: '不能保存空发货单',
              type: 'error'
            })
          } else {
            console.log('submitObj', submitObj)
            this.loading = true
            AddProjectSendingInfo(submitObj).then((res) => {
              if (res.IsSucceed) {
                this.$message({
                  message: '添加成功',
                  type: 'success'
                })
                closeTagView(this.$store, this.$route)
                this.$router.replace({
                  name: 'PROShipSent',
                  query: {
                    refresh: 1
                  }
                })
              } else {
                this.isClicked = false
                this.$message({
                  message: res.Message,
                  type: 'error'
                })
              }
              this.btnLoading = false
              this.loading = false
            })
          }
        }
      })
    },
    async getStopList(list, key) {
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Type: this.tabTypeCode === TAB_TYPE.com ? 2 : this.tabTypeCode === TAB_TYPE.unitPart ? 3 : 1 // 1：零件，3：部件，2：构件
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },
    async getInfo() {
      await GetProjectsendinginEntity({
        Id: this.$route.query.id
      }).then(async(res) => {
        if (res.IsSucceed) {
          this.plm_ProjectSendingInfo = res.Data.Plm_ProjectSendingInfo
          this.Itemdetail = res.Data.Itemdetail
          this.PackagesList = res.Data.PackagesList
          this.PartList = res.Data.PartList
          this.weightform = res.Data.WeightInfo
          this.pageStatus = this.plm_ProjectSendingInfo.Status
          this.isDraft = this.plm_ProjectSendingInfo?.Status === 0

          const {
            Id,
            Code,
            ProjectId,
            ProjectName,
            Consignee,
            ConsigneeTel,
            Depart_Id,
            MakerName,
            VehicleNo,
            DriverName,
            Telephone,
            Address,
            Attachment,
            SendDate,
            Remarks,
            Number,
            Logistics_Fee,
            Contract_Id,
            Loadings,
            LoadingsPersonnel,
            Trips,
            ReceivingUnit,

          } = this.plm_ProjectSendingInfo

          this.form.ProjectId = ProjectId
          this.form.receiveNum = Code
          this.form.projectName = ProjectName
          this.form.receiveName = Consignee
          this.form.Receiver_Tel = ConsigneeTel
          this.form.Depart_Id = Depart_Id
          this.form.issueName = MakerName
          this.form.License = VehicleNo
          this.form.Contact_UserName = DriverName
          this.form.Mobile = Telephone
          this.form.Address = Address
          this.form.Out_Date = new Date(SendDate)
          this.form.Remarks = Remarks
          this.form.Logistics_Fee = Logistics_Fee || undefined
          this.form.Contract_Id = Contract_Id
          this.form.Loadings = Loadings
          this.form.LoadingsPersonnel = LoadingsPersonnel
          this.form.Trips = Trips
          this.form.ReceivingUnit = ReceivingUnit
          this.sendNumber = Number

          if (VehicleNo && this.carOptions.every((v) => v.License !== VehicleNo)) {
            this.carOptions.push({
              License: VehicleNo,
              Contact_UserName: DriverName,
              Mobile: Telephone,
              detail: VehicleNo ? `${VehicleNo}(${DriverName} ${Telephone})` : ''
            })
          }

          if (Attachment) {
            const AttachmentArr = Attachment.split(',')
            AttachmentArr.forEach(item => {
              const fileUrl =
                item.indexOf('?Expires=') > -1
                  ? item.substring(0, item.lastIndexOf('?Expires='))
                  : item
              const fileName = decodeURI(fileUrl.substring(fileUrl.lastIndexOf('/') + 1))
              const AttachmentJson = {}
              AttachmentJson.name = decodeURIComponent(fileName)
              AttachmentJson.url = fileUrl
              AttachmentJson.encryptionUrl = fileUrl
              this.fileListData.push(AttachmentJson)
              this.fileListArr.push(AttachmentJson)
            })
          }

          if (this.weightform.Attachment_Weight) {
            const imgPromiseAll2 = this.weightform.Attachment_Weight.split(',').map(async url => {
              const fileUrl = url.split('?')[0]
              return {
                url: await this.handleUrl(fileUrl),
                name: getFileNameFromUrl(fileUrl)
              }
            })
            this.weightFileInfo = await Promise.all(imgPromiseAll2)
          }

          this.Itemdetail.forEach((item, index) => {
            const {
              Component_Id,
              S_Count,
              SteelWeight,
              AllWeight,
              Name,
              Spec,
              Length,
              WarehouseName,
              Code,
              LocationName,
              Area_Name,
              Wait_Stock_Count,
              Import_Detail_Id,
              Location_Id,
              SerialNumber
            } = item
            const tempItem = {
              Id: Component_Id,
              Area_Name: Area_Name,
              Name,
              Spec,
              Length,
              WarehouseName,
              Code,
              LocationName,
              Import_Detail_Id,
              Location_Id,
              S_Count: S_Count,
              Wait_Stock_Count: Wait_Stock_Count,
              Netweight: SteelWeight,
              AllWeight: AllWeight,
              isOld: true,
              SerialNumber: SerialNumber
            }
            this.tbData.push(tempItem)
            this.old_Component_Ids.push(Component_Id)
            this.getStopList(this.tbData, 'Component_Id')
          })
          this.PartList.forEach((element, idx) => {
            const tempItem = { ...element }
            tempItem.S_Count = tempItem.Amount
            tempItem.Netweight = tempItem.Weight
            tempItem.AllWeight = this.getAllWeight(tempItem)
            tempItem.Total_Weight = numeral(tempItem.Stock_Count).multiply(tempItem.Weight).value()
            if (tempItem.Part_Grade > 0) {
              this.tbData3.push(tempItem)
              this.getStopList(this.tbData3, 'Part_Aggregate_Id')
              console.log('this.tbData3', this.tbData3)
            } else {
              this.tbData4.push(tempItem)
              this.getStopList(this.tbData4, 'Part_Aggregate_Id')
              console.log('this.tbData4', this.tbData4)
            }
          })

          this.PackagesList.forEach((item, index) => {
            const {
              PkgNO,
              PackageSn,
              AllWeight,
              Volume,
              AllAmount,
              WarehouseName,
              LocationName,
              DIM,
              PackageId
            } = item
            const tempItem = {
              PkgNO: PkgNO,
              PackageSn: PackageSn,
              AllWeight,
              AllAmount,
              Volume,
              WarehouseName,
              LocationName,
              isOld: true,
              DIM: DIM,
              PackageId
            }
            this.tbData2.push(tempItem)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    checkSelectable(row) {
      return !row.stopFlag
    },
    async handleUrl(url) {
      const { Data } = await GetOssUrl({ url })
      return Data
    },
    carChange(val) {
      if (!val) {
        this.form.Contact_UserName = ''
        this.form.Mobile = ''
        this.form.License = ''
        return
      }
      const item = this.carOptions.find((v) => v.License === this.form.License)
      this.form.Contact_UserName = item.Contact_UserName
      this.form.Mobile = item.Mobile
      this.form.License = item.License
    },
    projectIdChange(e) {
      // if (e) {
      //   this.getAreaList();
      // }
    },
    projectIdClear(e) {
      // this.$refs.form2.resetFields();
    },
    getAllWeight(item) {
      return Number(item.S_Count * item.Netweight).toFixed(2) / 1
    },
    addSelectList(list) {
      console.log(list, 'list')
      console.log(this.tabTypeCode)
      if (this.tabTypeCode === TAB_TYPE.com) {
        console.log(11)
        list.forEach((item) => {
          item.AllWeight = this.getAllWeight(item)

          this.tbData.push(item)
        })
        this.tbData = JSON.parse(JSON.stringify(this.tbData))
        this.total = this.tbData.length
      } else if (this.tabTypeCode === TAB_TYPE.unitPart) {
        list.forEach((item) => {
          item.AllWeight = this.getAllWeight(item)
          this.tbData3.push(item)
        })
        // dont ask why just cv
        this.tbData3 = JSON.parse(JSON.stringify(this.tbData3))
        this.total = this.tbData3.length
      } else if (this.tabTypeCode === TAB_TYPE.part) {
        list.forEach((item) => {
          item.AllWeight = this.getAllWeight(item)
          this.tbData4.push(item)
        })
        this.tbData4 = JSON.parse(JSON.stringify(this.tbData4))
        this.total = this.tbData4.length
      } else if (this.tabTypeCode === TAB_TYPE.package) {
        list.forEach((item) => {
          this.tbData2.push(item)
        })
        this.total = this.tbData2.length
      }
    },
    handleDelete() {
      this.$confirm('删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (this.Is_Pack) {
            this.selectList.forEach((item) => {
              const index = this.tbData2.findIndex(
                (v) => v.PackageSn === item.PackageSn
              )
              index !== -1 && this.tbData2.splice(index, 1)
              if (this.isEdit) {
                const index = this.PackagesList.findIndex(
                  (v) => v.PackageSn === item.PackageSn
                )
                index !== -1 && this.PackagesList.splice(index, 1)
              }
            })
          } else {
            let _table = null
            if (this.tabTypeCode === TAB_TYPE.com) {
              _table = this.tbData
              this.selectList.forEach((item) => {
                const index = _table.findIndex((v) => v.Import_Detail_Id + v.Location_Id === item.Import_Detail_Id + item.Location_Id)
                index !== -1 && _table.splice(index, 1)
                if (this.isEdit) {
                  const index = this.Itemdetail.findIndex(
                    (v) => v.Component_Id === item.Id
                  )
                  index !== -1 && this.Itemdetail.splice(index, 1)
                }
              })
            } else if (this.tabTypeCode === TAB_TYPE.unitPart) {
              _table = this.tbData3
              this.selectList.forEach((item) => {
                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)
                index !== -1 && _table.splice(index, 1)
              })
            } else if (this.tabTypeCode === TAB_TYPE.part) {
              _table = this.tbData4
              this.selectList.forEach((item) => {
                const index = _table.findIndex((v) => v.Part_Produced_Id === item.Part_Produced_Id)
                index !== -1 && _table.splice(index, 1)
              })
            }
          }
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {})
    },
    multiSelectedChange(v) {
      this.selectList = v
    },
    fetchData() {
      // GetStockOutDetailList({ stockOutId: this.$route.query.id }).then(res => {
      //   if (res.IsSucceed) {
      //     this.tbData = res.Data
      //     this.tbData.forEach((element, idx) => {
      //       this.$set(element, 'UniqueCodesArray', element.UniqueCodes.split(','))
      //     })
      //   } else {
      //     this.$message({
      //       message: res.Message,
      //       type: 'error'
      //     })
      //   }
      //   this.tbLoading = false
      // })
    },
    getTotal() {
      // this.$nextTick(_ => {
      //   const columns = this.$refs.dyTable.$refs.dtable.columns
      //   columns.forEach((element, idx) => {
      //     if (idx === 0) {
      //       this.sums[0] = '合计'
      //     } else if (element.property === 'Out_Count') {
      //       const v = this.tbData.reduce((acc, cur) => {
      //         return this.highPrecisionAdd(acc, cur[element.property])
      //       }, 0)
      //       this.$set(this.sums, idx, v)
      //     } else if (element.property === 'NetWeight') {
      //       const v = this.tbData.reduce((acc, cur) => {
      //         return this.highPrecisionAdd(acc, cur[element.property] * cur['Out_Count'])
      //       }, 0)
      //       this.$set(this.sums, idx, numeral(v).divide(1000).format('0.[00]') + '（t）')
      //     } else {
      //       this.$set(this.sums, idx, '')
      //     }
      //   })
      // })
    },
    getAllCarList() {
      return new Promise((resolve) => {
        GetCurCarPageList({}).then((res) => {
          if (res.IsSucceed) {
            this.carOptions = res.Data
            this.carOptions.forEach((element, idx) => {
              this.$set(
                element,
                'detail',
                `${element.License}(${element.Contact_UserName} ${element.Mobile})`
              )
            })
            resolve()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      })
    },
    addCarData(data) {
      this.getAllCarList()
      // this.carOptions.push(data);
    },
    handleAdd() {
      this.currentComponent = 'AddDialog'
      this.width = '80%'
      this.topDialog = '1vh'
      this.dialogVisible = true
      if (this.Is_Pack === false) {
        let _table = null
        if (this.tabTypeCode === TAB_TYPE.com) {
          _table = this.tbData
          this.title = '添加构件'
        } else if (this.tabTypeCode === TAB_TYPE.unitPart) {
          _table = this.tbData3
          this.title = '添加部件'
        } else if (this.tabTypeCode === TAB_TYPE.part) {
          _table = this.tbData4
          this.title = '添加零件'
        }

        const tempData1 = _table.filter((item) => {
          return !item.isOld
        })
        this.$nextTick((_) => {
          this.$refs.content.init(tempData1, _table)
        })
      } else if (this.Is_Pack === true) {
        this.title = '添加打包件'
        const tempData2 = this.tbData2.filter((item) => {
          return !item.isOld
        })
        this.$nextTick((_) => {
          this.$refs.content.init(tempData2, this.tbData2)
        })
      }
    },
    // 打包件详情
    handleDetail(row) {
      console.log(row, 'id===')
      this.currentComponent = 'packDetail'
      this.width = '60%'
      this.title = '打包件详情'
      this.topDialog = '10vh'
      this.dialogVisible = true
      this.$nextTick((_) => {
        this.$refs.content.init(row.PackageId || row.Id)
      })
    },
    handleEditCar() {
      this.currentComponent = 'CarDialog'
      this.title = '新增车辆'
      this.topDialog = '10vh'
      this.dialogVisible = true
    },
    close() {
      this.dialogVisible = false
    },
    handleInfo(row) {
      this.$refs.info.handleOpen(row)
    },
    getTableConfig(code) {
      return new Promise((resolve) => {
        GetGridByCode({
          code
        }).then((res) => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message({
                message: '表格配置不存在',
                type: 'error'
              })
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            this.columns = (
              Data.ColumnList.filter((v) => v.Is_Display) || []
            ).map((item) => {
              item.Is_Resizable = true
              item.Is_Sortable = true
              return item
            })
            this.form.PageInfo.PageSize = +Data.Grid.Row_Number
            if (this.isSub) {
              this.tbConfig.Is_Select = false
              this.tbConfig.Is_Row_Number = true
            }
            resolve(this.columns)
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    uploadSuccess(response, file, fileList) {
      console.log('fileList', fileList)
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    uploadRemove(file, fileList) {
      this.fileListArr = JSON.parse(JSON.stringify(fileList))
    },
    async handlePreview(file) {
      console.log('file', file)
      let encryptionUrl = ''
      if (file.response && file.response.encryptionUrl) {
        encryptionUrl = file.response.encryptionUrl
      } else {
        encryptionUrl = await GetOssUrl({ url: file.encryptionUrl })
        encryptionUrl = encryptionUrl.Data
      }
      window.open(encryptionUrl)
    },
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '附件数量不能超过10个'
      })
    },
    async getContractList() {
      try {
        const submitData = {
          ContractTypeCode: 3,
          ProjectIds: []
        }
        if (this.form.ProjectId) {
          submitData.ProjectIds.push(this.form.ProjectId)
        }
        const res = await GetContractList(submitData)
        if (res.IsSucceed) {
          this.contractOptions = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      } catch (error) {
        console.error('获取合同列表失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tb-status {
  background: #fae6bb;
  padding: 16px 20px;
  font-size: 1.2em;
  font-weight: bold;
  display: flex;

  * {
    margin-right: 12px;
  }
}

.el-form {
  margin: 16px 10px;
}

.title {
  margin-left: 10px;
}

.cs-red{
  color:red
}

.cs-tree-x {
  ::v-deep {
    .el-select {
      width: 100%;
    }
  }
}

.statistics-container {
  display: flex;
  .statistics-item {
    margin-right: 32px;
    span:first-child {
      display: inline-block;
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      color: #999999;
      // margin-right: 16px;
    }
    span:last-child {
      font-size: 16px;
      font-weight: 600;
      color: #00c361;
    }
  }
}
</style>

<template>
  <div style="min-height: 80vh; display: flex; flex-direction: column">
    <div>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col v-if="tabTypeCode !== typeType.package" :span="8">
            <el-form-item label="区域" prop="Area_Id">
              <el-tree-select
                ref="treeSelectArea"
                v-model="form.Area_Id"
                class="treeselect"
                :select-params="selectParams"
                :styles="styles"
                :tree-params="treeParamsArea"
                @searchFun="filterFun($event, 'treeSelectArea')"
                @node-click="areaChange"
                @select-clear="areaClear"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="tabTypeCode !== typeType.package" :span="8">
            <el-form-item label="批次" prop="InstallUnit_Id">
              <el-select
                v-model="form.InstallUnit_Id"
                :disabled="!form.Area_Id"
                placeholder="请选择"
                style="width: 100%"
                filterable
                clearable=""
              >
                <el-option
                  v-for="item in SetupPositionData"
                  :key="item.Id"
                  :label="item.Name"
                  :value="item.Id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="isPack" :span="8">
            <el-form-item label="包编号" prop="Code">
              <el-input v-model="form.Code" placeholder="请输入" clearable />
            </el-form-item>
          </el-col>
          <el-col v-if="tabTypeCode !== typeType.package" :span="8">
            <el-form-item :label="`${tabTypeCode === typeType.com ? '构件' : tabTypeCode === typeType.unitPart ? '部件':'零件'}名称`" prop="Code">
              <el-input v-model="form.Code" placeholder="请输入" clearable />
            </el-form-item>
          </el-col>
          <template v-if="tabTypeCode === typeType.package || tabTypeCode === typeType.com">
            <el-col :span="8">
              <el-form-item label="仓库" prop="Warehouse_Id">
                <el-select
                  v-model="form.Warehouse_Id"
                  placeholder="请选择"
                  style="width: 100%"
                  filterable
                  clearable=""
                  @change="wareChange"
                >
                  <el-option
                    v-for="item in warehouses"
                    :key="item.Id"
                    :label="item.Display_Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="仓库" prop="Warehouse_Id">
                <el-input
                  v-model="form.Warehouse_Id"
                  clearable=""
                  placeholder="请输入"
                />
              </el-form-item> -->
            </el-col>
            <el-col :span="8">
              <el-form-item label="库位" prop="Location_Id">
                <el-select
                  v-model="form.Location_Id"
                  :disabled="!form.Warehouse_Id"
                  placeholder="请选择"
                  style="width: 100%"
                  filterable
                  clearable=""
                >
                  <el-option
                    v-for="item in locations"
                    :key="item.Id"
                    :label="item.Display_Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="库位" prop="Location_Id">
                <el-input
                  v-model="form.Location_Id"
                  clearable=""
                  placeholder="请输入"
                />
              </el-form-item> -->
            </el-col>
          </template>

          <el-col v-if="tabTypeCode === typeType.com" :span="8">
            <el-form-item
              label="构件类型"
              prop="ComponentType"
            >
              <el-tree-select
                ref="treeSelectObjectType"
                v-model="form.ComponentType"
                style="width: 100%"
                class="cs-tree-x"
                :select-params="treeSelectParams"
                :tree-params="ObjectTypeList"
                value-key="Id"
                @removeTag="removeTagFn"
                @searchFun="_searchFun"
              />
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item>
              <el-button
                type="primary"
                @click="
                  () => {
                    form.PageInfo.Page = 1;
                    getPageList();
                  }
                "
              >查询</el-button>
              <el-button @click="resetForm('form')">重置</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="text-align:right">
            <el-form-item>
              <el-button type="primary" :loading="addLoading" :disabled="!selectList.length" @click="handleAdd">加入列表</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
      <dynamic-data-table
        ref="dyTable"
        class="cs-plm-dy-table"
        :columns="columns"
        :config="tbConfig"
        :data="tbData"
        :page="form.PageInfo.Page"
        :total="total"
        border
        stripe
        style="margin-left: 20px"
        @gridPageChange="handlePageChange"
        @gridSizeChange="handleSizeChange"
        @multiSelectedChange="multiSelectedChange"
      >
        <template slot="Code" slot-scope="{ row }">
          <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
          <span>{{ row.Code }}</span>
        </template>
        <template slot="Part_Code" slot-scope="{ row }">
          <el-tag v-if="row.stopFlag" style="margin-right: 8px;" type="danger">停</el-tag>
          <span>{{ row.Part_Code }}</span>
        </template>
      </dynamic-data-table>
    </div>
    <span class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :disabled="!selectList.length" @click="handleSubmit">添 加</el-button>
    </span>
    <check-info ref="info" />
  </div>
</template>

<script>
import getTbInfo from '@/mixins/PRO/get-table-info-pro2'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'
import {
  GetProduceCompentEntity,
  GetWarehouseListOfCurFactory, GetProducedPartToSendPageList,
  GetLocationList
} from '@/api/PRO/component-stock-out'
import CheckInfo from '@/views/PRO/Component/GetPackingDetail/index.vue'
import { GeAreaTrees } from '@/api/PRO/project'
import { GetInstallUnitPageList } from '@/api/PRO/install-unit'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { GetCompTypeTree } from '@/api/PRO/factorycheck'
import { GetStopList } from '@/api/PRO/production-task'

const TAB_TYPE = {
  com: 1,
  package: 2,
  unitPart: 3,
  part: 4
}

export default {
  components: {
    DynamicDataTable,
    CheckInfo
  },
  mixins: [getTbInfo],
  props: {
    projectId: {
      type: String,
      default: ''
    },
    sysProjectId: {
      type: String,
      default: ''
    },
    addRadio: {
      type: String,
      default: 'pro_waiting_out_list'
    },
    isPack: {
      type: Boolean,
      default: false
    },
    tabTypeCode: {
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      tbConfig: {
        Pager_Align: 'center',
        checkSelectable: this.checkSelectable
      },
      columns: [],
      tbData: [],
      total: 0,
      addLoading: false,
      tbLoading: false,
      form: {
        // Project_Id: "",
        Sys_Project_id: '',
        Area_Id: '',
        InstallUnit_Id: '',
        Code: '',
        Warehouse_Id: '',
        Location_Id: '',
        ComponentType: [],
        Is_Pack: false,
        Model_Ids: [],
        PageInfo: {
          ParameterJson: [],
          Page: 1,
          PageSize: 20
        }
      },
      selectParams: {
        clearable: true,
        placeholder: '请选择'
      },
      styles: { width: '100%' },
      // 区域数据
      treeParamsArea: {
        'check-strictly': true,
        'expand-on-click-node': false,
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      SetupPositionData: [],
      selectList: [],
      // parentArray: [],
      factoryOption: [],
      locationName: '',
      locationOption: [],
      cmptTypes_1: [],
      cmptTypes_2: [],
      installOption: [],
      ProfessionalType: [],
      warehouses: [], // 仓库
      locations: [], // 库位
      treeSelectParams: {
        placeholder: '请选择'
      },
      ObjectTypeList: {
        // 构件类型
        'check-strictly': true,
        'default-expand-all': true,
        filterable: true,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Data'
        }
      },
      typeType: TAB_TYPE
    }
  },
  created() {
    if (!this.isPack) {
      this.getAreaList()
    }
    this.getWarehouseListOfCurFactory()
    this.form.Is_Pack = this.isPack
    if (this.isPack) {
      this.form.Sys_Project_id = this.sysProjectId
    } else {
      this.form.Sys_Project_id = this.sysProjectId
    }
  },
  methods: {
    async init(tableData, tbData) {
      // this.form.Component_Ids = old_Component_Ids;
      console.log('init', tableData, tbData)
      this.form.Model_Ids = tableData.map((v) => v.Model_Ids).toString()
      this.form.Component_Ids = tableData.map((v) => v.Id).filter(v => !!v)
      // this.parentArray = tableData
      this.tbLoading = true
      this.originTbData = tbData
      // await this.getTableConfig(this.addRadio);
      await this.getFactoryTypeOption()
      this.fetchData()
    },
    async getFactoryTypeOption() {
      await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProfessionalType = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      if (this.addRadio === 'PROShipAddUnitPart' || this.addRadio === 'PROShipAddPart') {
        await this.getTableConfig(this.addRadio)
      } else {
        await this.getTableConfig(
          `${this.addRadio},${this.ProfessionalType[0].Code}`
        )
      }

      if (!this.isPack) {
        await this.getObjectTypeList(this.ProfessionalType[0].Code)
      }
    },
    fetchData() {
      // this.tbLoading = false
      const form = { ...this.form }
      delete form['ComponentType']
      form.ComponentType = this.form.ComponentType.join(',')
      const obj = {
        ...form
      }
      if (!this.isPack) {
        console.log('this.originTbData', this.originTbData)

        if (this.tabTypeCode === TAB_TYPE.com) {
          const list = this.originTbData.map(item => {
            return {
              Import_Detail_Id: item.Import_Detail_Id,
              Location_Id: item.Location_Id
            }
          })
          obj.SelectedList = list
        } else {
          let list = null

          list = this.originTbData.map(item => item.Part_Produced_Id)
          obj.Selected_List = list
          obj.Code_Like = obj.Code
        }
      }

      let api = ''
      if (this.tabTypeCode === TAB_TYPE.package || this.tabTypeCode === TAB_TYPE.com) {
        api = GetProduceCompentEntity
      } else {
        api = GetProducedPartToSendPageList
        if (this.tabTypeCode === TAB_TYPE.unitPart) {
          obj.Type = 3
        }
        if (this.tabTypeCode === TAB_TYPE.part) {
          obj.Type = 1
        }
      }
      api(obj).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map(v => {
            if (this.tabTypeCode === TAB_TYPE.unitPart || this.tabTypeCode === TAB_TYPE.part) {
              v.Netweight = v.Weight
            }
            return v
          })
          this.total = res.Data.TotalCount
          this.tbLoading = false
          if (this.tabTypeCode !== TAB_TYPE.package) {
            this.getStopList(this.tbData)
          }
          // this.toggleSelection(this.parentArray)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.addLoading = false
      })
    },
    async getStopList(list) {
      const key = this.tabTypeCode === TAB_TYPE.com ? 'Import_Detail_Id' :   'Part_Aggregate_Id'
      const submitObj = list.map(item => {
        return {
          Id: item[key],
          Type: this.tabTypeCode === TAB_TYPE.com ? 2 : this.tabTypeCode === TAB_TYPE.unitPart ? 3 : 1 // 1：零件，3：部件，2：构件
        }
      })
      await GetStopList(submitObj).then(res => {
        if (res.IsSucceed) {
          const stopMap = {}
          res.Data.forEach(item => {
            stopMap[item.Id] = !!item.Is_Stop
          })
          list.forEach(row => {
            if (stopMap[row[key]]) {
              this.$set(row, 'stopFlag', stopMap[row[key]])
            }
          })
        }
      })
    },
    checkSelectable(row) {
      return !row.stopFlag
    },
    // 构件类型下拉
    getObjectTypeList(code) {
      GetCompTypeTree({ professional: code }).then((res) => {
        if (res.IsSucceed) {
          this.ObjectTypeList.data = res.Data
          this.$nextTick((_) => {
            this.$refs?.treeSelectObjectType?.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            type: 'error',
            message: res.Message
          })
        }
      })
    },
    removeTagFn(ids, tag) {
      // console.log("ids", ids);
      // console.log("tag", tag);
    },
    // 树过滤
    _searchFun(value) {
      this.$refs.treeSelectObjectType.filterFun(value)
    },
    // 获取区域
    getAreaList() {
      GeAreaTrees({
        sysProjectId: this.sysProjectId
      }).then((res) => {
        if (res.IsSucceed) {
          this.treeParamsArea.data = res.Data
          this.$nextTick((_) => {
            this.$refs?.treeSelectArea?.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    filterFun(val, ref) {
      this.$refs[ref].filterFun(val)
    },
    areaChange(e) {
      this.form.InstallUnit_Id = ''
      this.getInstall()
    },
    // 清空区域
    areaClear() {
      this.form.Area_Id = ''
      this.form.InstallUnit_Id = ''
    },
    // 获取批次
    getInstall() {
      GetInstallUnitPageList({
        Area_Id: this.form.Area_Id,
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          if (res.IsSucceed) {
            this.SetupPositionData = res.Data.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    // installNameChange(v) {
    //   this.$refs.dyTable.searchedField["InstallUnit_Name"] = v;
    //   this.showSearchBtn();
    // },
    // 获取仓库
    getWarehouseListOfCurFactory() {
      GetWarehouseListOfCurFactory().then((res) => {
        if (res.IsSucceed) {
          this.warehouses = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    wareChange(e) {
      this.getLocationList()
    },
    // 获取库位
    getLocationList() {
      GetLocationList({
        Warehouse_Id: this.form.Warehouse_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.locations = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleSubmit() {
      this.$emit('selectList', this.selectList)
      this.$emit('close')
      this.$emit('reCount')
    },
    multiSelectedChange(v) {
      this.selectList = v
      this.selectList.forEach((ele) => {
        ele.S_Count = ele.Stock_Count
        ele.Area_Name = ele.Area_Name
        ele.Wait_Stock_Count = ele.Stock_Count
      })
    },
    handleInfo(row) {
      this.$refs.info.handleOpen(row)
    },
    handleOpen() {},
    handleClose() {
      this.$emit('close')
    },
    toggleSelection(rows) {
      // let vArray = rows.map((v) => v.UniqueCodesArray);
      // vArray = vArray.flat(Infinity);
      this.$nextTick((_) => {
        this.tbData.forEach((element) => {
          if (rows.find((i) => i.Id === element.Id)) {
            this.$refs.dyTable.$refs.dtable.toggleRowSelection(element)
          }
        })
      })
    },
    getPageList() {
      this.fetchData()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.fetchData()
    },
    handleAdd() {
      this.addLoading = true
      this.$emit('selectList', this.selectList)

      this.$nextTick(_ => {
        let list = null

        if (this.tabTypeCode === TAB_TYPE.package) {
          list = this.$parent.$parent.tbData2
          list = list.filter((item) => {
            return !item.isOld
          })
        } else if (this.tabTypeCode === TAB_TYPE.com) {
          list = this.$parent.$parent.tbData
          this.originTbData = list
        } else if (this.tabTypeCode === TAB_TYPE.unitPart) {
          list = this.$parent.$parent.tbData3
          this.originTbData = list
        } else {
          list = this.$parent.$parent.tbData4
          this.originTbData = list
        }
        this.form.Model_Ids = list.map((v) => v.Model_Ids).toString()
        this.form.Component_Ids = list.map((v) => v.Id).filter(v => !!v)

        this.fetchData()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  margin-top: 10px;
}
::v-deep .custom-pagination .checked-count {
  top: 10px;
}
::v-deep .pagination {
  margin-top: 10px;
  justify-content: right !important;
}
.dialog-footer {
  text-align: right;
  margin-top: 16px;
  //position: absolute;  fk
  //bottom: 12px;
  //right: 35px;
}
::v-deep .el-tree-select-input{
  width: 100%;
}
</style>

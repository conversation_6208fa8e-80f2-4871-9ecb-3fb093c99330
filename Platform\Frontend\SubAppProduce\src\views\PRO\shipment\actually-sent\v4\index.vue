<!--成品发货-->
<template>
  <div
    v-loading="pageLoading"
    class="abs100 cs-z-flex-pd16-wrap"
    style="display: flex; flex-direction: column"
  >
    <div
      class="cs-z-page-main-content"
      style="height: auto; margin-bottom: 16px"
    >
      <top-header style="height: 100px; line-height: normal">
        <template #left>
          <el-form
            ref="searchForm"
            :inline="true"
            :model="form"
            class="demo-form-inline form-search"
            style="height: 100px"
          >
            <el-form-item label="发货单号：" prop="Code">
              <el-input
                v-model="form.Code"
                clearable
                placeholder="请输入"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="发货单状态：" prop="Status">
              <el-select v-model="form.Status" clearable placeholder="请选择">
                <!--                <el-option label="未发货" :value="0" />
                <el-option label="已发货" :value="1" />
                <el-option label="部分验收" :value="2" />
                <el-option label="已验收" :value="3" />-->
                <!--                <el-option label="草稿" :value="0" />
                <el-option label="待过磅" :value="2" />
                <el-option label="已过磅" :value="3" />
                <el-option label="未验收" :value="4" />
                <el-option label="部分验收" :value="5" />
                <el-option label="已验收" :value="6" />-->

                <el-option v-for="(item,key) in statusInfo" :key="key" :label="item" :value="key" />

              </el-select>
            </el-form-item>
            <el-form-item label="项目名称" prop="ProjectId">
              <el-select
                v-model="form.ProjectId"
                class="w100"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="item in projects"
                  :key="item.Id"
                  :label="item.Short_Name"
                  :value="item.Sys_Project_Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="收货人：" prop="Consignee">
              <el-input
                v-model="form.Consignee"
                clearable
                placeholder="请输入"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="车牌号：" prop="VehicleNo">
              <el-input
                v-model="form.VehicleNo"
                clearable
                placeholder="请输入"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="是否退货" prop="IsReturn">
              <el-select v-model="form.IsReturn" clearable placeholder="请选择">
                <el-option label="无退货" :value="false" />
                <el-option label="有退货" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item label="过磅预警" prop="Is_Weight_Warning">
              <el-select v-model="form.Is_Weight_Warning" clearable placeholder="请选择">
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="
                  () => {
                    form.PageInfo.Page = 1;
                    getPageList();
                  }
                "
              >查询</el-button>
              <el-button @click="resetForm('searchForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </template>
      </top-header>
    </div>
    <div class="cs-z-page-main-content" style="flex: 1; display: -webkit-box">
      <div style="color: rgba(34, 40, 52, 0.65); padding: 10px 0px">
        <el-button type="primary" @click="handleAdd">新增发货单</el-button>
        <el-button
          type="primary"
          :disabled="!selectList.length"
          @click="handleExport"
        >导出发货单(pdf)</el-button>
        <el-button
          type="primary"
          :disabled="selectEmpty"
          @click="handleExportExcel"
        >导出发货单(excel)</el-button>
        <ExportCustomReport code="Shipping_single_template" style="margin:0 10px" name="自定义发货单(excel)" :ids="selectList.map(i=>i.Id)"></ExportCustomReport>
        <el-button
          type="success"
          :disabled="!selectList.length"
          @click="handlePrint"
        >打印</el-button>
        <!--        <el-button-->
        <!--          type="success"-->
        <!--          @click="handlePrintNoWeight"-->
        <!--          :disabled="!selectRow"-->
        <!--          >预览(无重量)-->
        <!--        </el-button>-->
        <el-button
          type="success"
          :loading="btnLoading"
          :disabled="tbData.length === 0"
          @click="handleLeadingOut"
        >导出</el-button>
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="form.dateRange"
            style="width: 100%"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="datePickerwrapper"
          />
        </div>
        <div v-if="ProfessionalType" class="total-wrapper">
          <span
            style="margin: 0 24px 0 12px"
          >总数：{{ totalData.Allsteelamount }}</span><span>总重：{{ totalData.Allsteelweight }}（{{
            ProfessionalType[0].Unit
          }}）</span>
        </div>
      </div>
      <div
        v-loading="tbLoading"
        class="fff cs-z-tb-wrapper"
        style="flex: 1 1 auto"
      >
        <dynamic-data-table
          ref="dyTable"
          class="cs-plm-dy-table"
          :columns="comList"
          :config="tbConfig"
          :data="tbData"
          :page="queryInfo.Page"
          :total="total"
          border
          stripe
          @gridPageChange="handlePageChange"
          @gridSizeChange="handleSizeChange"
          @select="selectChange"
          @multiSelectedChange="handleSelectionChange"
          @selectAll="handleSelectAll"
        >
          <template slot="SumNetWeight" slot-scope="{ row }">
            {{ row.SumNetWeight | displayValue }}
          </template>
          <template slot="Status" slot-scope="{ row }">
            {{ statusInfo[row.Status] }}
          </template>
          <template slot="Number" slot-scope="{ row }">
            <div>{{ row.Number || '-' }}</div>
          </template>
          <template slot="SumReturnCount" slot-scope="{ row }">
            <span>{{ row.SumReturnCount != null ? row.SumReturnCount : '-' }}</span>
          </template>
          <template slot="SumAcceptCount" slot-scope="{ row }">
            <span>{{ row.SumAcceptCount != null ? row.SumAcceptCount : '-' }}</span>
          </template>
          <template slot="SumPendingCount" slot-scope="{ row }">
            <span>{{ row.SumPendingCount != null ? row.SumPendingCount : '-' }}</span>
          </template>
          <template slot="SendDate" slot-scope="{ row }">
            <div>
              {{ row.SendDate || "—" }}
            </div>
          </template>
          <template slot="op" slot-scope="{ row, index }">
            <template v-if="row.Status == '0'">
              <el-button
                :index="index"
                type="text"
                @click="handleEdit(row.Id, false)"
              >编辑</el-button>
              <el-button
                v-if="Shipping_Weigh_Enabled"
                :index="index"
                type="text"
                @click="handSubmit(row)"
              >提交过磅</el-button>
              <template v-else>
                <el-button v-if="getAuditStatus(row)" type="text" @click="submitForReview(row)">提交审核</el-button>
                <el-button
                  v-else
                  :index="index"
                  type="text"
                  @click="handleSub(row)"
                >提交发货</el-button>
              </template>
              <el-button
                :index="index"
                type="text"
                style="color:red"
                @click="handleDel(row.Id)"
              >删除</el-button>
            </template>
            <template v-else>
              <el-button
                v-if="row.Status == '2'"
                :index="index"
                type="text"
                @click="handleWithdraw(row.Id)"
              >撤回草稿</el-button>
              <el-button
                v-if="row.Status != '999'"
                :index="index"
                type="text"
                @click="handleEdit(row.Id, row.Status!='-1')"
              >编辑</el-button>
              <el-button
                :index="index"
                type="text"
                @click="handleInfo(row.Id)"
              >查看</el-button>
              <el-button
                v-if="[4,5,6].includes(+row.Status)"
                :index="index"
                type="text"
                @click="handleChange(row.Id)"
              >变更记录</el-button>
              <el-button
                v-if="Is_Integration&&[4,5,6].includes(+row.Status)"
                :index="index"
                type="text"
                @click="handelView(row.Id)"
              >验收情况</el-button>
            </template>
            <el-button v-if="row.Status==999" type="text" @click="handleCancelFlow(row.FlowId)">撤回</el-button>
            <template v-if="row.Status==3">
              <el-button
                :index="index"
                type="text"
                @click="handleWithdraw(row.Id)"
              >撤回草稿</el-button>
              <el-button v-if="getAuditStatus(row)" type="text" @click="submitForReview(row)">提交审核</el-button>
              <el-button
                v-else
                :index="index"
                type="text"
                @click="handleSub(row)"
              >提交发货</el-button>
            </template>
            <el-button v-if="row.FlowId" type="text" @click="handleMonitor(row.FlowId)">监控</el-button>
            <template v-if="row.Status==-1">
              <el-button v-if="Shipping_Weigh_Enabled" type="text" @click="handSubmit(row)">提交过磅</el-button>
              <template v-else>
                <el-button v-if="getAuditStatus(row)" type="text" @click="submitForReview(row)">提交审核</el-button>
                <el-button
                  v-else
                  :index="index"
                  type="text"
                  @click="handleSub(row)"
                >提交发货</el-button>
              </template>
            </template>
          </template>
          <!-- <template slot="Out_Date" slot-scope="{ row }">
            {{ row.Out_Date | timeFormat }}
          </template> -->
        </dynamic-data-table>
      </div>
      <el-dialog
        v-dialogDrag
        title="新增发货单"
        class="plm-custom-dialog"
        :visible.sync="dialogVisible"
        width="30%"
        @close="handleClose"
      >
        <el-form
          ref="form2"
          :model="form2"
          :rules="rules"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-form-item label="项目" prop="ProjectId">
            <el-select
              v-model="form2.ProjectId"
              class="w100"
              placeholder="请选择"
              filterable
              clearable
              @change="projectIdChange"
              @clear="projectIdClear"
            >
              <el-option
                v-for="item in projects"
                :key="item.Id"
                :label="item.Short_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="text-align: right">
            <el-button @click="resetForm2('form2')">取 消</el-button>
            <el-button
              type="primary"
              @click="submitForm2('form2')"
            >确 定</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <PrintDialog ref="PrintDialog" />
    <radioDialog
      ref="radioDialog"
      :send-id="selectRow.Id"
      :title="title"
      :send-data="selectRow"
    />
    <dialogExcel
      ref="dialogExcel"
      :send-id="selectRow.Id"
      :title="title"
      :send-data="selectRow"
    />
    <checkDialog ref="checkDialog" />
    <Monitor ref="monitor" />
  </div>
</template>

<script>
import Monitor from '@/components/Monitor/index.vue'
import TopHeader from '@/components/TopHeader/index.vue'
import addRouterPage from '@/mixins/add-router-page'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable.vue'
import getTbInfo from '@/mixins/PRO/get-table-info-pro2'
import {
  GetProjectSendingInfoPagelist,
  DeleteProjectSendingInfo,
  SubmitProjectSending,
  GetProjectSendingAllCount,
  TransformsWithoutWeight,
  SubmitWeighingForPC,
  WithdrawDraft,
  ExportInvoiceList, SubmitApproval, CancelFlow
} from '@/api/PRO/component-stock-out'
import { GetProjectPageList } from '@/api/PRO/pro-schedules'
import { GeAreaTrees } from '@/api/PRO/project'
import { GetInstallUnitPageList } from '@/api/PRO/install-unit'
import { parseTime } from '@/utils'
import { baseUrl } from '@/utils/baseurl'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import PrintDialog from './component/printDialog.vue'
import radioDialog from './component/dialog.vue'
import dialogExcel from './component/dialogExcel.vue'
import checkDialog from './component/check.vue'
import { mapGetters } from 'vuex'
import ExportCustomReport from "@/components/ExportCustomReport/index.vue";

const StatusMap = {
  0: '草稿',
  2: '待过磅',
  3: '已过磅',
  4: '未验收',
  5: '部分验收',
  6: '已验收',
  7: '已退货',
  999: '审批中',
  '-1': '已退回'
}

export default {
  components: {
    ExportCustomReport,
    TopHeader,
    Monitor,
    DynamicDataTable,
    PrintDialog,
    radioDialog,
    checkDialog,
    dialogExcel
  },
  filters: {
    sendDateFilter(e) {
      // console.log(e,"eeee");
      return parseTime(new Date(e))
    }
  },
  mixins: [addRouterPage, getTbInfo],
  data() {
    return {
      selectList: [],
      statusInfo: StatusMap,
      IsVisabel: false,
      btnLoading: false,
      form: {
        Code: '',
        Status: null,
        ProjectId: '',
        VehicleNo: '',
        Consignee: '',
        IsReturn: null,
        Is_Weight_Warning: null,
        dateRange: ['', ''],
        PageInfo: {
          ParameterJson: [],
          Page: 1,
          PageSize: 20
        }
      },
      form2: {
        ProjectId: '',
        Area_Id: '',
        InstallUnit_Id: ''
      },
      rules: {
        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]
        // Area_Id: [{ required: true, message: "请选择", trigger: "change" }],
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      selectParams: {
        clearable: true,
        placeholder: '请选择'
      },
      ProjectId: '',
      dialogVisible: false,
      pageLoading: false,
      addPageArray: [
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('@/views/PRO/shipment/actually-sent/v4/add.vue'),
          name: 'PROShipSentAdd',
          meta: { title: '新建发货单' }
        },
        {
          path: this.$route.path + '/edit',
          hidden: true,
          component: () => import('@/views/PRO/shipment/actually-sent/v4/edit.vue'),
          name: 'PROShipSentEdit',
          meta: { title: '编辑发货单' }
        },
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () => import('@/views/PRO/shipment/actually-sent/v4/detail.vue'),
          name: 'PROShipSentDetail',
          meta: { title: '详情' }
        },
        {
          path: this.$route.path + '/changeRecord',
          hidden: true,
          component: () => import('@/views/PRO/shipment/actually-sent/v4/changeRecord.vue'),
          name: 'PROShipSentChangeRecord',
          meta: { title: '发货单变更记录' }
        }
      ],
      projects: [],
      // 区域数据
      treeParamsArea: {
        'check-strictly': true,
        'expand-on-click-node': false,
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      styles: { width: '100%' },
      SetupPositionData: [],
      queryInfo: {
        Page: 1,
        PageSize: 10,
        ParameterJson: []
      },
      queryInfo2: {
        BeginDate: '',
        EndDate: '',
        PageInfo: {
          ParameterJson: [],
          Page: 1,
          PageSize: 2
        }
      },
      tbConfig: {
        Pager_Align: 'center',
        Op_Width: 280
      },
      columns: [],
      tbData: [],
      total: 0,
      tbLoading: false,
      selectRow: {},
      totalData: {
        Allsteelamount: 0,
        Allsteelweight: 0
      },
      ProfessionalType: null,
      title: '',
      Is_Integration: false // 是否一体化
    }
  },
  computed: {
    selectEmpty() {
      return Object.keys(this.selectRow).length === 0
    },
    comList() {
      if (!this.Is_Integration) {
        return this.columns.filter((item) => {
          return (
            item.Code !== 'SumAcceptCount' && item.Code !== 'SumPendingCount'
          )
        })
      } else {
        return this.columns
      }
    },
    ...mapGetters('factoryInfo', ['Component_Shipping_Approval', 'autoGenerate', 'Shipping_Approval_LowerLimit', 'Shipping_Weigh_Enabled'])
  },
  async activated() {
    console.log('activated')
    if (this.$route.query.refresh) {
      this.fetchData()
    }
    // this.fetchData()
  },
  async created() {
    this.$store.dispatch('factoryInfo/getWorkshop')

    this.Is_Integration = await this.$store.dispatch('user/getPreferenceSetting', 'Is_Integration')
    this.getFactoryTypeOption()
    this.getProjectPageList()
  },
  mounted() {
    console.log('mounted')
  },
  methods: {
    handleCancelFlow(instanceId) {
      this.$confirm('是否撤回?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        CancelFlow({
          instanceId
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    submitForReview(row) {
      const Id = row.Id
      if (!row.VehicleNo) {
        this.$message({
          message: '发货单车辆信息未完善，请完善后提交',
          type: 'warning'
        })
        return
      }

      this.$confirm('是否提交审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        SubmitApproval({
          Id
        }).then(res => {
          if (res.IsSucceed) {
            this.fetchData()
            this.$message({
              message: '操作成功',
              type: 'success'
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    handleMonitor(rowId) {
      this.$refs['monitor'].opendialog(rowId, false)
    },
    getAuditStatus(row) {
      return this.Component_Shipping_Approval && (this.Shipping_Approval_LowerLimit || 0) * 1000 < row.Project_Sending_Weight
    },
    async getFactoryTypeOption() {
      await GetFactoryProfessionalByCode({
        factoryId: localStorage.getItem('CurReferenceId')
      }).then((res) => {
        if (res.IsSucceed) {
          this.ProfessionalType = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      await this.getTableConfig(
        `pro_component_out_bill_list,${this.ProfessionalType[0].Code}`
      )
      this.fetchData()
    },
    fetchData() {
      this.tbLoading = true
      const form = { ...this.form }
      delete form['dateRange']
      this.form.dateRange = this.form.dateRange || []
      form.BeginDate = parseTime(this.form.dateRange[0])
        ? parseTime(this.form.dateRange[0])
        : ''
      form.EndDate = parseTime(this.form.dateRange[1])
        ? parseTime(this.form.dateRange[1])
        : ''
      GetProjectSendingInfoPagelist(form).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map((v) => {
            v.SendDate = v.SendDate
              ? parseTime(new Date(v.SendDate), '{y}-{m}-{d}')
              : v.SendDate
            return v
          })
          // this.tbData = res.Data.Data;
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
      GetProjectSendingAllCount({ ...form }).then((res) => {
        if (res.IsSucceed) {
          // console.log(res.Data,"res.Data");
          this.totalData = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getPageList() {
      this.fetchData()
      console.log(this.form, 'this.form')
    },
    handSubmit(row) {
      const Id = row.Id
      if (!row.VehicleNo) {
        this.$message({
          message: '发货单车辆信息未完善，请完善后提交',
          type: 'warning'
        })
        return
      }
      this.$confirm('是否提交过磅?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        SubmitWeighingForPC({
          Id
        }).then(res => {
          if (res.IsSucceed) {
            this.fetchData()
            this.$message({
              message: res.Data,
              type: 'success'
            })
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    datePickerwrapper() {
      // console.log(form,"form1111111111");
      // console.log(new Date("2022-09-28T10:12:35.583Z"),"new Date111");
      // console.log(parseTime(new Date("2022-10-11T14:03:54")),"new Date222");
      if (!this.form.dateRange) {
        this.form.dateRange = ['', '']
      }
      this.fetchData()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.fetchData()
    },
    submitForm2(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { ProjectId, Area_Id, InstallUnit_Id } = this.form2
          const {
            Name,
            Id,
            Code,
            SPIC_UserName,
            Address,
            Receiver,
            Receiver_Tel,
            Sys_Project_Id,
            Receive_UserName
          } = this.projects.find((v) => v.Id === this.form2.ProjectId)
          const data = {
            ProjectId,
            Area_Id,
            InstallUnit_Id,
            Id,
            Name,
            Code,
            Address,
            Receiver,
            Receiver_Tel,
            Sys_Project_Id,
            Receive_UserName,
            autoGenerate: this.autoGenerate,
            ProfessionalType: this.ProfessionalType
          }
          this.$router.push({
            name: 'PROShipSentAdd',
            query: {
              pg_redirect: 'PROShipSent',
              p: encodeURIComponent(JSON.stringify(data))
            }
          })
          this.dialogVisible = false
          this.$refs.form2.resetFields()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm2(formName) {
      this.dialogVisible = false
      this.$refs[formName].resetFields()
    },
    handleClose() {
      this.$refs.form2.resetFields()
      this.dialogVisible = false
    },
    handleAdd() {
      this.dialogVisible = true
      // this.$router.push({ name: 'PROShipSentAdd', query: { pg_redirect: 'PROShipSent' }})
    },
    getProjectPageList() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projects = res.Data.Data
        }
      })
    },
    projectIdChange(e) {
      console.log(e, 'e')
      // if (e) {
      //   this.getAreaList();
      // }
    },
    projectIdClear(e) {
      this.$refs.form2.resetFields()
    },
    // 获取区域
    getAreaList() {
      GeAreaTrees({
        projectId: this.form2.ProjectId
      }).then((res) => {
        if (res.IsSucceed) {
          this.treeParamsArea.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    filterFun(val, ref) {
      this.$refs[ref].filterFun(val)
    },
    areaChange(e) {
      console.log(e, 'e')
      this.getInstall()
    },
    // 清空区域
    areaClear() {
      this.form2.Area_Id = ''
      this.form.InstallUnit_Id = ''
    },
    // 获取批次
    getInstall() {
      GetInstallUnitPageList({
        Area_Id: this.form2.Area_Id,
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          if (res.IsSucceed) {
            this.SetupPositionData = res.Data.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleEdit(id, isSub) {
      this.$router.push({
        name: 'PROShipSentEdit',
        query: { pg_redirect: 'PROShipSent', id, isSub: isSub ? '1' : '0',
          p: encodeURIComponent(JSON.stringify({ autoGenerate: this.autoGenerate }))
        }
      })
    },
    // 撤回至草稿
    handleWithdraw(id) {
      this.$confirm('撤回至草稿, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          WithdrawDraft({
            id: id
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '撤销成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => { })
    },
    handleSub(row) {
      const id = row.Id
      if (!row.VehicleNo) {
        this.$message({
          message: '发货单车辆信息未完善，请完善后提交',
          type: 'warning'
        })
        return
      }
      this.$confirm('提交该发货单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.pageLoading = true
          SubmitProjectSending({
            Id: id
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '发货成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
            this.pageLoading = false
          })
        })
        .catch(() => {
          this.pageLoading = false
        })
    },
    handleDel(id) {
      this.$confirm('是否删除该发货单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteProjectSendingInfo({
          Id: id
        }).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleInfo(id) {
      this.$router.push({
        name: 'PROShipSentDetail',
        query: { pg_redirect: 'PROShipSent', id }
      })
    },
    handleChange(id) {
      this.$router.push({
        name: 'PROShipSentChangeRecord',
        query: { pg_redirect: 'PROShipSent', id }
      })
    },
    // printMe() {},
    handleExport() {
      this.title = '导出'
      this.$nextTick(_ => {
        this.$refs.radioDialog.handleOpen(this.selectList)
      })
    },
    handleExportExcel() {
      this.title = '导出'
      this.$refs.dialogExcel.handleOpen(this.selectList)
    },
    handlePrint() {
      this.title = '打印'
      this.$refs.radioDialog.handleOpen(this.selectList)
    },
    handlePrintNoWeight() {
      // console.log(this.selectRow.Code, "this.selectRow.Code");
      // console.log(this.selectRow.Id, "this.selectRow.Id");
      TransformsWithoutWeight({
        sendId: this.selectRow.Id
      }).then((res) => {
        if (res.IsSucceed) {
          // const templateUrl = combineURL(this.$baseUrl, res.Data);
          // window.open(templateUrl, "_blank");
          const url = new URL(res.Data, baseUrl())
          window.open(url.href, '_blank')
          this.$message({
            type: 'success',
            message: '打印成功!'
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      // this.$refs.PrintDialog.open(this.selectRow.Code)
    },

    // 导出
    handleLeadingOut() {
      this.btnLoading = true
      const form = { ...this.form }
      delete form['dateRange']
      delete form['PageInfo']
      this.form.dateRange = this.form.dateRange || []
      form.BeginDate = parseTime(this.form.dateRange[0])
        ? parseTime(this.form.dateRange[0])
        : ''
      form.EndDate = parseTime(this.form.dateRange[1])
        ? parseTime(this.form.dateRange[1])
        : ''
      ExportInvoiceList({ ...form })
        .then(res => {
          if (res.IsSucceed) {
            this.$message.success('导出成功')
            const url = new URL(res.Data, baseUrl())
            window.open(url.href)
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
        .finally(() => {
          // 结束loading
          this.btnLoading = false
        })
    },
    handleSelectionChange(list) {
      this.selectList = list
    },
    selectChange({ selection, row }) {
      this.$refs.dyTable.$refs.dtable.clearSelection()
      if (selection.length != 0) {
        this.selectRow = row
      } else {
        this.selectRow = {}
      }
      if (selection.length > 1) {
        selection.shift()
      }
      // console.log(selection, "selection2");
      this.$refs.dyTable.$refs.dtable.toggleRowSelection(
        row,
        !!selection.length
      )
    },
    handleSelectAll() {
      // this.$refs.dyTable.$refs.dtable.clearSelection()
    },
    handelView(Id) {
      this.IsVisabel = true
      this.$nextTick((_) => {
        this.$refs.checkDialog.handelOpen(Id)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.total-wrapper {
  float: right;
  color: #298dff;
  background-color: #f5faff;
  font-size: 14px;
  padding: 6px 10px 6px 10px;
  margin-right: 10px;
}
.date-picker-wrapper {
  float: right;
  width: 20%;
}
::v-deep .form-search {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 24%;
    display: flex;
  }
  .el-form-item__label {
    width: 100px;
  }
  .el-form-item__content {
    min-width: 10px;
    flex: 1;
  }
  .el-select {
    width: 100%;
  }
}
.license-box {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 28px;
  background: #818fb7;
  border-radius: 4px;
  overflow: hidden;

  .inner-box {
    width: 98px;
    height: 24px;
    border: 1px solid #ffffff;
    border-radius: 3px;
    color: #ffffff;
    font-size: 14px;
  }
}
::v-deep .custom-pagination .checked-count {
  top: 20px;
}
::v-deep .pagination {
  justify-content: right !important;
}
</style>

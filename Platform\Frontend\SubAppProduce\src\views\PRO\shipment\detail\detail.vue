<template>
  <div class="abs100 cs-z-flex-pd16-wrap" style="display:flex; flex-direction:column">
    <div
      class="cs-z-page-main-content"
      style="height: auto; margin-bottom: 16px;padding: 16px 16px 0px 16px;"
    >
      <el-form
        ref="searchForm"
        :model="form"
        inline
        label-width="70px"
      >
        <el-row>
          <el-form-item label-width="90px" label="项目名称：" prop="Project_Id">
            <el-select
              v-model="form.Project_Id"
              class="w100"
              placeholder="请选择"
              filterble
              clearable
              @change="projectIdChange"
              @clear="projectIdClear"
            >
              <el-option
                v-for="item in projects"
                :key="item.Id"
                :label="item.Short_Name"
                :value="item.Sys_Project_Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="区域：" prop="Area_Id">
            <el-tree-select
              ref="treeSelectArea"
              v-model="form.Area_Id"
              class="treeselect"
              :disabled="!form.Project_Id"
              :select-params="selectParams"
              :styles="styles"
              :tree-params="treeParamsArea"
              @searchFun="filterFun($event, 'treeSelectArea')"
              @node-click="areaChange"
              @select-clear="areaClear"
            />
          </el-form-item>
          <el-form-item label="批次：" prop="InstallUnit_Id">
            <el-select
              v-model="form.InstallUnit_Id"
              :disabled="!form.Area_Id"
              class="w100"
              placeholder="请选择"
              filterable
              clearable=""
            >
              <el-option
                v-for="item in SetupPositionData"
                :key="item.Id"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="仓库" prop="Warehouse_Id">
            <el-select
              v-model="form.Warehouse_Id"
              placeholder="请选择"
              style="width: 100%"
              filterable
              clearable=""
              @change="wareChange"
            >
              <el-option
                v-for="item in warehouses"
                :key="item.Id"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="库位" prop="Location_Id">
            <el-select
              v-model="form.Location_Id"
              :disabled="!form.Warehouse_Id"
              placeholder="请选择"
              style="width: 100%"
              filterable
              clearable=""
            >
              <el-option
                v-for="item in locations"
                :key="item.Id"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              style="margin-left:46px"
              @click="
                () => {
                  form.PageInfo.Page = 1;
                  getPageList();
                }
              "
            >查询</el-button>
            <el-button @click="resetForm('searchForm')">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div class="cs-z-page-main-content" style="flex:1">
      <div class="cs-container">
        <div style="color: rgba(34, 40, 52, 0.65); padding: 10px 0px">
          <el-button type="success" @click="handleExport">导出</el-button>
          <div class="date-picker-wrapper">
            <el-date-picker
              v-model="form.dateRange"
              style="width: 100%"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              @change="datePickerwrapper"
            />
          </div>
          <div class="total-wrapper">
            <!--            <el-button type="primary" @click="exportTb">导出</el-button>-->
            <span
              style="margin: 0 24px 0 12px"
            >发货总数：{{ totalData.Allsteelamount }}（件）</span><span>发货总量：{{ totalData.Allsteelweight }}（t）</span>
          </div>
        </div>
        <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
          <dynamic-data-table
            ref="dyTable"
            class="cs-plm-dy-table"
            :columns="columns"
            :config="tbConfig"
            :data="tbData"
            :page="form.PageInfo.Page"
            :total="total"
            border
            stripe
            @gridPageChange="handlePageChange"
            @gridSizeChange="handleSizeChange"
          >
            <template slot="SendDate" slot-scope="{ row }">
              {{ row.SendDate || "-" }}
            </template>
            <template slot="PackageSn" slot-scope="{ row }">
              {{ row.PackageSn || "-" }}
            </template>
          </dynamic-data-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import getTbInfo from '@/mixins/PRO/get-table-info-pro2'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import { GetInstallUnitList } from '@/api/PRO/install-unit'
import {
  GetProjectSendingInfoAndItemPagelist,
  ExportSendSteel,
  GetProjectSendingAllCount,
  ExportSendingDetailInfoList
} from '@/api/PRO/component-stock-out'
import { GetProjectPageList } from '@/api/PRO/pro-schedules'
import { GeAreaTrees } from '@/api/PRO/project'
import { GetInstallUnitPageList } from '@/api/PRO/install-unit'
import { GetWarehouseListOfCurFactory } from '@/api/PRO/warehouse'
import { GetLocationList } from '@/api/PRO/location'
import { combineURL, parseTime } from '@/utils'
import { GetFactoryProfessionalByCode } from '@/api/PRO/professionalType'
import { baseUrl } from '@/utils/baseurl'
import dayjs from 'dayjs'
export default {
  components: {
    DynamicDataTable
  },
  mixins: [getTbInfo],
  data() {
    return {
      form: {
        WarehouseName: '',
        LocationName: '',
        Code: '',
        dateRange: [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        Project_Id: '',
        Area_Id: '',
        InstallUnit_Id: '',
        PageInfo: {
          ParameterJson: [],
          Page: 1,
          PageSize: 20
        },
        Warehouse_Id: '',
        Location_Id: ''
      },
      warehouses: [], // 仓库
      locations: [], // 库位
      projectId: '',
      // 区域数据
      treeParamsArea: {
        'check-strictly': true,
        'expand-on-click-node': false,
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Id'
        }
      },
      selectParams: {
        clearable: true,
        placeholder: '请选择'
      },
      styles: { width: '100%' },
      SetupPositionData: [], // 批次
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      totalData: {
        Allsteelamount: 0,
        Allsteelweight: 0
      },
      value2: '',
      tbConfig: {
        Pager_Align: 'center'
      },
      queryInfo: {
        ParameterJson: [],
        BeginDate: '',
        EndDate: ''
      },
      columns: [],
      tbData: [],
      projects: [],
      installOption: [],
      factoryOption: [],
      locationOption: [],
      factory: '',
      locationName: '',
      total: 0,
      tbLoading: false,
      installName: ''
    }
  },
  created() {
    this.getFactoryTypeOption()
    this.getWarehouseListOfCurFactory()
  },
  mounted() {
    // await this.getTableConfig("pro_total_out_detail_list");
    // this.getProjectPageList();
    // this.fetchData();
    // this.getSearchType();
    // this.installName = this.$refs.dyTable.searchedField["installunit_name"];
  },
  methods: {
    exportTb() {

    },
    async getFactoryTypeOption() {
      await GetFactoryProfessionalByCode({ factoryId: localStorage.getItem('CurReferenceId') }).then(res => {
        if (res.IsSucceed) {
          this.ProfessionalType = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
      await this.getTableConfig(`pro_total_out_detail_list,${this.ProfessionalType[0].Code}`)
      this.getProjectPageList()
      this.fetchData()
    },
    getProjectPageList() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projects = res.Data.Data
        }
      })
    },
    fetchData() {
      this.tbLoading = true
      const form = { ...this.form }
      delete form['dateRange']
      this.form.dateRange = this.form.dateRange || []
      // form.BeginDate = this.form.dateRange[0];
      // form.EndDate = this.form.dateRange[1];
      form.BeginDate = parseTime(this.form.dateRange[0]) || ''
      form.EndDate = parseTime(this.form.dateRange[1]) || ''
      // console.log(this.form, "this.form");
      // console.log(form, "form");
      GetProjectSendingInfoAndItemPagelist({ ...form }).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data?.Data||[]
          this.tbData.map(v => {
            v.sendId = v.Id
            v.SendDate = v.SendDate ? parseTime(new Date(v.SendDate), '{y}-{m}-{d}') : v.SendDate
            delete v['Id']
          })
          this.total = res.Data?.TotalCount||0
          this.totalData.Allsteelamount = res.Data?.AllSteelAmount||0
          this.totalData.Allsteelweight = res.Data?.AllSteelWeight||0
          // console.log(this.tbData, "this.tbData");
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
      // GetProjectSendingAllCount({ is_fh: true }).then((res) => {
      //   if (res.IsSucceed) {
      //     // console.log(res.Data,"res.Data");
      //     this.totalData = res.Data
      //   } else {
      //     this.$message({
      //       message: res.Message,
      //       type: 'error'
      //     })
      //   }
      // })
    },
    getPageList() {
      // console.log(this.form, "this.form");
      this.fetchData()
    },
    datePickerwrapper() {
      if (!this.form.dateRange) {
        this.form.dateRange = ['', '']
      }
      this.fetchData()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.fetchData()
    },
    projectIdChange(e) {
      // console.log(e, "e");
      if (e) {
        const temp = this.projects.find(item => {
          return item.Sys_Project_Id == e
        })
        this.projectId = temp.Id
        this.getAreaList()
      }
    },
    projectIdClear(e) {
      // this.$refs.form.resetFields();
      this.areaClear()
    },
    // 获取区域
    getAreaList() {
      GeAreaTrees({
        projectId: this.projectId
      }).then((res) => {
        if (res.IsSucceed) {
          this.treeParamsArea.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    filterFun(val, ref) {
      this.$refs[ref].filterFun(val)
    },
    areaChange(e) {
      // console.log(e, "e");
      this.getInstall()
    },
    // 清空区域
    areaClear() {
      this.form.Area_Id = ''
      this.form.InstallUnit_Id = ''
    },
    // 获取批次
    getInstall() {
      GetInstallUnitPageList({
        Area_Id: this.form.Area_Id,
        Page: 1,
        PageSize: -1
      }).then((res) => {
        if (res.IsSucceed) {
          if (res.IsSucceed) {
            this.SetupPositionData = res.Data.Data
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getWarehouseListOfCurFactory() {
      GetWarehouseListOfCurFactory().then((res) => {
        if (res.IsSucceed) {
          this.warehouses = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    wareChange(e) {
      // console.log(e, "e");
      this.getLocationList()
    },
    // 获取库位
    getLocationList() {
      GetLocationList({
        Warehouse_Id: this.form.Warehouse_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.locations = res.Data
          // console.log(this.locations,"this.locations1111");
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    handleExport() {
      // if (!this.form.Project_Id) {
      //   this.$message({
      //     message: '请选择项目',
      //     type: 'warning'
      //   })
      //   return
      // }
      const form = { ...this.form }
      delete form['dateRange']
      this.form.dateRange = this.form.dateRange || []
      // form.BeginDate = this.form.dateRange[0];
      // form.EndDate = this.form.dateRange[1];
      form.BeginDate = parseTime(this.form.dateRange[0]) || ''
      form.EndDate = parseTime(this.form.dateRange[1]) || ''
      ExportSendingDetailInfoList({
        ...form
      }).then(res => {
        if (res.IsSucceed) {
          window.open(combineURL(this.$baseUrl, res.Data), '_blank')
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })

      // console.log(this.selectRow.Id, "this.selectRow.Id");
      // ExportSendSteel({
      //   sendId: this.selectRow.Id,
      // }).then((res) => {
      //   if (res.IsSucceed) {
      //     // const templateUrl = combineURL(this.$baseUrl, res.Data);
      //     // window.open(templateUrl, "_blank");
      //     const url = new URL(res.Data, baseUrl());
      //     window.open(url.href, "_blank");
      //     this.$message({
      //       type: "success",
      //       message: "导出成功!",
      //     });
      //   } else {
      //     this.$message({
      //       message: res.Message,
      //       type: "error",
      //     });
      //   }
      // });
    }
    // getSearchType() {
    //   GetProjectPageList({ PageSize: -1 }).then((res) => {
    //     if (res.IsSucceed) {
    //       this.projects = res.Data;
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: "error",
    //       });
    //     }
    //   });
    //   GetWarehouseListOfCurFactory({}).then((res) => {
    //     if (res.IsSucceed) {
    //       this.factoryOption = res.Data;
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: "error",
    //       });
    //     }
    //   });
    // },
    // factoryChange(v) {
    //   this.locationName = "";
    //   this.locationOption = [];

    //   const item = this.factoryOption.find((t) => t.Display_Name === v);
    //   if (item) {
    //     this.getLocation(item.Id);
    //   }
    //   this.$refs.dyTable.searchedField["location_name"] = "";
    // },
    // locationNameChange(v) {
    //   this.$refs.dyTable.searchedField["location_name"] = v;
    //   this.showSearchBtn();
    // },
    // getLocation(Warehouse_Id) {
    //   GetLocationList({
    //     Warehouse_Id,
    //   }).then((res) => {
    //     if (res.IsSucceed) {
    //       this.locationOption = res.Data;
    //     } else {
    //       this.$message({
    //         message: res.Message,
    //         type: "error",
    //       });
    //     }
    //   });
    // },
    // projectChange(v) {
    //   this.installName = "";
    //   this.installOption = [];

    //   if (v) {
    //     const item = this.projects.find((t) => t.Name === v);
    //     this.getUnitList(item.Id);
    //   } else {
    //     this.$refs.dyTable.searchedField["installunit_name"] = "";
    //   }
    //   this.showSearchBtn();
    // },
    // getUnitList(Project_Id) {
    //   GetInstallUnitList({
    //     Project_Id,
    //   }).then((res) => {
    //     if (res.IsSucceed) {
    //       this.installOption = res.Data;
    //     }
    //   });
    // },
    // installNameChange(v) {
    //   this.$refs.dyTable.searchedField["installunit_name"] = v;
    //   this.showSearchBtn();
    // },
  }
}
</script>

<style scoped lang="scss">
.cs-z-page-main-content {
  border-radius: 4px;
}
.cs-container {
  // display: flex;
  // flex-direction: column;
  // justify-content: space-around;
  height: 100%;
  overflow: hidden;
  .cs-z-tb-wrapper{
    height: calc(100% - 50px);
  }
  .px50 {
    flex: 0 1 50px;
  }
}
::v-deep .custom-pagination .checked-count {
  top: 20px;
}
::v-deep .pagination {
  justify-content: right !important;
}
::v-deep .form-search {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .el-form-item {
    width: 24%;
    display: flex;
  }
  .el-form-item__label {
    width: 100px;
  }
  .el-form-item__content {
    min-width: 10px;
    flex: 1;
  }
  .el-select {
    width: 100%;
  }
}
.total-wrapper {
  float: right;
  color: #298dff;
  background-color: #f5faff;
  font-size: 14px;
  padding: 6px 10px 6px 10px;
  margin-right: 10px;
}
.date-picker-wrapper {
  float: right;
  width: 20%;
}
</style>

<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <top-header>
        <template #left>
          <div class="cs-header">
            <el-button @click="toBack">返回</el-button>
          </div>
        </template>
        <template #right>
          <ExportCustomReport v-if="form.Id" code="Shipping_plan_template" style="margin:0 10px" name="导出派工单" :ids="[form.Id]" />
          <template v-if="!readonly">
            <el-button type="primary" :loading="loading" @click="handleSubmit(1)">保存草稿</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit(2)">提交</el-button>
          </template>
        </template>
      </top-header>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="发货计划单号" prop="Code">
              <el-input
                v-model="form.Code"
                :disabled="true"
                placeholder="自动生成"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目名称" prop="projectName">
              <el-input v-model="form.projectName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="装车人" prop="Loader_UserId">
              <el-select v-model="form.Loader_UserId" :disabled="readonly" placeholder="请选择" clearable filterable style="width: 100%">
                <el-option v-for="item in allUsers" :key="item.id" :value="item.Id" :label="item.Display_Name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发货人" prop="Shipper_UserId">
              <el-select v-model="form.Shipper_UserId" :disabled="readonly" placeholder="请选择" clearable filterable style="width: 100%">
                <el-option v-for="item in allUsers" :key="item.id" :value="item.Id" :label="item.Display_Name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划发货日期" prop="Plan_Date">
              <el-date-picker
                v-model="form.Plan_Date"
                :disabled="readonly"
                placeholder="请选择"
                style="width: 100%"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划编制人" prop="Plan_UserId">
              <el-select v-model="form.Plan_UserId" :disabled="readonly" placeholder="请选择" clearable filterable style="width: 100%">
                <el-option v-for="item in allUsers" :key="item.id" :value="item.Id" :label="item.Display_Name" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input
                v-model="form.Remark"
                :disabled="readonly"
                :autosize="{ minRows: 2, maxRows: 2 }"
                :maxlength="500"
                show-word-limit
                type="textarea"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div class="header">
        <el-form inline label-width="70px">
          <el-form-item label="构件名称"><el-input v-model="query.code" clearable /></el-form-item>
          <el-form-item label="区域">
            <el-tree-select
              ref="treeSelectArea"
              v-model="query.area"
              class="treeselect"
              :select-params="selectParams"
              :tree-params="treeParamsArea"
              @searchFun="filterFun($event, 'treeSelectArea')"
              @select-clear="areaClear"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="query.status" clearable>
              <el-option label="未完成" value="未完成" />
              <el-option label="已完成" value="已完成" />
            </el-select>
          </el-form-item>
          <el-form-item><el-button type="primary" @click="search">搜索</el-button></el-form-item>
        </el-form>
        <div class="right">
          <div style="margin-right: 10px">理论总重：{{ sumWeight }}t</div>
          <template v-if="!readonly">
            <el-button
              :disabled="!selectList.length"
              size="mini"
              type="danger"
              @click="handleDelete"
            >删除</el-button>
            <el-button
              size="mini"
              type="primary"
              @click="handleAdd"
            >添加</el-button>
          </template>
        </div>
      </div>

      <div v-loading="tbLoading" class="fff cs-z-tb-wrapper">
        <vxe-table
          ref="vxeTable"
          :empty-render="{name: 'NotData'}"
          show-header-overflow
          empty-text="暂无数据"
          height="auto"
          show-overflow
          :loading="tbLoading"
          class="cs-vxe-table"
          align="left"
          stripe
          :data="filterData"
          resizable
          :edit-config="{trigger: 'click', mode: 'cell', activeMethod: activeCellMethod}"
          :tooltip-config="{ enterable: true }"
          @checkbox-change="checkboxChange"
          @checkbox-all="selectAllCheckboxChange"
        >
          <vxe-column v-if="!readonly" type="checkbox" width="60" />
          <template v-for="item in columns">
            <vxe-column
              v-if="item.Code === 'Plan_Count' && !readonly"
              :key="item.Code"
              :field="item.Code"
              :align="item.Align"
              :title="item.Display_Name"
              sortable
              :edit-render="{}"
              :min-width="item.Width"
            >
              <template #edit="{ row }">
                <vxe-input
                  v-model="row.Plan_Count"
                  type="integer"
                  :min="1"
                  @change="(val)=>sumItem(row)"
                />
              </template>
              <template #default="{ row }">
                {{ row.Plan_Count }}
              </template>
            </vxe-column>
            <vxe-column
              v-else-if="item.Code === 'Is_Direct'"
              :key="item.Code"
              :field="item.Code"
              :title="item.Display_Name"
              sortable
              :width="item.Width"
              :align="item.Align"
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ row.Is_Direct==true ? "是" : "否" }}
              </template>
            </vxe-column>
            <vxe-column
              v-else
              :key="item.Code"
              :fixed="item.Is_Frozen?item.Frozen_Dirction:''"
              show-overflow="tooltip"
              sortable
              :field="item.Code"
              :title="item.Display_Name"
              :align="item.Align"
              :min-width="item.Width"
            >
              <template #default="{ row }">
                {{ row[item.Code] ? row[item.Code] : "-" }}
              </template>
            </vxe-column>
          </template>
        </vxe-table>
      </div>
      <el-dialog
        v-if="dialogVisible"
        v-dialogDrag
        class="plm-custom-dialog"
        :title="title"
        :visible.sync="dialogVisible"
        :width="width"
        :top="topDialog"
        @close="close"
      >
        <component
          :is="currentComponent"
          ref="content"
          :dialog-visible="dialogVisible"
          :project-id="projectId"
          :sys-project-id="form.Sys_Project_Id"
          :checked-data="tbData"
          @close="close"
          @reCount="getTotal"
          @selectList="addSelectList"
        />
      </el-dialog>
      <check-info ref="info" />
    </div>
  </div>
</template>

<script>
import TitleInfo from '@/views/PRO/shipment/actually-sent/v3/component/TitleInfo'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import TopHeader from '@/components/TopHeader'
import CheckInfo from '@/views/PRO/Component/GetPackingDetail'
import { closeTagView, parseTime } from '@/utils'
import { GetProjectEntity, GetProjectPageList } from '@/api/PRO/pro-schedules'
import { GetAllUserPage, GetGridByCode } from '@/api/sys'
import numeral from 'numeral'
import AddDialog from './component/add'
import { GetLastUser, GetOutPlanEntity, SaveOutPlan } from '@/api/PRO/ship-plan'
import { GeAreaTrees } from '@/api/PRO/project'
import ExportCustomReport from "@/components/ExportCustomReport/index.vue";

export default {
  name: 'ShipPlanDetail',
  components: {
    ExportCustomReport,
    TitleInfo,
    TopHeader,
    DynamicDataTable,
    CheckInfo,
    AddDialog
  },
  data() {
    return {
      query: {
        code: '',
        area: '',
        status: ''
      },
      width: '80vw',
      currentComponent: '',
      title: '',
      dialogVisible: false,
      topDialog: '5vh',
      sendNumber: '',
      totalNum: '',
      totalWeight: '',
      form: {
        Sys_Project_Id: '',
        Remark: '',
        Loader_UserId: '',
        Shipper_UserId: '',
        Plan_UserId: localStorage.getItem('UserId'),
        Plan_Date: '',
        Status: '', // 状态, 1:草稿，2：审批中，3：结束，-1：已驳回
        projectName: ''
      },
      rules: {
        projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
        // Loader_UserId: [{ required: true, message: '请选择装车人', trigger: 'change' }],
        // Shipper_UserId: [{ required: true, message: '请选择发货人', trigger: 'change' }],
        Plan_Date: [{ required: true, message: '请选计划发货日期', trigger: 'change' }],
        Plan_UserId: [{ required: true, message: '请选计划编制人', trigger: 'change' }]
      },
      PageInfo: {
        ParameterJson: [],
        Page: 1,
        PageSize: 20
      },
      plm_ProjectSendingInfo: {},
      Itemdetail: [],
      projects: '',
      Id: '',
      projectId: '',
      tbConfig: {
        Pager_Align: 'center'
      },
      columns: [],
      tbData: [],
      total: 0,
      tbLoading: false,
      selectList: [],
      sums: [],
      allUsers: [],
      loading: false,
      filterData: [],
      type: 'view',
      selectParams: {
        placeholder: '请选择',
        clearable: true
      },
      // 区域数据
      treeParamsArea: {
        'check-strictly': true,
        'expand-on-click-node': false,
        'default-expand-all': true,
        filterable: false,
        clickParent: true,
        data: [],
        props: {
          children: 'Children',
          label: 'Label',
          value: 'Label'
        }
      }
    }
  },
  computed: {
    sumWeight() {
      let sum = 0
      this.filterData.forEach(item => {
        sum += Number(item.Total_Weight)
      })
      return (sum / 1000).toFixed(5)
    },
    readonly() {
      return this.type === 'view'
    }
  },
  watch: {
  },
  created() {
    this.getAllUsers()
    this.getTableConfig()
  },
  async mounted() {
    this.type = this.$route.query.type || 'add'
    if (this.type === 'add') {
      const {
        Name,
        Id,
        Sys_Project_Id
      } = JSON.parse(decodeURIComponent(this.$route.query.p))
      this.projectId = Id
      this.form.projectName = Name
      this.form.Sys_Project_Id = Sys_Project_Id
      this.getProjectEntity(this.projectId)
      this.getLastUser()
    } else {
      const {
        Name
      } = JSON.parse(decodeURIComponent(this.$route.query.p))
      console.log(JSON.parse(decodeURIComponent(this.$route.query.p)))
      this.form.projectName = Name
      this.getInfo()
    }
  },
  methods: {
    sumItem(row) {
      row.Total_Weight = (row.SteelWeight * row.Plan_Count).toFixed(2)
    },
    checkboxChange() {
      this.selectList = this.$refs.vxeTable.getCheckboxRecords()
    },
    selectAllCheckboxChange() {
      this.selectList = this.$refs.vxeTable.getCheckboxRecords()
    },
    filterFun(val, ref) {
      this.$refs[ref].filterFun(val)
    },
    // 清空区域
    areaClear() {
      this.query.Area_Id = ''
    },
    // 获取区域
    getAreaList() {
      GeAreaTrees({
        sysProjectId: this.form.Sys_Project_Id
      }).then((res) => {
        if (res.IsSucceed) {
          this.treeParamsArea.data = res.Data
          this.$nextTick((_) => {
            this.$refs.treeSelectArea.treeDataUpdateFun(res.Data)
          })
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    search() {
      this.filterData = this.tbData.filter(item => {
        return (item.Component_Code || '').includes(this.query.code) && (item.FullAreaposition || '').includes(this.query.area) && (item.Status || '').includes(this.query.status)
      })
    },
    getLastUser() {
      GetLastUser({
        Sys_Project_Id: this.form.Sys_Project_Id
      }).then(res => {
        if (res.IsSucceed) {
          this.form.Loader_UserId = res.Data.Loader_UserId
          this.form.Shipper_UserId = res.Data.Shipper_UserId
        }
      })
    },
    activeCellMethod({ row, column, columnIndex }) {
      return column.field === 'Plan_Count'
    },
    async handleSubmit(status) {
      await this.$refs['form'].validate()
      if (!this.tbData || !this.tbData.length) {
        this.$message.error('请添加明细')
        return
      }
      let flag = false
      this.tbData.forEach(item => {
        if (item.Plan_Count < 1) {
          flag = true
        }
      })
      if (flag) {
        this.$message.error('应发数量不能小于1')
        return
      }
      this.isClicked = true
      const submitObj = {
        Main: {
          ...this.form,
          Status: status
        },
        Details: this.tbData
      }
      const res = await SaveOutPlan(submitObj)
      if (res.IsSucceed) {
        this.$message.success('保存成功')
        this.toBack()
      } else {
        this.$message.error(res.Message)
      }
    },
    getNum(a, b) {
      return numeral(a).subtract(b).format('0.[000]')
    },
    getProjectEntity(Id) {
      GetProjectEntity({ Id }).then((res) => {
        if (res.IsSucceed) {
          const Consignee = res.Data.Contacts.find((item) => {
            return item.Type == 'Consignee'
          })
          console.log(Consignee, 'Consignee')
          this.form.receiveName = Consignee?.Name
          this.form.Receiver_Tel = Consignee?.Tel
        }
      })
    },
    getProjectPageList() {
      GetProjectPageList({ PageSize: -1 }).then((res) => {
        if (res.IsSucceed) {
          this.projects = res.Data.Data
        }
      })
    },
    toBack() {
      closeTagView(this.$store, this.$route)
    },
    getInfo() {
      GetOutPlanEntity({
        id: this.$route.query.id
      }).then((res) => {
        if (res.IsSucceed) {
          const name = this.form.projectName
          this.form = res.Data.Main
          this.form.projectName = name
          this.form.projectId = this.form.Sys_Project_Id
          this.tbData = res.Data.Details.map(item => {
            item.Status = ((item.Accept_Count - item.Plan_Count >= 0) && item.Accept_Count) ? '已完成' : '未完成'
            return item
          })
          this.search()
          this.getAreaList()
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    addSelectList(list) {
      list.forEach((item) => {
        item.Status = '未完成'
        this.tbData.push(item)
      })
      this.total = this.tbData.length
      this.search()
    },
    getTotal() {},
    handleAdd() {
      this.currentComponent = 'AddDialog'
      this.dialogVisible = true
      this.title = '新增'
    },
    handleDelete() {
      this.$confirm('删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.selectList.forEach((item) => {
            const index = this.tbData.findIndex((v) => v.Component_Id === item.Component_Id)
            index !== -1 && this.tbData.splice(index, 1)
          })
          this.search()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {})
    },
    close() {
      this.dialogVisible = false
    },
    handleInfo(row) {
      this.$refs.info.handleOpen(row)
    },
    getTableConfig() {
      return new Promise((resolve) => {
        GetGridByCode({
          code: 'PROShipPlanDetail'
        }).then((res) => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message({
                message: '表格配置不存在',
                type: 'error'
              })
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            this.columns = Data.ColumnList.map((item) => {
              item.Is_Resizable = true
              return item
            })
            this.PageInfo.PageSize = +Data.Grid.Row_Number
            resolve(this.columns)
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    getAllUsers() {
      GetAllUserPage().then(res => {
        this.allUsers = res.Data.Data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .tb-status {
    background: #fae6bb;
    padding: 16px 20px;
    font-size: 1.2em;
    font-weight: bold;
    display: flex;

    * {
      margin-right: 12px;
    }
  }

  .el-form {
    margin: 16px 10px;
  }

  .title {
    margin-left: 10px;
  }

  .cs-red{
    color:red
  }

  .statistics-container {
    display: flex;
    .statistics-item {
      margin-right: 32px;
      .cs-label{
        display: inline-block;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        color: #999999;
        margin-left: 10px;
        // margin-right: 16px;
      }
      .cs-num {
        font-size: 16px;
        font-weight: 600;
        color: #00c361;
      }
    }
  }
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right{
      display: flex;
      align-items: center;
    }
  }
</style>

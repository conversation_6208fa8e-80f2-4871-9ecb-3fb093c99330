<template>
  <div class="app-container abs100">
    <div class="cs-wrapper">
      <div class="search-x">
        <el-form inline style="display: flex;width: 100%">
          <el-form-item>
            <el-button type="primary" @click="dialogVisible = true">新建</el-button>
            <el-button
              type="success"
              :loading="exportLoading"
              @click="exportExcel"
            >导出</el-button>
          </el-form-item>
          <el-form-item>
            <ExportCustomReport code="Shipping_plan_template" name="导出派工单" :ids="selections.map(i=>i.Id)" />
          </el-form-item>
          <el-form-item label="发货计划单号" style="margin-left: auto">
            <el-input v-model="form.Code" placeholder="请输入" clearable style="width: 180px" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-select v-model="form.Sys_Project_Id" placeholder="请选择" filterable clearable style="width: 180px">
              <el-option
                v-for="item in projects"
                :key="item.Sys_Project_Id"
                :label="item.Short_Name"
                :value="item.Sys_Project_Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="计划发货日期">
            <el-date-picker
              v-model="form.dateRange"
              style="width: 300px"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.Status" filterable clearable style="width: 100px">
              <el-option v-for="item in statusDict" :key="item.label" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">搜索</el-button>
          </el-form-item>
        </el-form>

      </div>
      <div
        v-loading="tbLoading"
        class="fff cs-z-tb-wrapper"
        style="flex: 1 1 auto"
      >
        <dynamic-data-table
          ref="dyTable"
          class="cs-plm-dy-table"
          :columns="columns"
          :config="tbConfig"
          :data="tbData"
          :page="pageInfo.Page"
          :total="total"
          border
          stripe
          @gridPageChange="handlePageChange"
          @gridSizeChange="handleSizeChange"
          @select="selectChange"
          @selectAll="handleSelectAll"
        >
          <template slot="op" slot-scope="{ row, index }">
            <el-button
              v-if="[1,-1].includes(row.Status)"
              :index="index"
              type="text"
              @click="handleEdit(row.Id, row)"
            >编辑</el-button>
            <el-button
              v-if="[1,-1].includes(row.Status)"
              :index="index"
              type="text"
              @click="handleSub(row.Id)"
            >提交</el-button>
            <el-button
              v-if="[2,3,4].includes(row.Status)"
              :index="index"
              type="text"
              @click="handleInfo(row.Id,row)"
            >查看</el-button>
            <el-button
              v-if="[2].includes(row.Status)"
              :index="index"
              type="text"
              @click="handleWithdraw(row.Id)"
            >撤回</el-button>
            <el-button
              v-if="[1,-1].includes(row.Status)"
              :index="index"
              type="text"
              style="color:red"
              @click="handleDel(row.Id)"
            >删除</el-button>
          </template>
        </dynamic-data-table>
      </div>
    </div>
    <el-dialog
      v-dialogDrag
      title="新增发货计划"
      class="plm-custom-dialog"
      :visible.sync="dialogVisible"
      width="30%"
      @close="handleClose"
    >
      <el-form
        ref="form2"
        :model="form2"
        :rules="rules"
        label-width="70px"
        class="demo-ruleForm"
      >
        <el-form-item label="项目" prop="ProjectId">
          <el-select
            v-model="form2.ProjectId"
            class="w100"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="item in projects"
              :key="item.Id"
              :label="item.Short_Name"
              :value="item.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button @click="resetForm2('form2')">取 消</el-button>
          <el-button
            type="primary"
            @click="submitForm2('form2')"
          >确 定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { GetProjectPageList } from '@/api/PRO/pro-schedules'
import addRouterPage from '@/mixins/add-router-page'
import {
  CancelFlow,
  GetProjectSendingAllCount,
  SubmitProjectSending,
  WithdrawDraft
} from '@/api/PRO/component-stock-out'
import { GetGridByCode } from '@/api/sys'
import { parseTime } from '@/utils'
import DynamicDataTable from '@/components/DynamicDataTable/DynamicDataTable'
import { DeleteOutPlan, ExportOutPlanList, GetOutPlanPageList, SubmitOutPlan } from '@/api/PRO/ship-plan'
import ExportCustomReport from '@/components/ExportCustomReport/index.vue'

export default {
  name: 'ShipPlan',
  components: { ExportCustomReport, DynamicDataTable },
  mixins: [addRouterPage],
  data() {
    return {
      form: {
        dateRange: [],
        Sys_Project_Id: '',
        Code: '',
        Status: ''
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      projects: [],
      statusDict: [
        {
          label: '草稿',
          value: '1'
        },
        {
          label: '进行中',
          value: '3'
        },
        {
          label: '已完成',
          value: '4'
        }
      ],
      form2: {
        ProjectId: ''
      },
      dialogVisible: false,
      rules: {
        ProjectId: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      addPageArray: [
        {
          path: this.$route.path + '/add',
          hidden: true,
          component: () => import('@/views/PRO/shipment/plan/add'),
          name: 'PROShipPlanAdd',
          meta: { title: '新建发货计划单' }
        },
        {
          path: this.$route.path + '/edit',
          hidden: true,
          component: () => import('@/views/PRO/shipment/plan/add'),
          name: 'PROShipPlanEdit',
          meta: { title: '编辑发货计划单' }
        },
        {
          path: this.$route.path + '/detail',
          hidden: true,
          component: () => import('@/views/PRO/shipment/plan/add'),
          name: 'PROShipPlanDetail',
          meta: { title: '发货计划详情' }
        }
      ],
      tbData: [],
      total: 0,
      tbLoading: false,
      selections: [],
      totalData: {
        Allsteelamount: 0,
        Allsteelweight: 0
      },
      pageInfo: {
        Page: 1,
        PageSize: 20
      },
      tbConfig: {
        Pager_Align: 'right',
        Op_Width: 240
      },
      columns: [],
      exportLoading: false
    }
  },
  created() {
    this.getProjectList()
    this.getTableConfig()
    this.fetchData()
  },
  methods: {
    selectChange({ selection, row }) {
      this.selections = selection
    },
    handleSelectAll(selection) {
      this.selections = selection
    },
    search() {
      this.pageInfo.Page = 1
      this.fetchData()
    },
    exportExcel() {
      this.exportLoading = true
      const form = {
        ...this.form,
        ...this.pageInfo
      }
      delete form['dateRange']
      this.form.dateRange = this.form.dateRange || []
      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])
        ? parseTime(this.form.dateRange[0])
        : ''
      form.Plan_Date_End = parseTime(this.form.dateRange[1])
        ? parseTime(this.form.dateRange[1])
        : ''
      ExportOutPlanList(form).then(res => {
        if (res.IsSucceed) {
          window.open(this.$baseUrl + res.Data)
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      }).finally(() => {
        this.exportLoading = false
      })
    },
    async getProjectList() {
      this.treeLoading = true
      this.tableLoading = true
      const res = await GetProjectPageList({ PageSize: -1 })
      this.projects = res.Data.Data
      if (!res.Data.Data.length) {
        this.$message.error('暂无项目')
        this.treeLoading = false
        this.tableLoading = false
      } else {
        this.projectId = res.Data.Data[0].Sys_Project_Id
      }
    },
    handleClose() {
      this.$refs.form2.resetFields()
      this.dialogVisible = false
    },
    resetForm2(formName) {
      this.dialogVisible = false
      this.$refs[formName].resetFields()
    },
    submitForm2(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { ProjectId } = this.form2
          const {
            Name,
            Id,
            Code,
            Address,
            Receiver,
            Receiver_Tel,
            Sys_Project_Id,
            Receive_UserName
          } = this.projects.find((v) => v.Id === this.form2.ProjectId)
          const data = {
            ProjectId,
            Id,
            Name,
            Code,
            Address,
            Receiver,
            Receiver_Tel,
            Sys_Project_Id,
            Receive_UserName,
            ProfessionalType: this.ProfessionalType
          }
          this.$router.push({
            name: 'PROShipPlanAdd',
            query: {
              pg_redirect: this.$route.name,
              p: encodeURIComponent(JSON.stringify(data))
            }
          })
          this.dialogVisible = false
          this.$refs.form2.resetFields()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleEdit(id, { Project_Name }) {
      this.$router.push({
        name: 'PROShipPlanEdit',
        query: { pg_redirect: this.$route.name, id, type: 'edit', p: JSON.stringify({ Name: Project_Name }) }
      })
    },
    // 撤回至草稿
    handleWithdraw(id) {
      this.$confirm('撤回至草稿, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          WithdrawDraft({
            id: id
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '撤销成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => { })
    },
    handleSub(id) {
      console.log(id, 'id')
      this.$confirm('提交该发货计划, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          SubmitOutPlan({
            id
          }).then((res) => {
            if (res.IsSucceed) {
              this.$message({
                message: '提交成功',
                type: 'success'
              })
              this.fetchData()
            } else {
              this.$message({
                message: res.Message,
                type: 'error'
              })
            }
          })
        })
        .catch(() => { })
    },
    handleDel(id) {
      this.$confirm('是否删除该发货计划?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteOutPlan({
          id
        }).then((res) => {
          if (res.IsSucceed) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleInfo(id, { Project_Name }) {
      this.$router.push({
        name: 'PROShipPlanDetail',
        query: { pg_redirect: this.$route.name, id, type: 'view', p: JSON.stringify({ Name: Project_Name }) }
      })
    },
    handleCancelFlow(instanceId) {
      this.$confirm('是否撤回?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        CancelFlow({
          instanceId
        }).then(res => {
          if (res.IsSucceed) {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.fetchData()
          } else {
            this.$message({
              message: res.Message,
              type: 'error'
            })
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    fetchData() {
      this.tbLoading = true
      const form = {
        ...this.form,
        ...this.pageInfo
      }
      delete form['dateRange']
      this.form.dateRange = this.form.dateRange || []
      form.Plan_Date_Begin = parseTime(this.form.dateRange[0])
        ? parseTime(this.form.dateRange[0])
        : ''
      form.Plan_Date_End = parseTime(this.form.dateRange[1])
        ? parseTime(this.form.dateRange[1])
        : ''
      GetOutPlanPageList(form).then((res) => {
        if (res.IsSucceed) {
          this.tbData = res.Data.Data.map((v) => {
            v.Plan_Date = v.Plan_Date
              ? parseTime(new Date(v.Plan_Date), '{y}-{m}-{d}')
              : v.Plan_Date
            return v
          })
          this.total = res.Data.TotalCount
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
        this.tbLoading = false
      })
      GetProjectSendingAllCount({ ...form }).then((res) => {
        if (res.IsSucceed) {
          // console.log(res.Data,"res.Data");
          this.totalData = res.Data
        } else {
          this.$message({
            message: res.Message,
            type: 'error'
          })
        }
      })
    },
    getTableConfig() {
      return new Promise((resolve) => {
        GetGridByCode({
          code: 'ProShipPlanList'
        }).then(res => {
          const { IsSucceed, Data, Message } = res
          if (IsSucceed) {
            if (!Data) {
              this.$message({
                message: '表格配置不存在',
                type: 'error'
              })
              return
            }
            this.tbConfig = Object.assign({}, this.tbConfig, Data.Grid)
            this.columns = (Data.ColumnList.filter(v => v.Is_Display) || []).map(item => {
              item.Is_Resizable = true
              return item
            })
            if (this.pageInfo) {
              this.pageInfo.PageSize = +Data.Grid.Row_Number
            } else {
              this.form.PageSize = +Data.Grid.Row_Number
            }
            resolve(this.columns)
            // this.fetchData();
          } else {
            this.$message({
              message: Message,
              type: 'error'
            })
          }
        })
      })
    },
    handlePageChange(e) {
      if (this.pageInfo) {
        this.pageInfo.Page = e.page
      } else {
        this.form.Page = e.page
      }
      // console.log(this.pageInfo.Page);
      this.fetchData()
    },
    handleSizeChange(e) {
      if (this.pageInfo) {
        this.pageInfo.Page = 1
        this.pageInfo.PageSize = e.size
      } else {
        this.form.Page = 1
        this.form.PageSize = e.size
      }
      // console.log(this.pageInfo);
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
  .app-container {
    .cs-wrapper {
      background-color: #FFFFFF;
      height: 100%;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .search-x {
        width: 100%;
        display: flex;
        align-items: center;

        .cs-label {
          white-space: nowrap;
        }
      }

      .tb-x {
        flex: 1;
        margin-bottom: 10px;
        overflow: auto;
      }
    }

    .cs-unit {
      margin: 0 10px;
    }

  }

  .cs-red {
    color: red;
  }

</style>

/* eslint-disable */
import { hiprint} from "vue-plugin-hiprint";
import {GetPreferenceSettingValue} from "@/api/sys/system-setting";
import {GetCompany} from "@/api/plm/site";

// 自定义设计元素1
export const DeliveryNoteProvider = function (ops) {
  var addElementTypes = async function (context) {
    const res = await GetCompany()
    let logo = res.Data.Icon
    context.removePrintElementTypes("Shipment");
    context.addPrintElementTypes(
      1,
      [
        new hiprint.PrintElementTypeGroup("平台", [
          {
            tid: 'Shipment.Logo', title: 'Logo图片', data: '', type: 'image',
            options:{
              field: 'Logo',
              height: 40,
              width:40,
              src:logo
            }
          },
          {
            tid: 'Shipment.QrcodeText', title: '二维码', data: 'XS888888888', type: 'text',
            options: {
              field: 'QrcodeText',
              testData: 'XS888888888',
              height: 64,
              fontSize: 12,
              lineHeight: 18,
              textType: "qrcode"
            }
          },
          {
            tid: 'Shipment.ContractNumber', title: '内部合同编号', data: 'HT-0001', type: 'text',
            options: {
              testData: 'HT-0001',
              field: "ContractNumber",
              fontSize: 12,
              width: 200
            }
          },
          {
            tid: 'Shipment.Code', title: '单据号', data: 'BIM-CK-00249', type: 'text',
            options: {
              testData: 'BIM-CK-00249',
              field: "Code",
              fontSize: 12,
              width: 200
            }
          },
          {
            tid: 'Shipment.SendDate', title: '日期', data: '2024-04-03', type: 'text',
            options: {
              testData: '2024-04-03',
              fontSize: 12,
              field: "SendDate",
            }
          },
          {
            tid: 'Shipment.Number', title: '发货序号', data: '1', type: 'text',
            options: {
              field: 'Number',
              testData: '1',
              fontSize: 12,
            }
          },
          {
            tid: 'Shipment.Address', title: '项目地址', data: '浙江省绍兴市柯桥区鉴湖路1587号', type: 'text',
            options: {
              field: 'Address',
              testData: '浙江省绍兴市柯桥区鉴湖路1587号',
              fontSize: 12,
              width: 300
            }
          },
          {
            tid: 'Shipment.ProjectName', title: '项目名称', data: '比姆泰客测试项目', type: 'text',
            options: {
              field: 'ProjectName',
              testData: '比姆泰客测试项目',
              fontSize: 12,
              width: 250
            }
          },

          {
            tid: 'Shipment.MakerName', title: '出库人', data: 'Lily', type: 'text',
            options: {
              field: 'MakerName',
              testData: 'Lily',
              fontSize: 12,
            }
          },

          {
            tid: 'Shipment.Consignee', title: '收货人', data: 'Lily', type: 'text',
            options: {
              field: 'Consignee',
              testData: 'Lily',
              fontSize: 12,
            }
          },
          {
            tid: 'Shipment.ConsigneeTel', title: '联系电话', data: '185****4235', type: 'text',
            options: {
              field: 'ConsigneeTel',
              testData: '185****4235',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.VehicleNo', title: '车牌', data: '浙D388432', type: 'text',
            options: {
              field: 'VehicleNo',
              testData: '浙D388432',
              fontSize: 12,
            }
          },
          {
            tid: 'Shipment.Telephone', title: '司机电话', data: '185****4235', type: 'text',
            options: {
              field: 'Telephone',
              testData: '185****4235',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.LoadingsName', title: '装车班', data: '仓储部门', type: 'text',
            options: {
              field: 'LoadingsName',
              testData: '仓储部门',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.LoadingsPersonnelName', title: '装车班人员', data: '张三', type: 'text',
            options: {
              field: 'LoadingsPersonnelName',
              testData: '张三',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.ReceivingUnit', title: '收货单位', data: 'XX单位', type: 'text',
            options: {
              field: 'ReceivingUnit',
              testData: 'XX单位',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.Trips', title: '车次', data: '第一车', type: 'text',
            options: {
              field: 'Trips',
              testData: '第一车',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.Pound_Weight', title: '磅重（kg）', data: '5369.64', type: 'text',
            options: {
              field: 'Pound_Weight',
              testData: '5369.64',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.Tare_Weight', title: '皮重（kg）', data: '5369.64', type: 'text',
            options: {
              field: 'Tare_Weight',
              testData: '5369.64',
              fontSize: 12,
              width: 150
            }
          },
          {
            tid: 'Shipment.Net_Weight', title: '净重（kg）', data: '5369.64', type: 'text',
            options: {
              field: 'Net_Weight',
              testData: '5369.64',
              fontSize: 12,
              width: 150
            }
          },
          // {
          //   tid: 'Shipment.CustomText',
          //   title: '',
          //   customText: '自定义文本',
          //   custom: true,
          //   type: 'text',
          //   options: {
          //     field:'CustomText',
          //     testData: '自定义文本',
          //     height: 16,
          //     fontSize: 15,
          //     hideTitle: true
          //   }
          // },
          {
            tid: "Shipment.customText",
            title: "自定义文本",
            customText: "自定义文本",
            custom: true,
            type: "text"
          }
        ]),
        new hiprint.PrintElementTypeGroup("表格/其他", [
          {
            tid: 'Shipment.Table', title: '订单数据',
            type: 'table',
            options: {
              field: 'Table',
              tableHeaderRepeat: 'first',
              tableFooterRepeat: 'last',
              fields: [
                {text: '序号', field: 'RowNo'},
                {text: '构件/包名称', field: 'SteelName'},
                {text: '数量', field: 'SteelAmount'},
                {text: '规格', field: 'SteelSpec'},
                {text: '长度', field: 'SteelLength'},
                {text: '单重（kg）', field: 'SteelWeight'},
                {text: '总重（kg）', field: 'SteelAllWeight'},
                {text: '单毛重（kg）', field: 'GrossWeight'},
                {text: '总毛重（kg）', field: 'GrossAllWeight'},
                {text: '备注（构件清单备注信息）', field: 'ComponentDetails'},
                {text: '备注（要货区域）', field: 'AreaPosition'},
              ],
              testData:JSON.stringify([
                {
                  SteelName:'构件包-1',
                  SteelAmount:2,
                  SteelSpec:'PKG00068',
                  SteelLength:'50',
                  AreaPosition:'第一批次加工构件',
                  SteelWeight:'1000',
                  SteelAllWeight:'2000',
                  GrossWeight:'1050',
                  GrossAllWeight:'2100',
                  RowNo:1,
                  Unit:'kg',
                  IsPackage:true
                },
                {
                  SteelName:'AKP-001',
                  SteelAmount:2,
                  SteelSpec:'88*88',
                  SteelLength:'50',
                  AreaPosition:'第一批次加工构件',
                  SteelWeight:'1000',
                  SteelAllWeight:'2000',
                  GrossWeight:'1050',
                  GrossAllWeight:'2100',
                  RowNo:2,
                  Unit:'kg'
                },
                {
                  SteelName:'AKP-002',
                  SteelAmount:3,
                  SteelSpec:'88*88',
                  SteelLength:'50',
                  AreaPosition:'第一批次加工构件',
                  SteelWeight:'1000',
                  SteelAllWeight:'3000',
                  GrossWeight:'1050',
                  GrossAllWeight:3150,
                  RowNo:3,
                  Unit:'kg'
                },
                {
                  SteelName:'合计',
                  SteelAmount:5,
                  SteelSpec:'',
                  SteelLength:'',
                  AreaPosition:'',
                  SteelWeight:'',
                  SteelAllWeight:'5000',
                  GrossWeight:'',
                  GrossAllWeight:7350,
                  RowNo:4,
                  Unit:''
                },
                {
                  SteelName:'备注',
                  SteelAmount:5,
                  SteelSpec:'',
                  SteelLength:'',
                  AreaPosition:'',
                  SteelWeight:'',
                  SteelAllWeight:'5000',
                  GrossWeight:'',
                  GrossAllWeight:7350,
                  RowNo:4,
                  Unit:'',
                  IsRemark:true
                }
              ])
            },
            editable: true,
            columnDisplayEditable: true,//列显示是否能编辑
            columnDisplayIndexEditable: true,//列顺序显示是否能编辑
            columnTitleEditable: true,//列标题是否能编辑
            columnResizable: true, //列宽是否能调整
            columnAlignEditable: true,//列对齐是否调整
            isEnableEditField: true, //编辑字段
            isEnableContextMenu: true, //开启右键菜单 默认true
            isEnableInsertRow: true, //插入行
            isEnableDeleteRow: true, //删除行
            isEnableInsertColumn: true, //插入列
            isEnableDeleteColumn: true, //删除列
            isEnableMergeCell: true, //合并单元格
            columns: [
              [
                {title: '序号', align: 'center', field: 'RowNo', width: 80, checked: false, drag:false, isDrag:false},
                {title: '构件/包名称', align: 'center', field: 'SteelName', width: 140, checked: true, drag:false, isDrag:false},
                {title: '规格', align: 'center', field: 'SteelSpec', width: 100, checked: true, isDrag:false},
                {title: '长度', align: 'center', field: 'SteelLength', width: 80, checked: true},
                {title: '数量', align: 'center', field: 'SteelAmount', width: 80, checked: true},
                {title: '单重（kg）', align: 'center', field: 'SteelWeight', width: 110, checked: true},
                {title: '总重（kg）', align: 'center', field: 'SteelAllWeight', width: 110, checked: true},
                {title: '单毛重（kg）', align: 'center', field: 'GrossWeight', width: 120, checked: false},
                {title: '总毛重（kg）', align: 'center', field: 'GrossAllWeight', width: 120, checked: false},
                {title: '备注（构件清单备注信息）', align: 'center', field: 'ComponentDetails', width: 220, checked: true},
                {title: '备注（要货区域）', align: 'center', field: 'AreaPosition', width: 160, checked: true},
              ],
            ],
            // @index 列index；@col 列配置；@data 列数据
            rowsColumnsMerge: function (data, col, index) {
              // 返回一个数组,参数一为行（rowspan）合并数,参数二为列（colspan）合并数, 被合并的行或者列值设为0
              // 处理包名行
              if(col.field === 'SteelSpec' && data.IsPackage){
                return [1,100]
              }
              if((col.field !== 'RowNo' && col.field !== 'SteelName' ) && data.IsPackage){
                return [1,0]
              }
              // 处理备注行
              if(index===1 && data.IsRemark){
                return [1,100]
              }
              if(index!==0 && data.IsRemark){
                return [1,0]
              }
            },

          },
        ]),
        new hiprint.PrintElementTypeGroup("辅助", [
          {
            tid: 'Shipment.hline',
            title: '横线',
            type: 'hline',
            options:{
              field:'hline',
            }
          },
          {
            field: 'vline',
            tid: 'Shipment.vline',
            title: '竖线',
            type: 'vline',
            options:{
              field:'vline',
            }
          },
          {
            field: 'rect',
            tid: 'Shipment.rect',
            title: '矩形',
            type: 'rect',
            options:{
              field:'rect',
            }
          },
          {
            field: 'oval',
            tid: 'Shipment.oval',
            title: '椭圆',
            type: 'oval',
            options:{
              field:'oval',
            }
          },
        ])
      ]
    );
  };
  return {
    addElementTypes: addElementTypes
  };
};

export default [{
  name: '发货单',
  value: 1,
  f: DeliveryNoteProvider()
}]

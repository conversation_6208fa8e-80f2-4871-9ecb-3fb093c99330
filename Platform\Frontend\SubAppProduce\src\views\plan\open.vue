<template>
  <div v-loading="loading" class="bim-gantt open-plan">
    <div class="tool-bar">
      <span>最近保存：{{
        moment(plan.Modify_Date)
          ? moment(plan.Modify_Date).format('HH:mm')
          : ''
      }}</span>
      <span class="hidden-xs-only">
        <span>WBS切换 <el-switch v-model="wbsmode" /></span>
        <span>前锋线 <el-switch v-model="forwardline" /></span>
        <span>聚光灯 <el-switch v-model="spotlight" /></span>
      </span>

      <span>状态日期
        <div
          style="margin-left:6px;background:#F4F4F4; padding:4px 12px;border-radius:6px;"
        >
          {{
            moment(plan.Cur_Data_Date)
              ? moment(plan.Cur_Data_Date).format('YYYY-MM-DD')
              : ''
          }}
          <i class="el-icon-date" /></div></span>
    </div>
    <div
      id="gantt-chart"
      :class="{ hasbaseline: showBaseLine, hasfowardline: forwardline }"
    />
  </div>
</template>
<script>
import 'element-ui/lib/theme-chalk/display.css'
import * as BGT from '@/components/BimGantt/index'
import '@components/gantt/codebase/dhtmlxgantt.css'
import '@/components/BimGantt/index.scss'
import * as moment from 'moment'
import { GetPlanEntityReadOnly } from '@/api/plan/index'
export default {
  name: 'OpenPlan',
  data() {
    return {
      loading: false,
      wbsmode: false,
      forwardline: false,
      showBaseLine: false,
      spotlight: false,
      spot_range: {}, // 聚光灯范围
      fowards: {
        // 前锋线相关图层id
        overlay: ''
      },
      gantt: null,
      planId: null,
      auth: null,
      plan: {
        Plan_Data: {
          data: [],
          links: []
        }
      },
      gantFilters: new BGT.GantFilters([], 'ONE')
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.planId = to.params.id
    this.auth = to.query.auth
    next()
  },
  computed: {
    ganttData() {
      return {
        data: this.plan.Plan_Data.data,
        links: this.plan.Plan_Data.links
      }
    }
  },
  watch: {
    planId(v) {
      this.resetGantt(v)
      console.log(v)
    },
    wbsmode(nv) {
      this.gantt?.unselectTask()
      if (nv === true) {
        this.gantFilters.checkers.push({
          type: 'field',
          field: 'type',
          value: 'project'
        })
      } else {
        this.gantFilters.checkers = this.gantFilters.checkers.filter(chk => {
          return chk.field !== 'type' || chk.value !== 'project'
        })
      }
      this.gantt?.refreshData()
      this.reRenderGantt()
    },
    spotlight(nv) {
      this.onToggleSpotLight(nv)
    },
    forwardline(nv) {
      if (!nv) {
        this.removeFowardLine()
      } else {
        this.drawFowardLine()
      }
    }
  },
  created() {},
  mounted() {
    this.planId = this.$route.params.id
    this.auth = this.$route.query.auth
  },
  methods: {
    reRenderGantt() {
      if (this.showFowardLine) {
        this.removeFowardLine()
        this.drawFowardLine()
      }
      this.gantt?.render()
      if (this.showSpotLight) {
        setTimeout(() => {
          this.bindSpotMarkerEvents(
            document.querySelector('[data-marker-id=spot-marker]'),
            this.gantt.getMarker('spot-marker')
          )
        }, 30)
      }
    },
    canDrawLine(task, dataDate) {
      // 不绘制前锋线条件
      // 1.wbs 2.实际结束 3.没有目标横道 4.未开始且目标计划开始不早于数据日期;
      if (task.type !== 'task') return false
      if (task.Actual_End_Date) return false
      if (!task.Target_Start_Date) return false
      if (moment(task.Target_Start_Date).isSameOrAfter(dataDate)) return false
      return true
    },
    drawFowardLine() {
      var date = new Date(
        moment(this.plan.Cur_Data_Date ?? this.plan.Plan_Start_Date)
      )

      const scrollstate = this.gantt.getScrollState()
      const overlay = this.gantt.ext.overlay.addOverlay(container => {
        container.style.top = -scrollstate.y + 'px'
        var divDataArea = document.querySelector('.gantt_task_bg')
        const div = document.createElement('div')
        div.style.height = divDataArea.offsetHeight + 'px'
        div.style.width = divDataArea.offsetWidth + 'px'
        container.appendChild(div)
        const tasks = this.gantt.getTaskByTime()
        // -- SVG 方案 --
        let svg = `<svg style="width:${divDataArea.offsetWidth}px;height:${divDataArea.offsetHeight}px;">`
        const daySize = { h: 0, w: 0 }
        const startPos = this.gantt.posFromDate(date)
        tasks.forEach(t => {
          const size = this.gantt.getTaskPosition(t)
          daySize.h = size.height
          daySize.w = size.width
          const taskTargetStart = this.gantt.posFromDate(
            moment(t.Target_Start_Date)
              .startOf('date')
              .toDate()
          )
          const taskTargetEnd = this.gantt.posFromDate(
            moment(t.Target_End_Date)
              .startOf('date')
              .toDate()
          )
          const targetPos =
            (taskTargetEnd - taskTargetStart) * t.Actual_Progress +
            taskTargetStart
          if (this.canDrawLine(t, date)) {
            svg += `<line x1="${startPos}" y1="${size.top +
              size.height +
              4}" x2="${targetPos}" y2="${size.top +
              size.height +
              4 +
              size.height / 2}" style="stroke:rgb(255,0,0);stroke-width:2" />`
            svg += `<line x1="${targetPos}" y1="${size.top +
              size.height +
              size.height / 2 +
              4}" x2="${startPos}" y2="${size.top +
              size.rowHeight}" style="stroke:rgb(255,0,0);stroke-width:2" />`
            // 不绘制小圆环
            // svg += `<circle cx="${targetPos}" cy="${size.top +
            //   size.height +
            //   4 +
            //   size.height /
            //     2}" r="4" stroke="rgb(255,0,0)" stroke-width="2" fill="none" />`
          }
        })

        svg += `</svg>`
        div.innerHTML = svg
        return div
      })
      this.fowards.overlay = overlay
      this.gantt.ext.overlay.showOverlay(overlay)
      this.gantt.render()
    },
    removeFowardLine() {
      const { overlay } = { ...this.fowards }
      this.gantt.ext.overlay.deleteOverlay(overlay)
      // overlay删除当前版本有bug，手动移除元素
      const ele = document.querySelector(`[data-overlay-id="${overlay}"]`)
      ele?.remove()
      this.fowards = {
        overlay: ''
      }
      this.gantt.render()
    },
    onToggleSpotLight(show) {
      if (!show) {
        this.gantt.deleteMarker('spot-marker')
        return this.reRenderGantt()
      }
      // 当前时间线长度
      const dur = this.gantt.getSubtaskDuration()
      // 聚光灯设置
      let spotMarker
      if (this.plan && this.plan.Cur_Data_Date) {
        const today = moment(
          this.spot_range.start || this.plan.Cur_Data_Date
        ).toDate()
        const end =
          this.spot_range.end ||
          this.gantt.calculateEndDate({
            start_date: today,
            duration: dur < 6 ? dur : 6
          })
        spotMarker = this.gantt.addMarker({
          start_date: today,
          end_date: end,
          id: 'spot-marker',
          css: 'spot-marker',
          text: '聚光灯',
          title: `${moment(today).format('YYYY-MM-DD')} ~ ${moment(end).format(
            'YYYY-MM-DD'
          )}`
        })
        this.gantt.render()
        this.bindSpotMarkerEvents(
          document.querySelector('[data-marker-id=spot-marker]'),
          this.gantt.getMarker('spot-marker')
        )
      }
    },
    // 绑定聚光灯事件
    bindSpotMarkerEvents(ele, marker) {
      const leftBar = document.createElement('div')
      leftBar.className = 'l-bar'
      const rightBar = document.createElement('div')
      rightBar.className = 'r-bar'
      ele?.append(leftBar, rightBar)
      const bgW = document.querySelector('.gantt_task_bg')?.offsetWidth
      if (!ele) return
      // console.log(ele, marker)
      rightBar.addEventListener('touchstart', e => {
        e.stopPropagation()
        rightBar.__start_pos = [e.touches[0].pageX, e.touches[0].pageY]
        rightBar.__draging = true
      })
      rightBar.addEventListener('touchend', e => {
        if (rightBar.__draging) this.dragEnd(e)
      })
      rightBar.addEventListener('mouseleave', e => {
        if (rightBar.__draging) this.dragEnd(e)
      })
      rightBar.addEventListener('touchmove', e => {
        if (rightBar.__draging) {
          if (
            Number(ele.style.left.split('px')[0]) +
              10 +
              Number(ele.style.width.split('px')[0]) >
            bgW
          ) {
            ele.style.left = Number(ele.style.left.split('px')[0]) - 2 + 'px'
            return this.dragEnd(e)
          }
          rightBar.__to_pos = [e.touches[0].pageX, e.touches[0].pageY]
          ele.style.width =
            Math.max(
              Number(ele.style.width.split('px')[0]) +
                (rightBar.__to_pos[0] - rightBar.__start_pos[0]),
              60
            ) + 'px'

          rightBar.__start_pos = rightBar.__to_pos
        }
      })

      leftBar.addEventListener('touchstart', e => {
        e.stopPropagation()
        leftBar.__start_pos = [e.touches[0].pageX, e.touches[0].pageY]
        leftBar.__draging = true
      })
      leftBar.addEventListener('touchend', e => {
        if (leftBar.__draging) this.dragEnd(e)
      })
      leftBar.addEventListener('mouseleave', e => {
        if (leftBar.__draging) this.dragEnd(e)
      })
      leftBar.addEventListener('touchmove', e => {
        if (leftBar.__draging) {
          if (Number(ele.style.left.split('px')[0]) < 10) {
            ele.style.left = '10px'
            return this.dragEnd(e)
          }
          leftBar.__to_pos = [e.touches[0].pageX, e.touches[0].pageY]
          const moved = leftBar.__to_pos[0] - leftBar.__start_pos[0]
          ele.style.width =
            Math.max(Number(ele.style.width.split('px')[0]) - moved, 60) + 'px'
          ele.style.left = Number(ele.style.left.split('px')[0]) + moved + 'px'
          leftBar.__start_pos = leftBar.__to_pos
        }
      })
      const days = Math.abs(
        moment(marker.start_date).diff(marker.end_date, 'days')
      )
      // console.log(marker, days)
      const unit = ele.clientWidth / days // 天单元格长度
      ele.addEventListener('touchstart', e => {
        ele.__draging = true
        ele.__start_pos = [e.touches[0].pageX, e.touches[0].pageY]
      })
      ele.addEventListener('touchend', e => {
        if (ele.__draging) this.dragEnd(e)
      })
      ele.addEventListener('mouseleave', e => {
        if (ele.__draging) this.dragEnd(e)
      })
      ele.addEventListener('touchmove', e => {
        if (ele.__draging) {
          if (Number(ele.style.left.split('px')[0]) < 10) {
            ele.style.left = '10px'
            return this.dragEnd(e)
          }
          if (
            Number(ele.style.left.split('px')[0]) +
              10 +
              Number(ele.style.width.split('px')[0]) >
            bgW
          ) {
            ele.style.left = Number(ele.style.left.split('px')[0]) - 2 + 'px'
            return this.dragEnd(e)
          }

          ele.__to_pos = [e.touches[0].pageX, e.touches[0].pageY]
          ele.style.left =
            Number(ele.style.left.split('px')[0]) +
            (ele.__to_pos[0] - ele.__start_pos[0]) +
            'px'
          // 按天移动
          // if (Math.abs(ele.__to_pos[0] - ele.__start_pos[0]) > unit / 2) {
          //   const m = mod.gantt.getMarker(spotMarker)
          //   m.start_date = mod.gantt.calculateEndDate({
          //     start_date: m.start_date,
          //     duration: ele.__to_pos[0] - ele.__start_pos[0] > 0 ? 1 : -1
          //   })
          //   m.end_date = mod.gantt.calculateEndDate({
          //     start_date: m.end_date,
          //     duration: ele.__to_pos[0] - ele.__start_pos[0] > 0 ? 1 : -1
          //   })
          //   mod.gantt.updateMarker(spotMarker)
          //   bindSpotMarkerEvents(
          //     document.querySelector('[data-marker-id=spot-marker]'),
          //     marker
          //   )
          //   dragEnd(e)
          // }
          ele.__start_pos = ele.__to_pos
        }
      })

      const ms = document.querySelectorAll('.gantt_marker_area')
      ms[ms.length - 1].style.pointerEvents = 'none'
      ele.style.pointerEvents = 'initial'
    },
    dragEnd(e) {
      const ele = document.querySelector('[data-marker-id=spot-marker]')
      e.target.__draging = false
      e.target.__start_pos = null
      e.target.__to_pos = null
      const s_date = this.gantt.dateFromPos(
        Number(ele.style.left.split('px')[0])
      )
      const e_date = this.gantt.dateFromPos(
        Number(ele.style.left.split('px')[0]) +
          Number(ele.style.width.split('px')[0])
      )
      const m = this.gantt.getMarker('spot-marker')
      m.start_date = s_date
      m.end_date = e_date
      this.spot_range.start = m.start_date
      this.spot_range.end = m.end_date
      this.gantt.updateMarker('spot-marker')
      this.gantt.render()
      this.bindSpotMarkerEvents(
        document.querySelector('[data-marker-id=spot-marker]'),
        this.gantt.getMarker('spot-marker')
      )
      console.log('drag end.....')
    },
    moment(v) {
      if (!v) return ''
      return moment(v)
    },
    loadPlan(id) {
      return GetPlanEntityReadOnly(id, this.auth)
    },
    resetGantt(plan_id) {
      this.loadPlan(plan_id).then(res => {
        if (res.IsSucceed) {
          if (
            this.plan.Target_Plan_Id !== null &&
            this.plan.Target_Plan_Id !== undefined &&
            this.plan.Target_Plan_Id !== ''
          ) {
            this.showBaseLine = true
          } else {
            this.showBaseLine = false
          }
          this.plan = res.Data
          BGT.createGanttInstance(this, {
            el: 'gantt-chart',
            locale: 'cn',
            editMode: false,
            filters: this.gantFilters,
            gridWidth: 200,
            gridColumns: BGT.ALL_TASK_COLUMNS,
            showBaseLine: this.showBaseLine
          })
        } else {
          this.$message.warning(res.Message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.open-plan {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-flow: column;
  color: #888;
  font-size: 0.9em;
  .tool-bar {
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    span {
      margin: 0 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      .el-switch {
        margin: 0 4px;
      }
    }
  }
  #gantt-chart {
    flex: auto;
  }
}
</style>

<template>
  <div class="plan-gantt">
    <el-card shadow="no">
      <el-container>
        <el-header>
          <el-row :gutter="20">
            <el-col :span="18">
              <div class="proj-title">
                <div class="flag">
                  <i class="iconfont icon-gantt" />
                </div>
                <h3>绍兴国际会展中心EPC工程总承包项目V4.5</h3>
                <el-link
                  class="set-icon"
                  :underline="false"
                  type="primary"
                  icon="el-icon-setting"
                />
              </div>
            </el-col>
            <el-col :span="6" style="text-align:right;">
              <el-button type="success">导出</el-button>
              <el-button type="primary">保存</el-button>
              <el-button
                type="primary"
                icon="el-icon-s-check"
              >提交审核</el-button>
            </el-col>
          </el-row>
        </el-header>
        <el-main>
          <div class="gantt-toolbar">
            TOOLBAR
          </div>
          <div id="gantt" style="" />
        </el-main>
      </el-container>
    </el-card>
  </div>
</template>
<script>
import './plan.scss'
import { gantt, Gantt } from '@components/gantt'
import '@components/gantt/codebase/dhtmlxgantt.css'

export default {
  name: 'PlanEditor',
  data() {
    return {
      taskData: {
        data: [
          {
            id: '1',
            start_date: '2018-04-01 00:00:00',
            duration: '5',
            text: 'Project #1',
            progress: '0.8',
            parent: '0',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-01 00:00:00',
            planned_end: '2018-04-07 00:00:00',
            open: 1
          },
          {
            id: '2',
            start_date: '2018-04-06 00:00:00',
            duration: '4',
            text: 'Task #1',
            progress: '0.5',
            parent: '1',
            deadline: '2018-04-11 00:00:00',
            planned_start: '2018-04-06 00:00:00',
            planned_end: '2018-04-10 00:00:00',
            open: 1
          },
          {
            id: '3',
            start_date: '2018-04-03 00:00:00',
            duration: '6',
            text: 'Task #2',
            progress: '0.7',
            parent: '1',
            deadline: '2018-04-10 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-14 00:00:00',
            open: 1
          },
          {
            id: '4',
            start_date: '2018-04-07 00:00:00',
            duration: '2',
            text: 'Task #3',
            progress: '0',
            parent: '1',
            deadline: '2018-04-17 00:00:00',
            planned_start: '2018-04-03 00:00:00',
            planned_end: '2018-04-05 00:00:00',
            open: 1
          },
          {
            id: '5',
            start_date: '2018-04-05 00:00:00',
            duration: '5',
            text: 'Task #1.1',
            progress: '0.34',
            parent: '2',
            deadline: '2018-04-10 00:00:00',
            planned_start: '2018-04-03 00:00:00',
            planned_end: '2018-04-08 00:00:00',
            open: 1
          },
          {
            id: '6',
            start_date: '2018-04-11 00:00:00',
            duration: '4',
            text: 'Task #1.2',
            progress: '0.491477',
            parent: '2',
            deadline: '2018-04-15 00:00:00',
            planned_start: '2018-04-11 00:00:00',
            planned_end: '2018-04-16 00:00:00',
            open: 1
          },
          {
            id: '7',
            start_date: '2018-04-07 00:00:00',
            duration: '5',
            text: 'Task #2.1',
            progress: '0.2',
            parent: '3',
            deadline: '2018-04-11 00:00:00',
            planned_start: '2018-04-07 00:00:00',
            planned_end: '2018-04-12 00:00:00',
            open: 1
          },
          {
            id: '8',
            start_date: '2018-04-06 00:00:00',
            duration: '4',
            text: 'Task #2.2',
            progress: '0.9',
            parent: '3',
            deadline: '2018-04-16 00:00:00',
            planned_start: '2018-04-06 00:00:00',
            planned_end: '2018-04-10 00:00:00',
            open: 1
          },
          {
            id: '9',
            start_date: '2018-04-06 00:00:00',
            duration: '5',
            text: 'Task #3.1',
            progress: '1',
            parent: '4',
            deadline: '2018-04-16 00:00:00',
            planned_start: '2018-04-06 00:00:00',
            planned_end: '2018-04-11 00:00:00',
            open: 1
          },
          {
            id: '10',
            start_date: '2018-04-06 00:00:00',
            duration: '3',
            text: 'Task #3.2',
            progress: '0',
            parent: '4',
            deadline: '2018-04-11 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-08 00:00:00',
            open: 1
          },
          {
            id: '11',
            start_date: '2018-04-06 00:00:00',
            duration: '4',
            text: 'Task #3.3',
            progress: '0.33',
            parent: '4',
            deadline: '2018-04-10 00:00:00',
            planned_start: '2018-04-07 00:00:00',
            planned_end: '2018-04-11 00:00:00',
            open: 1
          },
          {
            id: '12',
            start_date: '2018-04-02 00:00:00',
            duration: '8',
            text: 'Project #2',
            progress: '0',
            parent: '0',
            deadline: '2018-04-04 00:00:00',
            planned_start: '2018-04-02 00:00:00',
            planned_end: '2018-04-20 00:00:00',
            open: 1
          },
          {
            id: '13',
            start_date: '2018-04-02 00:00:00',
            duration: '10',
            text: 'Task #1',
            progress: '0.2',
            parent: '12',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-02 00:00:00',
            planned_end: '2018-04-12 00:00:00',
            open: 1
          },
          {
            id: '14',
            start_date: '2018-04-04 00:00:00',
            duration: '4',
            text: 'Task #2',
            progress: '0.9',
            parent: '12',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-04 00:00:00',
            planned_end: '2018-04-08 00:00:00',
            open: 1
          },
          {
            id: '15',
            start_date: '2018-04-05 00:00:00',
            duration: '3',
            text: 'Task #3',
            progress: '0.6',
            parent: '12',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-08 00:00:00',
            open: 1
          },
          {
            id: '16',
            start_date: '2018-04-01 00:00:00',
            duration: '3',
            text: 'Task #4',
            progress: '0.214286',
            parent: '12',
            deadline: '2018-04-05 00:00:00',
            planned_start: '2018-04-01 00:00:00',
            planned_end: '2018-04-04 00:00:00',
            open: 1
          },
          {
            id: '17',
            start_date: '2018-04-06 00:00:00',
            duration: '6',
            text: 'Task #5',
            progress: '0.5',
            parent: '12',
            deadline: '2018-04-12 00:00:00',
            planned_start: '2018-04-06 00:00:00',
            planned_end: '2018-04-12 00:00:00',
            open: 1
          },
          {
            id: '18',
            start_date: '2018-04-05 00:00:00',
            duration: '5',
            text: 'Task #2.1',
            progress: '0.3',
            parent: '14',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-07 00:00:00',
            planned_end: '2018-04-12 00:00:00',
            open: 1
          },
          {
            id: '19',
            start_date: '2018-04-05 00:00:00',
            duration: '6',
            text: 'Task #2.2',
            progress: '0.453052',
            parent: '14',
            deadline: '2018-04-09 00:00:00',
            planned_start: '2018-04-08 00:00:00',
            planned_end: '2018-04-14 00:00:00',
            open: 1
          },
          {
            id: '20',
            start_date: '2018-04-05 00:00:00',
            duration: '4',
            text: 'Task #2.3',
            progress: '0.512605',
            parent: '14',
            deadline: '2018-04-08 00:00:00',
            planned_start: '2018-04-03 00:00:00',
            planned_end: '2018-04-07 00:00:00',
            open: 1
          },
          {
            id: '21',
            start_date: '2018-04-05 00:00:00',
            duration: '6',
            text: 'Task #2.4',
            progress: '0.7',
            parent: '14',
            deadline: '2018-04-14 00:00:00',
            planned_start: '2018-04-07 00:00:00',
            planned_end: '2018-04-13 00:00:00',
            open: 1
          },
          {
            id: '22',
            start_date: '2018-04-05 00:00:00',
            duration: '7',
            text: 'Task #4.1',
            progress: '1',
            parent: '16',
            deadline: '2018-04-15 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-12 00:00:00',
            open: 1
          },
          {
            id: '23',
            start_date: '2018-04-05 00:00:00',
            duration: '5',
            text: 'Task #4.2',
            progress: '1',
            parent: '16',
            deadline: '2018-04-11 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-10 00:00:00',
            open: 1
          },
          {
            id: '24',
            start_date: '2018-04-05 00:00:00',
            duration: '5',
            text: 'Task #4.3',
            progress: '0',
            parent: '16',
            deadline: '2018-04-12 00:00:00',
            planned_start: '2018-04-05 00:00:00',
            planned_end: '2018-04-10 00:00:00',
            open: 1
          }
        ],
        collections: {
          links: [
            {
              id: '1',
              source: '1',
              target: '2',
              type: '0'
            },
            {
              id: '2',
              source: '1',
              target: '3',
              type: '0'
            },
            {
              id: '3',
              source: '1',
              target: '4',
              type: '0'
            },
            {
              id: '4',
              source: '2',
              target: '6',
              type: '0'
            }
          ]
        }
      }
    }
  },
  computed: {
    // 进度页面，mode 0 为查看模式，1 为编辑模式
    mode() {
      return Number(this.$route.query.mode)
    }
  },
  created() {
    console.log('plan created....')
    this.getRouteInfo()
  },
  mounted() {
    gantt.config.date_format = '%Y-%m-%d %H:%i:%s'
    // gantt.services.scales = [
    //   { unit: 'month', step: 1, format: '%M' },
    //   { unit: 'year', step: 1, format: '%Y' },
    //   { unit: 'day', format: '%d %M' }
    // ]
    var zoomConfig = {
      levels: [
        {
          name: 'day',
          scale_height: 27,
          min_column_width: 80,
          scales: [{ unit: 'day', step: 1, format: '%d %M' }]
        },
        {
          name: 'week',
          scale_height: 50,
          min_column_width: 50,
          scales: [
            {
              unit: 'week',
              step: 1,
              format: function(date) {
                var dateToStr = gantt.date.date_to_str('%d %M')
                var endDate = gantt.date.add(date, 6, 'day')
                var weekNum = gantt.date.date_to_str('%W')(date)
                return (
                  '#' +
                  weekNum +
                  ', ' +
                  dateToStr(date) +
                  ' - ' +
                  dateToStr(endDate)
                )
              }
            },
            { unit: 'day', step: 1, format: '%j %D' }
          ]
        },
        {
          name: 'month',
          scale_height: 50,
          min_column_width: 120,
          scales: [
            { unit: 'month', format: '%F, %Y' },
            { unit: 'week', format: 'Week #%W' }
          ]
        },
        {
          name: 'quarter',
          height: 50,
          min_column_width: 90,
          scales: [
            { unit: 'month', step: 1, format: '%M' },
            {
              unit: 'quarter',
              step: 1,
              format: function(date) {
                var dateToStr = gantt.date.date_to_str('%M')
                var endDate = gantt.date.add(
                  gantt.date.add(date, 3, 'month'),
                  -1,
                  'day'
                )
                return dateToStr(date) + ' - ' + dateToStr(endDate)
              }
            }
          ]
        },
        {
          name: 'year',
          scale_height: 50,
          min_column_width: 30,
          scales: [{ unit: 'year', step: 1, format: '%Y' }]
        }
      ]
    }

    gantt.ext.zoom.init(zoomConfig)
    gantt.config.scale_height = 3 * 28
    gantt.config.bar_height = 16
    gantt.config.row_height = 40
    gantt.config.columns = [
      { name: 'text', label: 'Task name', tree: true, width: '*' },
      { name: 'start_date', label: 'Start time', align: 'center' },
      { name: 'duration', label: 'Dynamic_Duration', align: 'center' },
      { name: 'add', label: '' }
    ]
    setTimeout(() => {
      gantt.config.columns = [
        { name: 'text', label: 'Task name', tree: true, width: '*' },
        { name: 'start_date', label: 'Start time', align: 'center' },
        { name: 'duration', label: 'Dynamic_Duration', align: 'center' }
      ]
      console.log('update...')
    }, 10000)
    gantt.addTaskLayer({
      renderer: {
        render: function draw_planned(task) {
          if (task.planned_start && task.planned_end) {
            var sizes = gantt.getTaskPosition(
              task,
              task.planned_start,
              task.planned_end
            )
            var el = document.createElement('div')
            el.className = 'baseline'
            el.style.left = sizes.left + 'px'
            el.style.width = sizes.width + 'px'
            el.style.top = sizes.top + gantt.config.bar_height + 13 + 'px'
            return el
          }
          return false
        }
      }
    })
    gantt.templates.task_class = function(start, end, task) {
      if (task.planned_end) {
        var classes = ['has-baseline']
        if (end.getTime() > task.planned_end.getTime()) {
          classes.push('overdue')
        }
        return classes.join(' ')
      }
    }
    gantt.templates.rightside_text = function(start, end, task) {
      if (task.planned_end) {
        if (end.getTime() > task.planned_end.getTime()) {
          var overdue = Math.ceil(
            Math.abs(
              (end.getTime() - task.planned_end.getTime()) /
                (24 * 60 * 60 * 1000)
            )
          )
          var text = '<b>Overdue: ' + overdue + ' days</b>'
          return text
        }
      }
    }
    gantt.attachEvent('onTaskLoading', function(task) {
      task.planned_start = gantt.date.parseDate(task.planned_start, 'xml_date')
      task.planned_end = gantt.date.parseDate(task.planned_end, 'xml_date')
      return true
    })
    // gantt.init('gantt')
    // gantt.parse(this.taskData)
    const _this = this
    gantt.ext.zoom.attachEvent('onAfterZoom', (level, config) => {
      console.log('zoom finish...')
      setTimeout(() => {
        this.BindScaleEvents()
      }, 100)
    })
    this.BindScaleEvents()
  },
  methods: {
    BindScaleEvents() {
      const gantElem = this.$el.querySelector('.gantt_task_scale')
      // console.log(gantElem)
      let moveXTotal = 0
      gantElem.addEventListener('mouseleave', function(e) {
        gantElem.removeEventListener('mousemove', gmove)
        moveXTotal = 0
        gantElem.style.cursor = 'default'
      })
      gantElem.addEventListener('mousedown', function(e) {
        if (e.which === 1) {
          // console.log(e)
          gantElem.style.cursor = 'e-resize'
          gantElem.addEventListener('mousemove', gmove)
        }
      })
      gantElem.addEventListener('mouseup', function(e) {
        if (e.which === 1) {
          // console.log(e)
          gantElem.removeEventListener('mousemove', gmove)
          moveXTotal = 0
          gantElem.style.cursor = 'default'
        }
      })
      function gmove(e) {
        // console.log(e)
        moveXTotal += e.movementX
        console.log(moveXTotal)
        if (moveXTotal < -100) {
          gantt.ext.zoom.zoomOut()
          gantElem.removeEventListener('mousemove', gmove)
          moveXTotal = 0
        }
        if (moveXTotal > 100) {
          gantt.ext.zoom.zoomIn()
          gantElem.removeEventListener('mousemove', gmove)
          moveXTotal = 0
        }
      }
    },
    getRouteInfo() {
      console.log(this.$route)
    }
  }
}
</script>

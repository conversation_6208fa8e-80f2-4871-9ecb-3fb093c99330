<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div class="cs-z-page-main-content">
      <el-form style="width: 782px" inline>
        <el-row v-for="item in nodeData" :key="item.Plan_Type" :gutter="50">
          <el-form-item :label="item.Plan_Name||' '" label-width="100px">
            <el-input v-model="item.Plan_Name" style="width:150px" clearable />
          </el-form-item>
          <el-form-item label="计划下发通知人" label-width="150px">
            <el-select
              v-model="item.Plan_Notice_Userids"
              placeholder="请选择"
              style="width: 400px"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="item in userList"
                :key="item.Id"
                :label="item.Display_Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row style="text-align: right;margin-top: 20px">
          <el-button v-if="nodeData&&nodeData.length" type="primary" @click="saveConfig">保存</el-button>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import { GetConfigs, SaveConfigs } from '@/api/plm/projects'
import { GetUserPage } from '@/api/sys'

export default {
  data() {
    return {
      userList: [],
      nodeData: [],
      companyId: localStorage.getItem('CurReferenceId')
    }
  },
  async created() {
    await this.getList()
    this.getConfigList()
  },
  methods: {
    async getList() {
      const res = await GetUserPage({
        DepartmentId: this.departmentId,
        PageSize: 10000
      })
      this.userList = res.Data.Data.filter(item => item.UserStatusName === '正常')
    },
    getConfigList() {
      GetConfigs({
        CompanyId: this.companyId
      }).then(res => {
        this.$set(this, 'nodeData', res.Data)
      })
    },
    saveConfig() {
      SaveConfigs(this.nodeData).then(res => {
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.getConfigList()
        } else {
          this.$message.error(res.Message)
        }
      })
    }
  }
}
</script>


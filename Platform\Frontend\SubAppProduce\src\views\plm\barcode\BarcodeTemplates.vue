<template>
  <div v-loading="loading" class="abs100 cs-z-flex-pd16-wrap">
    <div style="display:flex;height:100%;">
      <el-container>
        <el-aside
          class="cs-z-page-main-content"
          style="background:#FFF;margin-right:16px;width:320px;"
        >
          <el-row :gutter="4" style="flex-shrink:0;">
            <el-col :span="17">
              <el-input
                v-model="keyword"
                placeholder="请输入内容"
                suffix-icon="el-icon-search"
              />
            </el-col>
            <el-col :span="7">
              <el-button type="primary" @click="createTmpl">新建模板</el-button>
            </el-col>
          </el-row>
          <div class="tmpl-list">
            <el-menu
              class="tmpl-menu"
              :default-active="String(activeIndex)"
              @select="handleSelect"
            >
              <el-menu-item
                v-for="tmpl in filteredTmplList"
                :key="tmpl.Id"
                :index="tmpl.Id"
                style="padding-left:12px;"
                :title="tmpl.Name"
              >
                <div
                  style="overflow:hidden;max-width:220px;text-overflow: ellipsis;"
                  @click.stop="tmplSelect(tmpl.Id)"
                >
                  <i class="el-icon-document" />{{ tmpl.Name }}
                </div>
                <template v-if="String(activeIndex) === tmpl.Id">
                  <el-link
                    :underline="false"
                    type="primary"
                    @click.stop="toEdit = tmpl.Id"
                  >
                    <i class="right-align-icon el-icon-edit" />
                  </el-link>
                  <el-link
                    :underline="false"
                    type="danger"
                    @click="onDelete(tmpl.Id)"
                  >
                    <i class="right-align-icon el-icon-delete" />
                  </el-link>
                </template>
              </el-menu-item>
            </el-menu>
          </div>
        </el-aside>
        <el-container v-if="form" class="cs-z-page-main-content">
          <el-header style="border-bottom:1px solid #DDD;" height="100px">
            <el-form
              ref="baseForm"
              :model="form"
              :label-width="'80px'"
              :rules="rules"
            >
              <el-row :gutter="12">
                <el-col :span="5">
                  <el-form-item label="模板布局" prop="TemplateFile" required>
                    <el-select
                      v-model="form.TemplateFile"
                      placeholder="请选择"
                      :disabled="!Boolean(toEdit)"
                    >
                      <el-option label="默认" value="Barcode" />
                      <el-option label="定制1(XF11x5)" value="XFBarcode" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="条码名称" prop="Name" required>
                    <el-input
                      v-model="form.Name"
                      :disabled="!Boolean(toEdit)"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="语言标记">
                    <el-select
                      v-model="form.CallBackFunction"
                      placeholder="请选择"
                      :disabled="!Boolean(toEdit)"
                    >
                      <el-option label="简体中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="网页地址">
                    <el-input
                      v-model="form.WebUrl"
                      :disabled="!Boolean(toEdit)"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="12">
                <el-col :span="7">
                  <el-form-item label="条码类型">
                    <el-select
                      v-model="form.Type"
                      placeholder="请选择"
                      :disabled="!Boolean(toEdit)"
                      @change="barTypeChange($event)"
                    >
                      <el-option
                        v-for="t in BarTypes"
                        :key="t.Id"
                        :label="t.Display_Name"
                        :value="t.Value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item label="效果图">
                    <el-input
                      v-model="form.PreviewImage"
                      placeholder="300*200px"
                      :disabled="!Boolean(toEdit)"
                    >
                      <OSSUpload
                        slot="append"
                        class="upload-demo"
                        :action="$store.state.uploadUrl"
                        accept="image/*"
                        :on-success="previewUploadSucceed"
                        :show-file-list="false"
                      >
                        <el-button
                          v-if="Boolean(toEdit)"
                          size="mini"
                          type="primary"
                          icon="el-icon-upload"
                        >点击上传</el-button>
                      </OSSUpload>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="纸张(毫米)">
                    <div style="display:flex;justify-content: space-between;">
                      <span>长：</span><el-input
                        v-model.number="form.Length"
                        :min="0"
                        type="number"
                        style="width:80px;"
                        :disabled="!Boolean(toEdit)"
                      />
                      <span>宽：</span><el-input
                        v-model.number="form.Width"
                        :min="0"
                        type="number"
                        style="width:80px;"
                        :disabled="!Boolean(toEdit)"
                      />
                      <el-button
                        type="primary"
                        :icon="
                          advSetShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                        "
                        @click="advSetShow = !advSetShow"
                      >高级设置</el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-header>
          <el-form
            v-if="advSetShow"
            class="adv-set"
            style="background:rgb(245, 246, 248);margin-top:12px;padding:16px;padding-bottom:0;"
          >
            <el-row :gutter="16" class="dir-row">
              <el-col :span="12">
                <el-form-item label="样式配置">
                  <el-input
                    v-model="form.CssStyle"
                    :disabled="!Boolean(toEdit)"
                    placeholder="标签边距及图片显示方向，如:
{
  &quot;top&quot;:&quot;1mm&quot;,
  &quot;bottom&quot;:&quot;1mm&quot;,
  &quot;left&quot;:&quot;1mm&quot;,
  &quot;right&quot;:&quot;1mm&quot;,
  &quot;direction&quot;:&quot;ltr&quot;
}
ltr表示从左向右，rtl表示从右向左"
                    type="textarea"
                    :rows="4"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自定义配置">
                  <el-input
                    v-model="form.CustomSetting"
                    :disabled="!Boolean(toEdit)"
                    type="textarea"
                    :rows="4"
                    placeholder="Logo及二维码图片大小，如:
{
  &quot;logo&quot;:{
    &quot;width&quot;:&quot;40mm&quot;,
    &quot;height&quot;:&quot;12mm&quot;,
    &quot;align&quot;:&quot;left&quot;
  },
  &quot;barcode&quot;:{
    &quot;width&quot;:&quot;20mm&quot;,
    &quot;height&quot;:&quot;20mm&quot;
  }
}"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-main style="background:#F5F6F8;">
            <div class="normal-set">
              <div
                v-if="form && form.TemplateFile === 'Barcode'"
                class="img-set"
                :style="{
                  direction:
                    CssStyleObject && CssStyleObject.direction === 'rtl'
                      ? 'rtl'
                      : 'ltr'
                }"
              >
                <div class="qrimg">
                  <div class="avatar-uploader">
                    <div class="el-upload">
                      <el-link
                        class="avatar-uploader-icon"
                        style="cursor: context-menu;"
                        :underline="false"
                      >此处显示二维码</el-link>
                    </div>
                  </div>
                </div>
                <span
                  class="trans-icon"
                  title="交换位置"
                ><i
                  class="el-icon-sort"
                  @click="swapDirection"
                /></span>
                <div class="logoimg" style="direction:ltr;">
                  <OSSUpload
                    class="logo-uploader"
                    :show-file-list="false"
                    :on-success="handleLogoSuccess"
                    :action="$store.state.uploadUrl"
                    accept="image/*"
                    :disabled="!Boolean(toEdit)"
                  >
                    <img
                      v-if="form.LogoFileUrl"
                      :src="form.LogoFileUrl"
                      class="avatar"
                      :style="{
                        width:
                          CustomSettingObject.logo &&
                          CustomSettingObject.logo.width
                            ? CustomSettingObject.logo.width
                            : 'auto',
                        height:
                          CustomSettingObject.logo &&
                          CustomSettingObject.logo.height
                            ? CustomSettingObject.logo.height
                            : '238px'
                      }"
                    >
                    <el-link
                      v-else
                      icon="el-icon-plus"
                      class="avatar-uploader-icon"
                      :underline="false"
                    >此处显示公司LOGO</el-link>
                  </OSSUpload>
                </div>
              </div>
              <div class="fields-set">
                <div
                  v-if="form && form.TemplateFile === 'Barcode'"
                  class="rows"
                >
                  <el-row v-for="(r, ri) in fillrows" :key="ri" :gutter="24">
                    <el-col :span="4">
                      <div class="f-row">
                        <el-select
                          v-model="r.colLength"
                          placeholder="请选择"
                          style="width:98px;"
                          :disabled="!Boolean(toEdit)"
                          @change="changeCols(r, $event)"
                        >
                          <el-option label="1列" :value="1" />
                          <el-option label="2列" :value="2" />
                        </el-select>
                      </div>
                    </el-col>
                    <el-col :span="9">
                      <div class="f-row">
                        <el-select
                          v-model="r.leftFieldName"
                          placeholder="请选择"
                          style="flex:auto;"
                          :disabled="!Boolean(toEdit)"
                          @change="rowFeildChange(r, 'leftFieldName', $event)"
                        >
                          <el-option
                            v-for="f in subFields"
                            :key="f.Id"
                            :label="f.Display_Name"
                            :value="f.Value"
                          />
                        </el-select>
                      </div>
                    </el-col>
                    <el-col :span="9">
                      <div class="f-row">
                        <el-select
                          v-if="r.colLength === 2 || r.rightFieldName"
                          v-model="r.rightFieldName"
                          placeholder="请选择"
                          style="flex:auto;"
                          :disabled="!Boolean(toEdit)"
                          @change="rowFeildChange(r, 'rightFieldName', $event)"
                        >
                          <el-option
                            v-for="f in subFields"
                            :key="f.Id"
                            :label="f.Display_Name"
                            :value="f.Value"
                          />
                        </el-select>
                      </div>
                    </el-col>
                    <el-col :span="2">
                      <div class="f-row">
                        <el-link
                          v-if="ri === fillrows.length - 1 && toEdit"
                          :underline="false"
                          icon="el-icon-circle-plus-outline"
                          type="primary"
                          style="margin:6px 0 0 12px;font-size:1.2em;"
                          @click="addRow"
                        />
                        <el-link
                          v-if="(ri !== 0 || fillrows.length > 1) && toEdit"
                          :underline="false"
                          icon="el-icon-remove-outline"
                          type="danger"
                          style="margin:6px 0 0 12px;font-size:1.2em;"
                          @click="removeRow(ri)"
                        />
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <div v-else class="rows" style="margin-top:20px;">
                  <el-row v-for="i in 2" :key="i" :gutter="24">
                    <el-col v-if="i === 1" :span="24">
                      <div
                        style="height:88px;border:1px solid #DDD; display:flex;align-items:center;padding:20px;"
                      >
                        <span
                          style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                        >属性1:</span>
                        <el-select
                          v-model="fillrows[0].leftFieldName"
                          placeholder="选择属性1"
                          style="flex:auto;"
                          :disabled="!Boolean(toEdit)"
                          @change="
                            rowFeildChange(fillrows[0], 'leftFieldName', $event)
                          "
                        >
                          <el-option
                            v-for="f in subFields"
                            :key="f.Id"
                            :label="f.Display_Name"
                            :value="f.Value"
                          />
                        </el-select>
                      </div>
                    </el-col>
                    <template v-if="i === 2">
                      <el-col :span="14">
                        <div style="margin-top:20px;">
                          <el-row>
                            <el-col :span="24" style="margin-bottom:12px;">
                              <span
                                style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                              >属性2:</span>
                              <el-select
                                v-model="fillrows[0].rightFieldName"
                                placeholder="选择属性2"
                                style="flex:auto;"
                                :disabled="!Boolean(toEdit)"
                                @change="
                                  rowFeildChange(
                                    fillrows[0],
                                    'rightFieldName',
                                    $event
                                  )
                                "
                              >
                                <el-option
                                  v-for="f in subFields"
                                  :key="f.Id"
                                  :label="f.Display_Name"
                                  :value="f.Value"
                                />
                              </el-select>
                            </el-col>
                          </el-row>
                          <el-row>
                            <el-col :span="24" style="margin-bottom:12px;">
                              <span
                                style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                              >属性3:</span>
                              <el-select
                                v-model="fillrows[1].leftFieldName"
                                placeholder="选择属性3"
                                style="flex:auto;"
                                :disabled="!Boolean(toEdit)"
                                @change="
                                  rowFeildChange(
                                    fillrows[1],
                                    'leftFieldName',
                                    $event
                                  )
                                "
                              >
                                <el-option
                                  v-for="f in subFields"
                                  :key="f.Id"
                                  :label="f.Display_Name"
                                  :value="f.Value"
                                />
                              </el-select>
                            </el-col>
                          </el-row>
                          <el-row :gutter="8">
                            <el-col :span="12">
                              <span
                                style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                              >属性4:</span>
                              <el-select
                                v-model="fillrows[1].rightFieldName"
                                placeholder="选择属性4"
                                style="flex:auto;"
                                :disabled="!Boolean(toEdit)"
                                @change="
                                  rowFeildChange(
                                    fillrows[1],
                                    'rightFieldName',
                                    $event
                                  )
                                "
                              >
                                <el-option
                                  v-for="f in subFields"
                                  :key="f.Id"
                                  :label="f.Display_Name"
                                  :value="f.Value"
                                />
                              </el-select>
                            </el-col>
                            <el-col :span="12">
                              <span
                                style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                              >属性5:</span>
                              <el-select
                                v-model="fillrows[2].leftFieldName"
                                placeholder="选择属性5"
                                style="flex:auto;"
                                :disabled="!Boolean(toEdit)"
                                @change="
                                  rowFeildChange(
                                    fillrows[2],
                                    'leftFieldName',
                                    $event
                                  )
                                "
                              >
                                <el-option
                                  v-for="f in subFields"
                                  :key="f.Id"
                                  :label="f.Display_Name"
                                  :value="f.Value"
                                />
                              </el-select>
                            </el-col>
                          </el-row></div></el-col>
                      <el-col
                        :span="5"
                      ><div style="margin-top:20px;margin-bottom:20px;">
                        <div class="qrimg">
                          <div
                            class="avatar-uploader"
                            style="text-align:center;border:none;"
                          >
                            <div
                              class="el-upload"
                              style="width:100px;height:100px; margin:0 auto !important;"
                            >
                              <el-link
                                class="avatar-uploader-icon"
                                style="width:100px;height:100px;cursor: context-menu;font-size:13px;"
                                :underline="false"
                              >二维码</el-link>
                            </div>
                          </div>
                        </div>
                      </div></el-col>
                      <el-col
                        :span="5"
                      ><div style="margin-top:20px;margin-bottom:20px;">
                        <span
                          style="font-size:14px;color:rgba(34, 40, 52, 0.65);margin-right:8px;"
                        >属性6:</span><br>
                        <el-select
                          v-model="fillrows[2].rightFieldName"
                          placeholder="选择属性6"
                          style="flex:auto;"
                          :disabled="!Boolean(toEdit)"
                          @change="
                            rowFeildChange(
                              fillrows[2],
                              'rightFieldName',
                              $event
                            )
                          "
                        >
                          <el-option
                            v-for="f in subFields"
                            :key="f.Id"
                            :label="f.Display_Name"
                            :value="f.Value"
                          />
                        </el-select></div></el-col>
                    </template>
                  </el-row>
                </div>
                <div
                  v-if="form && form.TemplateFile === 'Barcode'"
                  class="remark"
                  style="text-align:right;"
                >
                  <span>*</span><el-input
                    v-model="form.Remark"
                    :disabled="!Boolean(toEdit)"
                    style="width:240px; display:inline-block;margin-left:6px;"
                    size="mini"
                    placeholder="请输入Tips信息"
                  />
                </div>
              </div>
            </div>
          </el-main>
          <el-footer
            height="48px"
            style="background:#f2f5f8;text-align:right;padding-top:12px;"
          >
            <!-- <el-button
              type="primary"
              size="mini"
              @click="printView"
            >全屏预览</el-button> -->
            <el-button
              v-if="toEdit"
              type="primary"
              size="mini"
              :loading="submitLoading"
              @click="submit"
            >保存模板</el-button>
          </el-footer>
        </el-container>
      </el-container>
    </div>
    <el-dialog
      title="打印条码阅览"
      :visible.sync="dialogVisible"
      width="100%"
      :append-to-body="true"
      :fullscreen="true"
      custom-class="print-preview-dialog"
      :center="true"
    >
      <template slot="title">
        <span style="font-size:1.6em;color:#666;">打印条码阅览</span>
      </template>
      <div v-loading="loading" style="height:100%;width:100%;">
        <iframe
          ref="frame"
          src="/singles/print-preview"
          width="100%"
          height="100%"
          frameborder="0"
          @load="frameLoaded"
        />
      </div>
      <template slot="footer">
        <div class="print-tip">
          <div class="tips">
            <strong>注意</strong>：打印之前，请进行扫码确认二维码信息准确无误
          </div>
          <el-button @click="printFrame">打印</el-button>
        </div>
      </template>
    </el-dialog>
    <div style="position: fixed;top: -1000px">{{ markerName }}</div>
  </div>
</template>
<script>
import { Add1, Delete1, Edit1, GetBarCodeSettingEntity, GetEntities, Addprint, GetEntities2 } from '@/api/plm/production/barcode.js'
import { GetDictionaryDetailListByCode, GetDictionaryDetailListByParentId } from '@/api/sys/index'
import { getColumnConfiguration, getPropsName } from '@/utils/multi-specialty'
import { GetOssUrl } from '@/api/sys/index'
import OSSUpload from '@/views/PRO/components/ossupload'
let timer
export default {
  name: 'BarcodeTemplates',
  components: { OSSUpload },
  data() {
    return {
      loading: false,
      submitLoading: false,
      keyword: '',
      BarTypes: [],
      tmplList: [],
      activeIndex: '',
      form: null,
      Attachment: [],
      rules: {
        Name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      ImgDirection: 'ltr',
      advSetShow: false,
      dialogVisible: false,
      fillrows: [{ colLength: 1 }],
      toEdit: '',
      typeCode: '',
      typeId: '',
      markerName: '1',
      mode: ''
    }
  },
  computed: {
    filteredTmplList() {
      return this.tmplList.filter(t => t.Name.indexOf(this.keyword) > -1)
    },
    CssStyleObject() {
      let o
      try {
        o = JSON.parse(this.form.CssStyle)
      } catch (err) {
        // console.error(err)
        // this.form.CssStyle = null
      }
      console.log(Object.prototype.toString.call(o))
      if (Object.prototype.toString.call(o) === '[object Object]') {
        return o
      } else {
        return {}
      }
    },
    CustomSettingObject() {
      console.log('re computed css')
      let o
      try {
        o = JSON.parse(this.form.CustomSetting)
      } catch (err) {
        // console.error(err)
        // this.form.CustomSetting = null
      }
      if (Object.prototype.toString.call(o) === '[object Object]') {
        return o
      } else {
        return { logo: {}, qrcode: {}}
      }
    },
    subFields() {
      return (
        this.BarTypes.find(
          t =>
            t.Id === this.form.Dictionary_Id ||
            String(t.Value) === String(this.form.Type)
        )?.subFields ?? []
      )
    }
  },
  watch: {
    $route(nv) {
      if (this.$route.query.id) {
        this.activeIndex = this.$route.query.id
        this.getEntity(this.$route.query.id)
      } else {
        this.form = null
        this.Attachment = []
      }
    },
    'form.TemplateFile'(nv) {
      console.log(nv)
      console.log(this.fillrows)
      if (nv === 'XFBarcode') {
        if (this.fillrows.length < 3) {
          this.fillrows = this.fillrows.concat(
            Array.from(Array(3 - this.fillrows.length).keys()).map(k => ({
              colLength: 2
            }))
          )
        }
        console.log(this.fillrows)
      }
    }
  },
  created() {
    console.log(this.$route)
    this.getCurType()
    GetDictionaryDetailListByCode({
      dictionaryCode: 'BarcodeType'
    })
      .then(res => {
        if (res.IsSucceed) {
          this.BarTypes = res.Data
          this.BarTypes.forEach(t => {
            GetDictionaryDetailListByParentId(t.Id).then(rlt => {
              if (rlt.IsSucceed) {
                t.subFields = rlt.Data
              }
              this.BarTypes = [].concat(this.BarTypes)
            })
          })
        }
      })
      .finally(() => {
        if (this.$route.query.id) {
          this.activeIndex = this.$route.query.id
          this.getEntity(this.$route.query.id)
        }
      })
    this.getTmplList()
  },
  destroyed() {
    clearInterval(timer)
  },
  methods: {
    async getProMapList() {
      console.log(this.form.TypeCode, 'this.form.TypeCode')
      // const list1 = await getColumnConfiguration(this.form.TypeCode)
      // const list2 = await getColumnConfiguration(this.form.TypeCode, 'plm_packages_page_list')
      // const list = [...list1, ...list2]
      const list = []
      timer = setInterval(() => {
        if (this.subFields && this.subFields.length > 0) {
          clearInterval(timer)
          this.subFields.forEach((item, index) => {
            const name = getPropsName(list, item.Value) || item.Display_Name
            this.$set(this.subFields[index], 'Display_Name', name)
            this.markerName = name
          })
        }
      }, 1000)
    },
    getCurType() {
      console.log(this.$route.query, 'this.$route.query.typeCode')
      this.typeCode = this.$route.query.typeCode
      this.typeId = this.$route.query.typeId
    },
    getTmplList() {
      this.loading = true
      return GetEntities2({ typeCode: this.typeCode })
        .then(res => {
          if (res.IsSucceed) {
            this.tmplList = res.Data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    rowFeildChange(row, field, value) {
      if (field === 'leftFieldName' || field === 'rightFieldName') {
        const nf = field === 'leftFieldName' ? 'leftShowName' : 'rightShowName'
        const subs =
          this.BarTypes.find(b => String(b.Value) === String(this.form.Type))
            ?.subFields ?? []
        row[nf] = subs.find(s => s.Value === value)?.Display_Name ?? ''
      }
      if (row.leftFieldName && row.rightFieldName) {
        row.colLength = 2
      }
    },
    tmplSelect(index) {
      console.log(index)
      this.toEdit = ''
      if (this.form && this.form.Id === index) return
      this.$router.push({
        name: 'BarcodeTemplates',
        query: {
          id: index,
          pg_redirect: this.$route.name,
          typeCode: this.typeCode,
          typeId: this.typeId
        }
      })
    },
    handleSelect(index, indexPath) {
      this.activeIndex = index
    },
    getEntity(id) {
      this.loading = true
      return GetBarCodeSettingEntity(id)
        .then(res => {
          if (res.IsSucceed) {
            this.form = {
              ...res.Data,
              Type: String(res.Data.Type),
              TemplateFile: res.Data.TemplateFile || 'Barcode'
            }
            this.getProMapList()
            try {
              JSON.parse(this.form.CssStyle)
            } catch (_) {
              this.form.CssStyle = `{
  "top":"1mm",
  "bottom":"1mm",
  "left":"1mm",
  "right":"1mm",
  "direction":"ltr"
}`
            }
            if (!this.form.CssStyle) {
              this.form.CssStyle = `{
  "top":"1mm",
  "bottom":"1mm",
  "left":"1mm",
  "right":"1mm",
  "direction":"ltr"
}`
            }
            try {
              JSON.parse(this.form.CustomSetting)
            } catch (_) {
              this.form.CustomSetting = `{
  "logo":{
    "width":"40mm",
    "height":"12mm",
    "align":"left"
  },
  "barcode":{
    "width":"20mm",
    "height":"2mmm"
  }
}`
            }
            if (!this.form.CustomSetting) {
              this.form.CustomSetting = `{
  "logo":{
    "width":"40mm",
    "height":"12mm",
    "align":"left"
  },
  "barcode":{
    "width":"20mm",
    "height":"20mm"
  }
}`
            }
            this.Attachment = []
            try {
              this.fillrows = JSON.parse(res.Data.SettingRecord).map(o => {
                const m = {
                  ...o,
                  colLength: o.leftFieldName && o.rightFieldName ? 2 : 1
                }
                const subs =
                  this.BarTypes.find(
                    b => String(b.Value) === String(this.form.Type)
                  )?.subFields ?? []
                console.log(subs)
                if (m.leftFieldName) {
                  m.leftShowName =
                    subs.find(s => s.Value === m.leftFieldName)?.Display_Name ??
                    ''
                }
                if (m.rightFieldName) {
                  m.rightShowName =
                    subs.find(s => s.Value === m.rightFieldName)
                      ?.Display_Name ?? ''
                }
                return m
              })
            } catch (err) {
              console.error(err)
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    onDelete(id) {
      this.$confirm('是否删除所选内容', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      })
        .then(() => {
          this.deleteTmpl(id)
          if (this.toEdit) {
            this.toEdit = ''
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    deleteTmpl(id) {
      return Delete1(id).then(res => {
        if (res.IsSucceed) {
          if (id === this.form.Id) {
            this.$router.push({
              name: 'BarcodeTemplates',
              query: {
                pg_redirect: this.$route.name,
                typeCode: this.typeCode,
                typeId: this.typeId
              }
            })
          }
          this.getTmplList()
        } else {
          this.$message.warning(res.Message)
        }
      })
    },
    previewUploadSucceed(response, file, fileList) {
      console.log(file)
      this.form.PreviewImage = file.raw.name ?? ''
      this.Attachment = [
        {
          File_Name: file.raw.name,
          File_Type: file.raw.type,
          File_Size: file.raw.size,
          File_Url: file.response.Data ? file.response.Data.split('*')[0] : ''
        }
      ]
    },
    swapDirection() {
      console.log(this.CssStyleObject)
      const newCss = { ...this.CssStyleObject }
      if (newCss.direction === 'rtl') {
        newCss.direction = 'ltr'
      } else {
        newCss.direction = 'rtl'
      }
      console.log(newCss)
      this.form.CssStyle = JSON.stringify(newCss)
    },
    barTypeChange(tv) {
      console.log(tv)
      const t = this.BarTypes.find(tins => String(tins.Value) === String(tv))
      if (t) {
        this.form.Dictionary_Id = t.Id
      }
      console.log(this.form)
    },
    printFrame() {
      console.log(this.$refs)
      this.$refs.frame.contentDocument.getElementById('btn')?.click()
    },
    printView() {
      console.log(this.fillrows)
      this.dialogVisible = true
      this.loading = true
    },
    frameLoaded() {
      const fields = []
      console.log(this.fillrows instanceof Array)
      this.fillrows.forEach(r => {
        const fc = []
        if (r.leftFieldName) {
          fc.push({
            label: r.leftShowName,
            value: '测试数据abc'
          })
        }
        if (r.rightFieldName) {
          fc.push({
            label: r.rightShowName,
            value: '测试数据abc'
          })
        }
        fields.push(fc)
      })
      console.log(fields, 'fields')
      // return
      setTimeout(() => {
        // console.log(this.form,"this.form");
        this.loading = false
        console.log('test', this.$refs.frame?.contentWindow?.getPrintData)
        this.$refs.frame?.contentWindow?.getPrintData &&
          this.$refs.frame?.contentWindow?.getPrintData({
            tmpl: this.form.TemplateFile, // 打印模板组件名
            type: this.form.Type, // 模板类型:包模板/构件模板
            data: {
              direction: this.CssStyleObject?.direction || 'ltr',
              logo: {
                url: this.form.LogoFileUrl,
                width: this.CustomSettingObject?.logo?.width || '40mm',
                height: this.CustomSettingObject?.logo?.height || '12mm',
                align: this.CustomSettingObject?.logo?.align || 'left'
              },
              barcode: {
                url: '',
                width: this.CustomSettingObject?.barcode?.width || '20mm',
                height: this.CustomSettingObject?.barcode?.width || '20mm'
              },
              tips: this.form.Remark,
              paper: {
                width: (this.form.Length || '80') + 'mm',
                height: (this.form.Width || '50') + 'mm',
                top: this.CssStyleObject?.top || '1mm',
                bottom: this.CssStyleObject?.bottom || '1mm',
                left: this.CssStyleObject?.left || '1mm',
                right: this.CssStyleObject?.right || '1mm'
              },
              entities: [
                {
                  id: '',
                  fields: fields
                }
              ]
            }
          })
      }, 1500)
    },
    createTmpl() {
      this.form = { PreviewImage: '', LogoFileUrl: '', TemplateFile: 'Barcode', Type: this.BarTypes && this.BarTypes.length > 0 ? this.BarTypes[0].Value : '' }
      this.toEdit = 'new'
      this.Attachment = []
      this.fillrows = [{ colLength: 2 }]
    },
    addRow() {
      this.fillrows.push({
        colLength: 1
      })
    },
    removeRow(ri) {
      this.fillrows.splice(ri, 1)
    },
    changeCols(row, colLength) {
      row.colLength = colLength
      if (colLength === 1) {
        row.rightFieldName = ''
        row.rightShowName = ''
      }
    },
    beforeAvatarUpload() {},
    handleLogoSuccess(response, file, fileList) {
      // console.log(file,"file");
      this.form.LogoFileUrl = file.response.Data
        ? file.response.Data.split('*')[0]
        : ''
      if (this.form.LogoFileUrl) {
        GetOssUrl({ url: this.form.LogoFileUrl, day: 30 }).then((res) => {
          this.form.LogoFileUrl = res.Data
          // window.open(res.Data);
        })
      }
      // console.log(this.form.LogoFileUrl,"this.form.LogoFileUrl");
    },
    submit() {
      if (this.submitLoading) return
      const t = this.BarTypes.find(
        tins => String(tins.Value) === String(this.form.Type)
      )
      if (t) {
        this.form.Dictionary_Id = t.Id
      }
      this.form.SettingRecord = JSON.stringify(this.fillrows)
      this.$refs['baseForm'].validate(valid => {
        if (valid) {
          this.saveTmpl()
        } else {
          this.$message.warning('请填写必填项目')
          return false
        }
      })
    },
    saveTmpl() {
      console.log(this.form)
      // return
      this.submitLoading = true
      let apiMethod = Addprint
      if (this.form.Id) {
        apiMethod = Edit1
      }
      apiMethod({
        TbProBarcode: {
          ...this.form,
          TypeId: this.typeId,
          TypeCode: this.typeCode
        },
        Attachment: this.Attachment
      })
        .then(res => {
          if (res.IsSucceed) {
            this.$router.push({
              name: 'BarcodeTemplates',
              query: {
                id: this.form.Id ?? res.Data,
                pg_redirect: this.$route.name,
                typeCode: this.typeCode,
                typeId: this.typeId
              }
            })
            this.$message.success('保存成功')
            this.getTmplList()
          } else {
            this.$message.warning(res.Message)
          }
        })
        .finally(() => {
          this.submitLoading = false
        })
    }
  }
}
</script>
<style lang="scss">
.print-preview-dialog {
  display: flex;
  flex-direction: column;
  .el-dialog__header {
    height: 60px;
  }
  .el-dialog__body {
    flex: auto;
    padding: 0 20px;
  }
  .el-dialog__footer {
    height: 60px;
  }
  .print-tip {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    .tips {
      font-size: 0.9em;
      color: #999;
    }
  }
}
</style>
<style lang="scss" scoped>
.f-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-bottom: 12px;
  align-items: center;
}
.avatar-uploader {
  padding: 6px;
  border: 1px solid #cec7c7;
  ::v-deep .el-upload {
    border: 1px dashed #cec7c7;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409eff;
    }
  }
  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 240px;
    height: 240px;
    line-height: 240px;
    text-align: center;
  }
  .avatar {
    width: 240px;
    height: 240px;
    display: block;
  }
}
.logo-uploader {
  padding: 6px;
  border: 1px solid #cec7c7;
  ::v-deep .el-upload {
    width: 100%;
    border: 1px dashed #cec7c7;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409eff;
    }
  }
  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 100%;
    height: 240px;
    line-height: 240px;
    text-align: center;
  }
  .avatar {
    width: 100%;
    height: 240px;
    display: block;
  }
}
.normal-set {
  background: #fff;
  min-height: 360px;
  .fields-set {
    padding: 16px;
    padding-top: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .rows {
      background: #f2f5f8;
      min-height: 200px;
      margin-bottom: 16px;
      border: 1px dashed #cec7c7;
      padding: 16px;
    }
  }
  .img-set {
    display: flex;
    flex-direction: row;
    padding: 16px;
    justify-content: space-between;
    align-items: center;
    .trans-icon {
      transform: rotate(90deg);
      color: #298dff;
      font-size: 2.4em;
      font-weight: bold;
      cursor: pointer;
      margin: 0 24px;
      &:hover {
        color: #60a8fa;
      }
    }
    .qrimg {
      flex-shrink: 0;
      background: #f2f5f8;
    }
    .logoimg {
      flex: auto;
      background: #f2f5f8;
    }
  }
}
.tmpl-list {
  margin-top: 12px;
  flex: 1;
  height: 0;
  overflow-y: auto;
  .tmpl-menu {
    border-right: none;
    .el-menu-item {
      height: 32px;
      line-height: 32px;
      .el-link {
        position: absolute;
        top: 20%;
        right: 12px;
        margin-top: -7px;
        transition: transform 0.3s;
        &:last-child {
          right: 36px;
        }
      }
    }
  }
}
.dir-row {
  display: flex;
  flex-direction: row;
  .el-form-item {
    display: flex;
    ::v-deep .el-form-item__label {
      flex-shrink: 0;
    }
    ::v-deep .el-form-item__content {
      flex: 1;
    }
  }
}
</style>

<template>
  <div class="abs100 cs-z-flex-pd16-wrap">
    <div style="display:flex;height:100%;">
      <el-aside
        class="cs-z-page-main-content"
        style="background:#FFF;margin-right:16px;width: 20vw;min-width:320px;"
      >
        <el-row :gutter="4" style="flex-shrink:0;">
          <el-col :span="17">
            <el-input
              v-model="keyword"
              placeholder="请输入内容"
              suffix-icon="el-icon-search"
            />
          </el-col>
          <el-col :span="7">
            <el-button type="primary" @click="createTemplate">新建模板</el-button>
          </el-col>
        </el-row>
        <div class="tmpl-list">
          <el-menu
            class="tmpl-menu"
            :default-active="String(activeIndex)"
          >
            <el-menu-item
              v-for="tmpl in filteredTmplList"
              :key="tmpl.Id"
              :index="tmpl.Id"
              style="padding-left:12px;"
              :title="tmpl.Name"
            >
              <div
                style="overflow:hidden;max-width:220px;text-overflow: ellipsis;"
                @click.stop="tmplSelect(tmpl.Id)"
              >
                <i class="el-icon-document" />{{ tmpl.Name }}
              </div>
              <template v-if="String(activeIndex) === tmpl.Id">
                <!--                <el-link-->
                <!--                  :underline="false"-->
                <!--                  type="primary"-->
                <!--                  @click.stop="toEdit = tmpl.Id"-->
                <!--                >-->
                <!--                  <i class="right-align-icon el-icon-edit" />-->
                <!--                </el-link>-->

                <el-link
                  :underline="false"
                  type="danger"
                  @click="deleteTemplate(tmpl.Id)"
                >
                  <i class="right-align-icon el-icon-delete" />
                </el-link>
                <el-link
                  :underline="false"
                  type="primary"
                  @click="cloneTemplate(tmpl.Id)"
                >
                  <i class="right-align-icon el-icon-copy-document" />
                </el-link>
              </template>
            </el-menu-item>
          </el-menu>
        </div>
        <div v-if="mode==2" class="flex-row justify-center flex-wrap" style="display: flex;flex-wrap: wrap">
          <!-- tid 与 defaultElementTypeProvider 中对应 -->
          <!-- 包含 class="ep-draggable-item" -->

          <div class="title">条码区</div>
          <div class="ep-draggable-item item" tid="Barcode.customImage">
            图片
          </div>
          <div class="ep-draggable-item item" tid="Barcode.Barcode_Url">
            二维码
          </div>
          <div class="title">数据区</div>
          <div class="ep-draggable-item item" tid="Barcode.ProjectName">
            项目名称
          </div>
          <div class="ep-draggable-item item" tid="Barcode.AreaPosition">
            区域
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SetupPosition">
            批次
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelType">
            构件类型
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelName">
            构件名称
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelAmount">
            数量
          </div>
          <div class="ep-draggable-item item" tid="Barcode.IsDirect">
            是否是直发件
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelWeight">
            单重（kg）
          </div>
          <div class="ep-draggable-item item" tid="Barcode.GrossWeight">
            单毛重（kg）
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelSpec">
            规格
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelMaterial">
            材质
          </div>
          <div class="ep-draggable-item item" tid="Barcode.Area">
            面积
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SteelLength">
            长度（mm，含连接件）
          </div>
          <div class="ep-draggable-item item" tid="Barcode.SerialNumber">
            管理编号
          </div>
          <div class="ep-draggable-item item" tid="Barcode.Axis">
            位置轴线
          </div>
          <div class="ep-draggable-item item" tid="Barcode.TopHeight">
            顶标高（m）
          </div>
          <div class="ep-draggable-item item" tid="Barcode.BottomHeight">
            底标高（m）
          </div>
          <div class="title">其他</div>
          <div class="ep-draggable-item item" tid="Barcode.customText">
            自定义文本
          </div>
          <div class="ep-draggable-item item" tid="Barcode.hline">
            横线
          </div>
          <div class="ep-draggable-item item" tid="Barcode.vline">
            竖线
          </div>
          <div class="ep-draggable-item item" tid="Barcode.rect">
            矩形
          </div>
          <div class="ep-draggable-item item" tid="Barcode.oval">
            椭圆
          </div>
        </div>
        <div v-if="mode==3" class="flex-row justify-center flex-wrap" style="display: flex;flex-wrap: wrap">
          <!-- tid 与 defaultElementTypeProvider 中对应 -->
          <!-- 包含 class="ep-draggable-item" -->

          <div class="title">条码区</div>
          <div class="ep-draggable-item item" tid="Barcode.customImage">
            图片
          </div>
          <div class="ep-draggable-item item" tid="Barcode.Barcode_Url">
            二维码
          </div>
          <div class="title">数据区</div>
          <div class="ep-draggable-item item" tid="Barcode.ProjectName">
            项目名称
          </div>
          <div class="ep-draggable-item item" tid="Barcode.PackageSN">
            包编号
          </div>
          <div class="ep-draggable-item item" tid="Barcode.AllWeight">
            总重（t）
          </div>
          <div class="ep-draggable-item item" tid="Barcode.Remark">
            备注
          </div>
          <div class="ep-draggable-item item" tid="Barcode.PNum">
            数量
          </div>
          <div class="ep-draggable-item item" tid="Barcode.PkgNo">
            包名称
          </div>
          <div class="title">其他</div>
          <div class="ep-draggable-item item" tid="Barcode.customText">
            自定义文本
          </div>
          <div class="ep-draggable-item item" tid="Barcode.hline">
            横线
          </div>
          <div class="ep-draggable-item item" tid="Barcode.vline">
            竖线
          </div>
          <div class="ep-draggable-item item" tid="Barcode.rect">
            矩形
          </div>
          <div class="ep-draggable-item item" tid="Barcode.oval">
            椭圆
          </div>
        </div>
      </el-aside>
      <el-container class="cs-z-page-main-content">
        <div class="header">
          <el-button type="primary" sizi="mini" @click="handlePrint">打印预览</el-button>
          <el-button type="success" sizi="mini" :loading="saveLoading" @click="saveTemplate">保存模板</el-button>
          <el-button type="danger" sizi="mini" @click="clearTemplate">清空</el-button>
          <span class="label">模板名称</span>
          <el-input v-model="form.Name" style="width: 150px" :maxlength="50" />
          <span class="label">模板布局</span>
          <el-select v-model="curPaperType" style="width: 120px" @change="changePaper">
            <el-option v-for="item in paperTypes" :key="item.type" :value="item.type" :label="item.type" />
          </el-select>
          <div v-if="curPaperType==='自定义纸张'">
            <span class="label">宽</span>
            <el-input v-model="paperWidth" type="input" class="input" @change="changePaper" />
            <span class="label">高</span>
            <el-input v-model="paperHeight" type="input" class="input" @change="changePaper" />
          </div>
          <div style="display: flex;align-items: center;margin-left: 10px">
            <i class="el-icon-zoom-out zoom-btn" @click="changeScale(false)" />
            <div class="zoom">{{ ~~(scaleValue * 100) }}%</div>
            <i class="el-icon-zoom-in zoom-btn" @click="changeScale(true)" />
          </div>
        </div>
        <!-- 设计器的 容器 -->
        <div style="margin-top: 10px;display: flex">
          <div style="flex:1;padding-left: 16px;padding-top: 16px;overflow: auto">
            <div id="hiprint-printTemplate" />
          </div>
          <div class="hinnn-layout-sider" style="width: 20vw;min-width: 300px;margin-left: 16px">
            <div id="PrintElementOptionSetting" />
          </div>
        </div>
      </el-container>
    </div>
    <input v-show="false" ref="imageUploader" type="file" accept="image/*" @change="handleFileChange">
  </div>
</template>

<script>
import { hiprint, hiPrintPlugin } from 'vue-plugin-hiprint'
import providers from './providers'
import {
  DeletePrintTemplate,
  GetPrintTemplateEntity,
  GetPrintTemplateList,
  SavePrintTemplateEntity
} from '@/api/PRO/shipment/ship-template-print'
import { paperTypes } from './config'
import { GetPreferenceSettingValue } from '@/api/sys/system-setting'
import html2canvas from 'html2canvas'
import { GetCompany } from '@/api/plm/site'
import { deepClone } from '@/utils'

hiPrintPlugin.disAutoConnect()

let hiprintTemplate
let intervalFLag = null
export default {
  name: 'ShipTemplatePrintDetail',
  data() {
    return {
      // 当前纸张
      curPaper: {
        type: '自定义纸张',
        width: 100,
        height: 60
      },
      curPaperType: '自定义纸张',
      // 纸张类型
      paperTypes: deepClone(paperTypes),
      // 自定义纸张
      paperWidth: '100',
      paperHeight: '60',
      tmplList: [],
      mode: this.$route.query.mode,
      activeIndex: '',
      keyword: '',
      toEdit: '',
      form: {
        Name: '',
        Type: this.$route.query.mode, // 2-构件条码；3-构件包
        Data: '',
        Base64Image: ''
      },
      logoUrl: require('@/assets/logo-inner.png'),
      // 缩放
      scaleValue: 1,
      scaleMax: 5,
      scaleMin: 0.5,
      saveLoading: false,
      curImageId: '',
      imageObj: {}
    }
  },
  computed: {
    filteredTmplList() {
      return this.tmplList.filter(t => t.Name.indexOf(this.keyword) > -1)
    }
  },
  mounted() {
    this.init()
    /**
       * 这里必须要在 mounted 中去构建 左侧可拖拽元素 或者 设计器
       * 因为都是把元素挂载到对应容器中, 必须要先找到该容器
       */
    this.buildLeftElement()
    this.buildDesigner()
  },
  beforeDestroy() {
    clearInterval(intervalFLag)
  },
  methods: {
    async cloneTemplate() {
      const copyTemplate = JSON.parse(JSON.stringify(this.form))
      delete copyTemplate.Id
      await SavePrintTemplateEntity(copyTemplate).then(res => {
        if (res.IsSucceed) {
          this.$message.success('复制成功')
          this.getTemplateList()
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()

        reader.onload = (e) => {
          this.imageObj[this.curImageId] = e.target.result
        }

        reader.readAsDataURL(file) // 读取文件并转换为Data URL
      }
    },
    changeScale(big) {
      let scaleValue = this.scaleValue
      if (big) {
        scaleValue += 0.1
        if (scaleValue > this.scaleMax) scaleValue = 5
      } else {
        scaleValue -= 0.1
        if (scaleValue < this.scaleMin) scaleValue = 0.5
      }
      if (hiprintTemplate) {
        // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大
        hiprintTemplate.zoom(scaleValue)
        this.scaleValue = scaleValue
      }
    },
    async getImage() {
      try {
        return await new Promise((resolve, reject) => {
          // 在浏览器空闲时执行截图操作
          const executeCapture = async() => {
            try {
              // 存储和移除边框样式
              const textElements = document.querySelectorAll('.hiprint-printElement-text-content')
              const paperElements = document.querySelectorAll('.hiprint-printPaper.design')

              // 存储原始边框样式
              const originalBorders = new Map()
              textElements.forEach((el, index) => {
                originalBorders.set(`text-${index}`, window.getComputedStyle(el).border)
                el.style.border = 'none'
              })
              paperElements.forEach((el, index) => {
                originalBorders.set(`paper-${index}`, window.getComputedStyle(el).border)
                el.style.border = 'none'
              })

              const canvas = await html2canvas(document.getElementsByClassName('hiprint-printPaper')[0], {
                useCORS: true,
                logging: false, // 关闭日志输出
                removeContainer: true, // 自动清理临时容器
                onclone: (clonedDoc) => {
                  // 在克隆文档中移除不必要的元素
                  const animations = clonedDoc.querySelectorAll('[class*="animate"]')
                  animations.forEach(el => el.remove())
                }
              })

              // 恢复原始边框样式
              textElements.forEach((el, index) => {
                el.style.border = originalBorders.get(`text-${index}`) || ''
              })
              paperElements.forEach((el, index) => {
                el.style.border = originalBorders.get(`paper-${index}`) || ''
              })

              const dataUrl = canvas.toDataURL('image/png')
              resolve(dataUrl)
            } catch (error) {
              reject(error)
            }
          }

          // 使用 requestIdleCallback 在浏览器空闲时执行
          if (window.requestIdleCallback) {
            window.requestIdleCallback(executeCapture, { timeout: 5000 })
          } else {
            setTimeout(executeCapture, 0)
          }
        })
      } finally {
      }
    },
    getLogo() {
      GetCompany().then(res => {
        this.logoUrl = res.Data.Icon
      })
    },
    tmplSelect(id) {
      this.toEdit = ''
      this.activeIndex = id
      if (this.form && this.form.Id === id) return
      this.loadTemplate(id)
    },
    deleteTemplate(id) {
      this.$confirm('是否删除所选内容', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true
      })
        .then(() => {
          DeletePrintTemplate({ id }).then(res => {
            if (res.IsSucceed) {
              this.$message.success('删除成功')
              this.getTemplateList()
            } else {
              this.$message.error(res.Message)
            }
          })
          if (this.toEdit) {
            this.toEdit = ''
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async init() {
      // 初始化 provider
      this.getLogo()
      const provider = providers.find(i => i.value == this.mode)
      hiprint.init({
        providers: [provider.f]
      })
      this.getTemplateList()
    },

    /**
       * 构建左侧可拖拽元素
       * 注意: 可拖拽元素必须在 hiprint.init() 之后调用
       * 而且 必须包含 class="ep-draggable-item" 否则无法拖拽进设计器
       */
    buildLeftElement() {
      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))
    },
    buildDesigner(template = {}) {
      // eslint-disable-next-line no-undef
      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建
      hiprintTemplate = new hiprint.PrintTemplate({
        template,
        settingContainer: '#PrintElementOptionSetting', // 元素参数容器
        onImageChooseClick: (target) => {
          this.curImageId = target.el.id
          clearInterval(intervalFLag)
          this.$refs.imageUploader.click()
          intervalFLag = setInterval(() => {
            target.refresh(this.imageObj[target.el.id])
          }, 100)
        }
      })
      // 构建 并填充到 容器中
      hiprintTemplate.design('#hiprint-printTemplate')
      hiprintTemplate.setPaper(this.paperWidth, this.paperHeight)
    },
    handlePrint() {
      // 打印数据，key 对应 元素的 字段名
      const printData = {}
      const printArr = hiprintTemplate.getJson().panels[0].printElements
      printArr.forEach(item => {
        if (item.options.field == 'Table') {
          printData[item.options.field] = JSON.parse(item.options.testData)
        } else {
          console.log(item)
          printData[item.options.field] = item.options.testData || item.options
        }
      })
      console.log(printData)

      // let printData = hiprintTemplate.getJson()
      // 参数: 打印时设置 左偏移量，上偏移量
      const options = { leftOffset: -1, topOffset: -1 }
      // 扩展
      const ext = {
        callback: () => {
          console.log('浏览器打印窗口已打开')
        }
        // styleHandler: () => {
        //   // 重写 文本 打印样式
        //   return "<style>.hiprint-printElement-text{color:red !important;}</style>";
        // }
      }
      // 调用浏览器打印
      hiprintTemplate.print(printData, options, ext)
    },
    changePaper() {
      let temp = this.paperTypes.find(i => i.type === this.curPaperType)
      temp = deepClone(temp)
      if (this.curPaperType === '自定义纸张') {
        hiprintTemplate.setPaper(this.paperWidth, this.paperHeight)
      } else {
        hiprintTemplate.setPaper(temp.width, temp.height)
      }
    },
    // 新建模板
    createTemplate() {
      this.form = {
        Name: '',
        Type: this.mode, // 1-发货单
        Data: '',
        Base64Image: ''
      }
      this.clearTemplate()
    },
    // 保存模板
    async saveTemplate() {
      if (this.saveLoading) return
      this.saveLoading = true
      try {
        this.form.Base64Image = await this.getImage()
        if (!this.form.Name) {
          this.$message.error('请输入模板名称')
          return
        }
        const json = hiprintTemplate.getJson()
        this.form.Data = JSON.stringify(json)
        const res = await SavePrintTemplateEntity(this.form)
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.getTemplateList()
        } else {
          this.$message.error(res.Message)
        }
      } catch (error) {
        this.$message.error('保存失败')
        console.error('保存模板失败:', error)
      } finally {
        this.saveLoading = false
      }
    },
    // 加载模板
    async loadTemplate(id) {
      this.clearTemplate()
      const res = await GetPrintTemplateEntity({ id })
      this.form = res.Data
      const parseData = JSON.parse(res.Data.Data)
      try {
        const index = parseData.panels[0].printElements.findIndex(i => i.options.field === 'Logo')
        parseData.panels[0].printElements[index].options.src = this.logoUrl
      } catch (e) {}
      console.log()
      const template = parseData
      this.buildDesigner(template)

      // 匹配纸张
      const { width, height } = template.panels[0]
      const matchedPaper = this.paperTypes.find(i => i.width == width & i.height == height)
      if (matchedPaper) {
        this.curPaper = matchedPaper
      } else {
        this.curPaper = {
          type: '自定义纸张',
          width,
          height
        }
      }
      this.curPaperType = this.curPaper.type
      this.paperWidth = width
      this.paperHeight = height
      this.changePaper()
    },
    // 清空模板
    clearTemplate() {
      $('#hiprint-printTemplate').empty() // 先清空, 避免重复构建
      this.buildDesigner()
    },

    async getTemplateList() {
      const res = await GetPrintTemplateList({
        type: this.mode
      })
      this.tmplList = res.Data
    }
  }
}
</script>

<style lang="scss" scoped>
  .title{
    min-width: 100%;
    padding-left: 8px;
    font-size: 18px;
    margin-top: 20px;
  }
  ::-webkit-scrollbar {
    width: 0px;
    height: 8px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    // box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ddd;
  }
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: #ededed;
  }
  .item {
    color: rgba(34,40,52,0.65);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 10px 10px;
    width: 112px;
    height: 44px;
    border-radius: 4px 4px 4px 4px;
    border: 1px dashed #D0D3DB;
    font-size: 14px;
  }
  .label{
    margin: 0 5px 0 10px;
  }
  /*::v-deep{*/
  /*    .hiprint-option-item-settingBtn{*/
  /*        background: #0ba1f8;*/
  /*    }*/
  /*}*/
  .header{
    display: flex;
    flex-wrap: wrap;
    row-gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    color: rgba(34, 40, 52, 0.85);
    font-size: 12px;
    .input{
      width: 100px;
    }
    .zoom{
      margin: 0 10px;
      border: 1px solid #D0D3DB;
      border-radius: 4px;
      width: 100px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
    }
    .zoom-btn{
      padding: 5px;
      font-size: 19px;
    }
  }
  ::v-deep{
    .hinnn-layout-sider{
      *{
        color: rgba(34, 40, 52, 0.85);
      }
      /*input[placeholder="请输入图片地址"]  {*/
      /*  width: 100%!important;*/
      /*  & + button{*/
      /*    display: none;*/
      /*  }*/
      /*}*/
      input,textarea,select{
        border-radius: 4px!important;
        border: 1px solid #D0D3DB!important;
      }
      input,select{
        height: 32px!important;
        line-height: 32px;
      }
      .hiprint-option-item-settingBtn{
        background-color: #298DFF;
        border-radius: 4px;
        height: 30px;
        color: #ffffff;
        cursor: pointer;
      }
      .hiprint-option-item-deleteBtn{
        background-color: #FB6B7F;
      }
    }

  }
  .tmpl-list {
    margin-top: 12px;
    overflow-y: auto;
    min-height: 130px;
    .tmpl-menu {
      border-right: none;
      .el-menu-item {
        height: 32px;
        line-height: 32px;
        .el-link {
          position: absolute;
          top: 20%;
          right: 12px;
          margin-top: -7px;
          transition: transform 0.3s;
          &:last-child {
            right: 36px;
          }
        }
      }
    }
  }
</style>


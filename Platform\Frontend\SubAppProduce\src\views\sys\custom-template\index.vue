<template>
  <div class="app-container abs100">
    <bt-tree :data="treeData" :props="treeProps" :default-selected-key="treeDefaultSelectedKey" node-key="Code" @node-click="nodeClick" />
    <el-card class="card">
      <div class="content">
        <el-form ref="form" inline>
          <el-button type="primary" @click="openDialog()">新增</el-button>
        </el-form>
        <bt-table class="bt-table" code="customTemplate" :config="config" @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange">
          <template #Template_Name="{row}">
            <el-button type="text" style="font-size: 14px" @click="downloadExcel(row)">{{ row.Template_Name }}</el-button>
          </template>
          <template #Export_Type="{row}">
            {{ ['excel','zip','sheet'][(row.Export_Type || 1) - 1] }}
          </template>
          <template #actions="{row}">
            <el-button type="text" @click="openDialog(row)">编辑</el-button>
            <el-button type="text" :loading="row.loading" @click="downloadExcel(row)">导出</el-button>
            <el-button type="text" @click="deleteTemplate(row)">删除</el-button>
          </template>
        </bt-table>
      </div>
    </el-card>
    <el-dialog
      v-if="dialogVisible"
      v-dialogDrag
      class="plm-custom-dialog"
      title="导入自定义表"
      :visible.sync="dialogVisible"
      width="600px"
      top="10vh"
    >
      <el-form ref="templateForm" label-width="100px" :model="templateForm" :rules="rules">
        <el-form-item label="模板名称" prop="Template_Name">
          <el-input v-model="templateForm.Template_Name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="导出类型" prop="Export_Type">
          <el-select v-model="templateForm.Export_Type" placeholder="请选择">
            <el-option label="excel" :value="1" />
            <el-option label="zip" :value="2" />
            <el-option label="sheet" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板上传" prop="Template_Url">
          <bt-upload
            ref="upload"
            type="drag"
            class="cs-upload"
            :limit="1"
            accept=".xlsx"
            :on-success="uploadSuccess"
            :file-list.sync="fileList"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  DeleteReportTemplateEntity,
  GetReportTemplateList,
  GetReportTemplateTypeList,
  SaveReportTemplateEntity
} from '@/api/sys/custom-template'
import { GetOssUrl } from '@/api/sys'
import { getFileNameFromUrl } from '@/utils/file'

const templateForm = {
  Template_Name: '',
  Template_Url: '',
  Export_Type: 1
}

export default {
  name: 'CustomTemplate',
  data() {
    return {
      treeData: [],
      treeProps: {
        label: 'Name',
        id: 'Code'
      },
      treeDefaultSelectedKey: '',
      dialogVisible: false,
      templateForm: { ...templateForm },
      rules: {
        Template_Name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        Export_Type: [
          { required: true, message: '请选择导出类型', trigger: 'blur' }
        ],
        Template_Url: [
          { required: true, message: '请上传文件', trigger: 'blur' }
        ]
      },
      fileList: [],
      curNode: null,
      config: {
        loading: false,
        tableData: [],
        operateOptions: {
          width: 180,
          align: 'center'
        },
        sortRemote: false
      }
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      GetReportTemplateTypeList().then(res => {
        this.treeData = res.Data.map(item => {
          item.loading = false
          return item
        })
        if (this.treeData.length) {
          this.treeDefaultSelectedKey = this.treeData[0].Code
          this.curNode = this.treeData[0]
          this.getTableData()
        }
      })
    },
    getTableData() {
      this.config.loading = true
      GetReportTemplateList({ TypeCode: this.curNode.Code }).then(res => {
        this.config.tableData = res.Data
      }).finally(() => {
        this.config.loading = false
      })
    },
    handleCurrentChange(val) {
      this.config.currentPage = val
      this.getTableData()
    },
    handleSizeChange(val) {
      this.config.pageSize = val
      this.getTableData()
    },
    nodeClick(node) {
      this.curNode = node
      this.getTableData(node.Code)
    },
    openDialog(row) {
      if (row) {
        this.fileList = [{
          url: row.Template_Url,
          name: getFileNameFromUrl(row.Template_Url)
        }]
        this.templateForm = {
          Template_Name: row.Template_Name,
          Template_Url: row.Template_Url,
          Export_Type: row.Export_Type,
          Id: row.Id
        }
      }
      if (this.curNode) {
        this.dialogVisible = true
      } else {
        this.$message.error('请选择模板类型')
      }
    },
    closeDialog() {
      this.templateForm = { ...templateForm }
      this.fileList = []
      this.dialogVisible = false
    },
    uploadSuccess() {
      this.templateForm.Template_Url = this.fileList[0].url
    },
    async handleSubmit() {
      await this.$refs.templateForm.validate()
      SaveReportTemplateEntity({
        ...this.templateForm,
        Type_Code: this.curNode.Code
      }).then(res => {
        if (res.IsSucceed) {
          this.$message.success('保存成功')
          this.getTableData()
          this.closeDialog()
        } else {
          this.$message.error(res.Message)
        }
      })
    },
    downloadExcel(row) {
      row.loading = true
      GetOssUrl({
        url: row.Template_Url
      }).then(res => {
        window.open(res.Data, '_blank')
      }).finally(() => {
        row.loading = false
      })
    },
    deleteTemplate(row) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteReportTemplateEntity({
          Id: row.Id
        }).then(res => {
          if (res.IsSucceed) {
            this.$message.success('删除成功')
            this.getTableData()
          } else {
            this.$message.error(res.Message)
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container{
  display: flex;
  .card{
    flex:1;
    height: 100%;
    margin-left: 16px;
    .content{
      display: flex;
      flex-direction: column;
      row-gap: 12px;
      height: 100%;
    }
    .bt-table{
      flex:1;
    }
  }
}
</style>

<template>
  <div class="bim-gantt">
    <header>
      <el-row :gutter="20">
        <el-col :span="16">
          <div class="proj-title">
            <div class="flag">
              <i class="iconfont icon-gantt" />
            </div>
            <h3>{{ plan.Name }}</h3>
            <el-link
              class="set-icon"
              :underline="false"
              type="primary"
              icon="el-icon-setting"
            />
          </div>
        </el-col>
        <el-col :span="8" style="text-align:right;padding-right:20px;">
          <el-button type="warning">导入</el-button>
          <el-button type="success">导出</el-button>
          <el-button type="primary" @click="save">保存</el-button>
          <el-button type="primary" icon="el-icon-s-check">提交审核</el-button>
        </el-col>
      </el-row>
    </header>
    <div class="toolbar">
      <el-row class="gantt-toolbar" :gutter="12">
        <el-col :span="6" class="grouparea" style="padding-left:20px;">
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-plus"
            @click="addTask"
          >新增</el-button>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-minus"
          >删除</el-button>
          <el-button
            type="warning"
            size="mini"
            icon="el-icon-set-up"
          >WBS</el-button>
          <el-button
            type="success"
            size="mini"
            icon="el-icon-document"
          >详情</el-button>
        </el-col>
        <el-col :span="6" class="grouparea">
          <span>大纲级别</span>
          <el-slider
            size="mini"
            :step="1"
            :max="5"
            :show-tooltip="false"
            style="width:60px;"
          />
          <span>前锋线</span>
          <el-switch />
          <span>聚光灯</span>
          <el-switch />
        </el-col>
        <el-col :span="4" class="grouparea">
          <el-input
            placeholder="请输入内容"
            size="mini"
            suffix-icon="el-icon-search"
            style="border-width:0px !important;width:160px;"
          />
          <el-badge is-dot :hidden="false" style="margin-left:6px;">
            <el-link :underline="false" icon="el-icon-attract" />
          </el-badge>
        </el-col>
        <el-col :span="4" class="grouparea">
          <el-link :underline="false" icon="el-icon-tickets" />
          <el-link :underline="false" icon="el-icon-date" />
          <el-link :underline="false" icon="el-icon-time" />
          <el-dropdown trigger="click">
            <el-link :underline="false" icon="el-icon-more-outline" />
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="calendar">日历设置</el-dropdown-item>
              <el-dropdown-item command="target">目标计划</el-dropdown-item>
              <el-dropdown-item
                command="fillset"
              >填报任务设置</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-col>
        <el-col :span="3" class="grouparea">
          <el-link :underline="false" icon="el-icon-zoom-out" />
          <el-link :underline="false" icon="el-icon-zoom-in" />
          <el-link :underline="false" icon="el-icon-full-screen" />
        </el-col>
      </el-row>
    </div>
    <div class="gantt-container">
      <div
        id="gantt-chart"
        ref="gantt"
        :class="{ hasbaseline: opts.showBaseline }"
      />
    </div>
  </div>
</template>
<script>
import { Gantt } from '@components/gantt'
import '@components/gantt/codebase/dhtmlxgantt.css'
import {
  mergeOpts,
  setGanttDefaultOpts,
  mapPlanData,
  createEmptyTask
} from './utils'
import TestData from './testdata.json'
import './index.scss'
export default {
  name: 'BimGantt',
  props: {
    wbsMode: {
      type: Boolean,
      default: false
    },
    editMode: {
      type: Boolean,
      default: false
    },
    taskMap: {
      type: Object,
      default: () => ({})
    },
    linkMap: {
      type: Object,
      default: () => ({})
    },
    plan: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      gantt: null, // 甘特实例
      opts: {
        wbsMode: false,
        editMode: false,
        showBaseline: false //
      },
      UniqId: 0 // 维护一个非重复ID
    }
  },
  watch: {
    opts(nv, ov) {
      if (nv.wbsMode !== ov.wbsMode) {
        this.onWbsModeChange()
      }
    }
  },
  created() {
    this.opts = mergeOpts(this.opts, {
      wbsMode: this.wbsMode,
      editMode: this.editMode
    })
  },
  mounted() {
    setTimeout(() => {
      const data = this.plan.Plan_Data ?? {
        tasks: [],
        links: []
      }
      this.createGanttInstance(data)
    }, 100)
  },
  methods: {
    changeWbsMode(isWbs) {
      this.opts = mergeOpts(this.opts, {
        wbsMode: isWbs
      })
    },
    onWbsModeChange() {
      console.log('onWbsModeChange...')
      const data = {}
      this.createGanttInstance(data)
    },
    createGanttInstance(data) {
      if (this.gantt) {
        this.gantt.destructor()
        this.gantt = null
      }
      this.gantt = Gantt.getGanttInstance()
      setGanttDefaultOpts(this.gantt, this.opts)
      // 自定义双击任务动作
      this.gantt.attachEvent('onTaskDblClick', (id, e) => {
        const task = this.gantt.getTask(id)
        this.openTaskDetail(task)
        return false
      })
      this.gantt.init('gantt-chart')
      if (Object.prototype.toString.call(data) === '[object Object]') {
        data = mapPlanData(data, this.taskMap, this.linkMap)
        console.log(data)
        this.gantt.parse(data)
      }
    },
    openTaskDetail(task) {
      console.log(task)
    },
    toggleBaseline() {
      this.opts.showBaseline = !this.opts.showBaseline
      this.createGanttInstance({
        tasks: TestData.Plan_Data.data,
        links: TestData.Plan_Data.links
      })
    },
    save() {
      console.log(this.gantt.serialize())
    },
    addTask() {
      const id = new Date().getTime().toString()
      const parentId = this.gantt.getSelectedId()
      let parent
      if (parentId) {
        parent = this.gantt.getTask(parentId)
      }

      if (parent && parent.type !== 'project') {
        return this.$message.warning('只能在wbs条目下新建子作业')
      }
      this.UniqId++ // id自增
      const task = createEmptyTask({
        type: this.opts.wbsMode,
        id: this.UniqId,
        parent: parent
      })
      console.log(task)
      this.gantt.addTask(task)
    }
  }
}
</script>

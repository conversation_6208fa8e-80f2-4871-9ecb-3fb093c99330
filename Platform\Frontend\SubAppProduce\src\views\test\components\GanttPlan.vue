<template>
  <div :class="'gantt-plan ' + (baseline ? 'hasbaseline' : '')">
    <div ref="gantthere" class="gantthere" />
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleDialogClose"
    >
      <span>这是一段信息</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="dialogVisible = false"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { gantt, Gantt } from '@components/gantt'
import '@components/gantt/codebase/dhtmlxgantt.css'
import './gantt-plan.scss'
import { gantdata } from './gant-data'
export default {
  name: 'GanttPlan',
  props: {
    // 计划模式，0 为查看，1 为新增
    mode: {
      type: Number,
      default: 0
    },
    // wbs 模式，默认为 false，则为 task 作业模式
    wbs: {
      type: Boolean,
      default: false
    },
    // 是否显示项目基线
    baseline: {
      type: Boolean,
      default: false
    },
    // 计算基准时间
    basedate: {
      type: Date,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      deepLevel: 0, // 最深层级
      svgLayer: false, // 前锋线绘制层
      showFowardLine: false, // 是否显示前锋线
      keyword: '', // filter 任务关键字
      ganttElement: null, // 甘特图绑定元素
      currentTaskMode: '', // 当前目录层级模式
      config: {
        // 甘特图插件 services
        date_format: '%Y-%m-%d', // 解析数据以及像服务发送的格式
        task_date: '%Y-%m-%d',
        scale_height: 60 // 时间尺度的高度和grid高度
      },
      originData: null, // 原始甘特数据备份
      cachedSettings: null, // 缓存gantt services
      visibilityData: {
        data: [],
        links: []
      }, // 显示用甘特数据
      zoomConfig: {
        levels: [
          // day
          {
            name: 'day',
            scale_height: 60,
            scales: [
              { unit: 'month', format: '<strong>%Y年%m月</strong>' },
              {
                unit: 'day',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%d</span>'
              }
            ]
          },
          // week
          {
            name: 'week',
            scale_height: 60,
            scales: [
              {
                unit: 'week',
                format: function(date) {
                  var dateToStr = gantt.date.date_to_str('%m月%d')
                  var endDate = gantt.date.add(date, +6, 'day')
                  var weekNum = gantt.date.date_to_str('%W')(date)
                  return (
                    gantt.date.date_to_str('%Y年 ')(date) +
                    dateToStr(date) +
                    ' - ' +
                    dateToStr(endDate) +
                    ' 第' +
                    weekNum +
                    '周'
                  )
                }
              },
              {
                unit: 'day',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%D</span>'
              }
            ]
          },
          // month
          {
            name: 'month',
            scale_height: 60,
            scales: [
              { unit: 'year', format: '<strong>%Y年</strong>' },
              {
                unit: 'month',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%m月</span>'
              }
            ]
          },
          // quarter
          {
            name: 'quarter',
            scale_height: 60,
            scales: [
              { unit: 'year', format: '<strong>%Y年</strong>' },
              {
                unit: 'month',
                step: 3,
                format: date => {
                  const month = date.getMonth()
                  const sec = parseInt(month / 3) + 1
                  return `第${sec}季度`
                }
              }
            ]
          },
          // year
          {
            name: 'year',
            scale_height: 60,
            scales: [
              {
                unit: 'year',
                step: 5,
                format: date => {
                  const y = date.getFullYear()
                  var endy = gantt.date.add(date, +5, 'year')
                  return `<strong>${y} - ${gantt.date.date_to_str('%Y')(
                    endy
                  )}年</strong>`
                }
              },
              {
                unit: 'year',
                step: 1,
                format: '<span data-date=%Y-%m-%d>%Y</span>'
              }
            ]
          }
        ],
        element: function() {
          return gantt.$root.querySelector('.gantt_task')
        }
      }
    }
  },
  watch: {
    currentTaskMode(newMode, oldMode) {
      if(!oldMode) return
      // task或project
      console.log(newMode)
      this.foldTaskType(newMode)
    }
  },
  created() {
    console.log('gantplan component created...')
    // props的wbs模式初始化currentTaskMode
    this.currentTaskMode = this.wbs ? 'project' : 'task'
    // 初始化配置
    for (const k in this.config) {
      console.log(k)
      gantt.config[k] = this.config[k]
    }
    gantt.config.show_task_cells = true
    // 多语言
    gantt.i18n.setLocale('cn')
    // 树图标
    gantt.templates.grid_folder = function(item) {
      return ''
    }
    // 叶节点图标
    gantt.templates.grid_file = function(item) {
      return '<i class="el-icon-link" style="margin-top:18px;margin-right:4px;color:#298DFF;"></i>'
    }
    gantt.config.columns = [
      {
        name: 'wbs',
        label:
          '<i class="el-icon-film" style="font-size:1.6em;cursor:pointer;" id="column-services"></i>',
        width: 60,
        template: gantt.getWBSCode
      },
      {
        name: 'text',
        label: `<div id="wbs2task"><span class="tag ${
          this.wbs ? 'wbs' : 'task'
        }"><span>${this.wbs ? 'W' : '作'}</span></span>作业名称</div>`,
        tree: true,
        width: 150,
        resize: true,
        template: task => {
          return task.text
        }
      },
      {
        name: 'start_date',
        label: '开始时间',
        align: 'center',
        width: 150,
        resize: true
      },
      {
        name: 'planned_end',
        label: '结束时间',
        align: 'center',
        width: 150,
        resize: true
      },
      {
        name: 'duration',
        label: '工期(天)',
        align: 'center',
        width: 70,
        resize: true
      }
    ]
    console.log(gantt.config)
    gantt.plugins({
      auto_scheduling: true,
      marker: true,
      tooltip: true,
      fullscreen: true
    })
    // gantt.services.auto_types = true
    gantt.config.auto_scheduling = true
    gantt.config.auto_scheduling_compatibility = true
    // gantt.services.open_split_tasks = true
    // gantt.locale.labels.section_split = 'Display'
    gantt.config.layout = {
      css: 'gantt_container',
      cols: [
        {
          width: 500,
          min_width: 500,
          rows: [
            {
              view: 'grid',
              scrollX: 'gridScroll',
              scrollable: true,
              scrollY: 'scrollVer'
            },
            { view: 'scrollbar', id: 'gridScroll', group: 'horizontal' }
          ]
        },
        { resizer: true, width: 1 },
        {
          rows: [
            { view: 'timeline', scrollX: 'scrollHor', scrollY: 'scrollVer' },
            { view: 'scrollbar', id: 'scrollHor', group: 'horizontal' }
          ]
        },
        { view: 'scrollbar', id: 'scrollVer' }
      ]
    }
    if (this.baseline) {
      gantt.config.row_height = 48
      gantt.config.bar_height = gantt.config.row_height / 2 - 4
      gantt.addTaskLayer({
        renderer: {
          render: function draw_planned(task) {
            if (task.start_date_plan && task.end_date_plan) {
              var sizes = gantt.getTaskPosition(
                task,
                task.start_date_plan,
                task.end_date_plan
              )
              var el = document.createElement('div')
              el.className = 'baseline'
              el.style.left = sizes.left + 'px'
              el.style.width = sizes.width + 'px'
              el.style.top = sizes.top + gantt.config.bar_height + 13 + 'px'
              return el
            }
            return false
          }
        }
      })
      gantt.attachEvent('onTaskLoading', function(task) {
        task.start_date_plan = gantt.date.parseDate(
          task.start_date_plan,
          'xml_date'
        )
        task.end_date_plan = gantt.date.parseDate(
          task.end_date_plan,
          'xml_date'
        )
        return true
      })
    }
    gantt.attachEvent('onGanttReady', () => {
      this.bindGanttEvents()
      this.highlightScaleCell()

      var tooltips = gantt.ext.tooltips
      gantt.templates.tooltip_text = (start, end, task) => {
        return `<div class="custom-plan-tooltip"><header><h3>${
          task.text
        }</h3><p>${gantt.date.date_to_str(gantt.config.task_date)(
          new Date(task.start_date_plan)
        )} ~ ${gantt.date.date_to_str(gantt.config.task_date)(
          new Date(task.end_date_plan)
        )}</p></header><div>
					<b>工期：</b>${task.duration}<br/>
					<b>进度：</b>${(task.progress_act * 100).toFixed(1)}%<br/>
          <b>备注：</b>${task.remarks ?? ''}</div></div>`
      }
    })
    gantt.attachEvent('onBeforeTaskDisplay', (id, task) => {
      return this.hasKeywordInTask(this.keyword, task)
    })
    gantt.attachEvent('onAfterTaskDrag', (id, mode, e) => {
      console.log('...............')
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onGanttScroll', (left, top) => {
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onSmartRender', (left, top) => {
      this.drawSvg()
      this.highlightScaleCell()
    })
    gantt.attachEvent('onGanttRender', () => {
      this.drawSvg()
      this.highlightScaleCell()
      this.bindGanttEvents()
    })
    // when gantt is expended to full screen
    gantt.attachEvent('onExpand', function() {
      const wrapper = gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 9999
    })

    // when gantt exited the full screen mode
    gantt.attachEvent('onCollapse', function() {
      const wrapper = gantt.ext.fullscreen.getFullscreenElement()
      wrapper.style.zIndex = 1
    })

    // 日期单元格
    gantt.templates.scale_cell_class = date => {
      if (
        gantt.date.date_to_str(gantt.config.task_date)(this.basedate) ==
        gantt.date.date_to_str(gantt.config.task_date)(date)
      ) {
        return 'basedate'
      }
    }
    // 时间线单元格
    gantt.templates.timeline_cell_class = (task, date) => {
      if (
        gantt.date.date_to_str(gantt.config.task_date)(this.basedate) ==
        gantt.date.date_to_str(gantt.config.task_date)(date)
      ) {
        return 'basedate'
      }
    }
    // 阻止默认的编辑弹出框
    gantt.attachEvent('onTaskDblClick', function(id, e) {
      // any custom logic here
      console.log(id, e)
      return false
    })
    // 阻止默认关系线弹出框
    gantt.attachEvent('onLinkDblClick', function(id, e) {
      // any custom logic here
      console.log(id, e)
      return false
    })
    // 缩放配置
    gantt.ext.zoom.init(this.zoomConfig)
    gantt.ext.zoom.setLevel('day')
    gantt.$zoomToFit = false
  },
  mounted() {
    this.ganttElement = this.$refs.gantthere
    gantt.init(this.ganttElement)
    //
    window.onresize = () => {
      this.drawSvg()
      this.highlightScaleCell()
    }
    // 异步模拟
    setTimeout(() => {
      this.originData = gantdata
      if (this.wbs) {
        this.visibilityData = JSON.parse(
          JSON.stringify(
            Object.assign(
              {},
              {
                data: gantdata.data.filter(
                  d => d.type === gantt.config.types.project
                ),
                links: []
              }
            )
          )
        )
      } else {
        this.visibilityData = JSON.parse(JSON.stringify(gantdata))
      }

      gantt.parse(this.visibilityData)
      this.computeLevelDeep()
      // 添加日期线
      gantt.addMarker({
        id: 'dujianguangTestMarker',
        start_date: this.basedate,
        css: 'today',
        text: '状态日期',
        title:
          '状态日期: ' +
          gantt.date.date_to_str(gantt.config.task_date)(this.basedate)
      })
    })
  },
  methods: {
    handleDialogClose() {},
    zoom(step) {
      if (this.cachedSettings) {
        this.restoreConfig()
        gantt.render()
        this.cachedSettings = null
      }
      if (step > 0) {
        gantt.ext.zoom.zoomIn()
        gantt.$zoomToFit = false
      } else {
        gantt.ext.zoom.zoomOut()
        gantt.$zoomToFit = false
      }
    },
    fit() {
      gantt.$zoomToFit = !gantt.$zoomToFit
      if (!gantt.$zoomToFit) {
        this.restoreConfig()
        gantt.render()
        return
      }
      this.saveConfig()
      var project = gantt.getSubtaskDates()
      var areaWidth = gantt.$task.offsetWidth
      var scaleConfigs = this.zoomConfig.levels

      for (var i = 0; i < scaleConfigs.length; i++) {
        var columnCount = this.getUnitsBetween(
          project.start_date,
          project.end_date,
          scaleConfigs[i].scales[scaleConfigs[i].scales.length - 1].unit,
          scaleConfigs[i].scales[0].step ?? 1
        )
        if ((columnCount + 2) * gantt.config.min_column_width <= areaWidth) {
          break
        }
      }
      if (i == scaleConfigs.length) {
        i--
      }
      gantt.ext.zoom.setLevel(scaleConfigs[i].name)
      this.applyConfig(scaleConfigs[i], project)
    },
    applyConfig(config, dates) {
      gantt.config.scales = config.scales
      var lowest_scale = config.scales.reverse()[0]

      if (dates && dates.start_date && dates.end_date) {
        gantt.config.start_date = gantt.date.add(
          dates.start_date,
          -1,
          lowest_scale.unit
        )
        gantt.config.end_date = gantt.date.add(
          gantt.date[lowest_scale.unit + '_start'](dates.end_date),
          2,
          lowest_scale.unit
        )
      } else {
        gantt.config.start_date = gantt.config.end_date = null
      }

      // restore the previous scroll position
      if (config.scroll_position) {
        setTimeout(() => {
          gantt.scrollTo(config.scroll_position.x, config.scroll_position.y)
        }, 4)
      }
    },
    saveConfig() {
      var config = gantt.config
      this.cachedSettings = {}
      this.cachedSettings.scales = config.scales
      this.cachedSettings.start_date = config.start_date
      this.cachedSettings.end_date = config.end_date
      this.cachedSettings.scroll_position = gantt.getScrollState()
    },
    restoreConfig() {
      this.applyConfig(this.cachedSettings)
    },
    getUnitsBetween(from, to, unit, step) {
      var start = new Date(from)
      var end = new Date(to)
      var units = 0
      while (start.valueOf() < end.valueOf()) {
        units++
        start = gantt.date.add(start, step, unit)
      }
      return units
    },
    toggleScreen() {
      gantt.ext.fullscreen.toggle()
    },
    // 高亮多行缩放级别日期
    highlightScaleCell() {
      document.querySelectorAll('.gantt_scale_line').forEach(e => {
        e.querySelectorAll('span').forEach(s => {
          if (
            s.getAttribute('data-date') ===
            gantt.date.date_to_str(gantt.config.task_date)(this.basedate)
          ) {
            s.parentNode.classList.add('basedate')
          }
        })
      })
    },
    computeLevelDeep() {
      let max = 0
      gantt.eachTask(t => {
        if (t.$level > max) max = t.$level
      })
      this.deepLevel = max
      this.$parent.maxLevel = this.deepLevel
    },
    bindGanttEvents() {
      this.bindColumnConfigEvent()
      this.bindWBS2TaskEvent()
    },
    // 列配置事件绑定
    bindColumnConfigEvent() {
      const cfgBtn = this.ganttElement.querySelector('#column-services')
      console.log(cfgBtn)
      cfgBtn.addEventListener('click', () => {
        this.dialogVisible = true
      })
    },
    // WBS 切换 task 事件
    bindWBS2TaskEvent() {
      this.ganttElement
        .querySelector('#wbs2task>span.tag')
        .addEventListener('click', () => {
          const lvl = this.currentTaskMode == 'task' ? 'project' : 'task'
          console.log(lvl)
          this.currentTaskMode = lvl
          this.$nextTick(() => {
            const toBtn = this.ganttElement.querySelector('#wbs2task>span.tag')
            if (lvl === 'task') {
              toBtn.classList.remove('wbs')
              toBtn.classList.add('task')
              toBtn.innerHTML = '<span>作</span>'
            } else {
              console.log(toBtn)
              toBtn.classList.remove('task')
              toBtn.classList.add('wbs')
              toBtn.innerHTML = '<span>W</span>'
            }
          })
        })
    },
    // 任务关键字筛选
    filterKeyword(key) {
      this.keyword = key
      gantt.refreshData()
    },
    hasKeywordInTask(k, task) {
      const taskStr = JSON.stringify(task)
      return taskStr.indexOf(k) > -1
    },
    // 折叠树层级
    foldTaskLevel(level) {
      // gantt.eachTask(task => {
      //   gantt.open(task.id)
      //   if(level!==0){
      //     if(task.$level >= this.deepLevel - level) {
      //       console.log(task, this.deepLevel - level)
      //       gantt.close(task.id)
      //     }
      //   }
      // })

      // 批量更新效率较高
      gantt.batchUpdate(() => {
        var tasks = gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          const position = gantt.getTaskPosition(task)
          // console.log(position)
          task.$open = true
          if (level !== 0) {
            if (task.$level >= this.deepLevel - level) {
              task.$open = false
            }
          }
          gantt.updateTask(task.id)
        }
      })
    },
    // 折叠树类型
    foldTaskType(showType) {
      gantt.batchUpdate(() => {
        var tasks = gantt.getTaskByTime()
        for (var i = 0; i < tasks.length; i++) {
          var task = tasks[i]
          task.$open = true
          if (showType === 'project') {
            const childs = tasks.filter(t => t.parent === task.id)
            // 折叠 project 类,并且无project类子元素
            if (
              task.type == 'project' &&
              !childs.find(c => c.type == 'project')
            ) {
              task.$open = false
            }
          }
          gantt.updateTask(task.id)
        }
      })
    },
    // 绘制前锋线
    drawProcessLine() {
      if (!this.basedate) return
      console.log(gantt)

      this.drawSvg()
    },
    drawSvg() {
      // this.svgLayer = document.createElementNS(
      //   'http://www.w3.org/2000/svg',
      //   'svg'
      // )
      // const wrapper = this.ganttElement.querySelector('.gantt_task_bg')
      // console.log(wrapper)
      // wrapper.appendChild(this.svgLayer)
      var strID = 'dujianguangtest'
      var strContainerCss = '.gantt_links_area'
      // "gantt_layout_cell  timeline_cell gantt_layout_outer_scroll gantt_layout_outer_scroll_vertical
      // gantt_layout_outer_scroll gantt_layout_outer_scroll_horizontal";
      var divContainer = document.querySelector(strContainerCss)
      if (!this.showFowardLine) {
        // 清除前锋线
        var ele = document.getElementById(strID)
        // console.log(ele)
        if (ele) {
          divContainer.removeChild(ele)
        }
        this.svgLayer = null
        return
      }
      var pathAll = ''
      var s1 = gantt.config.start_date
      if (s1 == null) s1 = gantt._min_date
      const s2 = this.basedate
      var date_status = new Date(s2.getFullYear(), s2.getMonth(), s2.getDate())
      // s2 = new Date(2018,9,9);
      var timeMilliSecond = date_status.getTime() - s1.getTime()
      var days = parseInt(timeMilliSecond / (1000 * 60 * 60 * 24))
      var divMarker = document.querySelector(
        "[data-marker-id='dujianguangTestMarker']"
      )
      // 状态日期的x坐标，天数*最小列宽度
      // var startLeft=days*gantt.services.min_column_width ;//divDataDate.offsetLeft;
      console.dir(divMarker)
      if (!divMarker) return
      console.log('continue drawing...')
      const startLeft = divMarker.offsetLeft
      let lefPre = startLeft
      let topPre = 4
      var colorLine = 'red' // 线条颜色
      var $divtasks = document.querySelectorAll('.gantt_bars_area')
      console.log($divtasks)
      // 杜建光 按照top排序 否则错位
      var arr = []
      $divtasks[0].querySelectorAll('.gantt_bar_task').forEach(n => {
        arr.push(n)
      })
      console.log(arr)
      arr.sort(function(a, b) {
        var ai = parseFloat(a.style.top, 10)
        var bi = parseFloat(b.style.top, 10)
        if (ai > bi) {
          return 1
        } else if (ai < bi) {
          return -1
        } else {
          return 0
        }
      })

      // 原计算路径函数
      function calcLineOrigin() {
        // var left = sizes.left + sizes.width * task.progress_clc;
        var left = sizes.left + sizes.width * task.progress
        var top = sizes.top + gantt.config.bar_height + 13
        var topMiddle = top
        console.log(task, sizes)
        // 如果计划不为里程碑计划和WBS计划，可以绘制
        if (
          task.type != gantt.config.types.milestone &&
          task.type != gantt.config.types.project
        ) {
          if (
            (left <= startLeft && task.progress >= 0 && task.progress < 1) ||
            (left >= startLeft && task.progress > 0 && task.progress <= 1)
          ) {
            pathAll =
              pathAll +
              '<path d="M' +
              lefPre +
              ',' +
              topPre +
              ' L' +
              left +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            console.log(topPre, topMiddle, topPre)
            var strCircle =
              '<circle cx="' +
              left +
              '" cy="' +
              (topPre + (topMiddle - topPre) / 2) +
              '" r="3" style="stroke:' +
              colorLine +
              '; fill:none;" />'
            if (left != startLeft) pathAll = pathAll + strCircle
            pathAll =
              pathAll +
              '<path d="M' +
              left +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              ' L' +
              startLeft +
              ',' +
              top +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            lefPre = startLeft
            topPre = top
          } else {
            pathAll =
              pathAll +
              '<path d="M' +
              lefPre +
              ',' +
              topPre +
              ' L' +
              startLeft +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            var strCircle =
              '<circle cx="' +
              startLeft +
              '" cy="' +
              (topPre + (topMiddle - topPre) / 2) +
              '" r="3" style="stroke:' +
              colorLine +
              '; fill:none;" />'
            // if (left != startLeft) pathAll = pathAll + strCircle;
            pathAll =
              pathAll +
              '<path d="M' +
              startLeft +
              ',' +
              (topPre + (topMiddle - topPre) / 2) +
              ' L' +
              startLeft +
              ',' +
              top +
              '"style="stroke:' +
              colorLine +
              '; fill:none;"/>'
            lefPre = startLeft
            topPre = top
          }
        } else {
          pathAll =
            pathAll +
            '<path d="M' +
            lefPre +
            ',' +
            topPre +
            ' L' +
            startLeft +
            ',' +
            topMiddle +
            '"style="stroke:' +
            colorLine +
            '; fill:none;"/>'
          var strCircle =
            '<circle cx="' +
            startLeft +
            '" cy="' +
            topMiddle +
            '" r="3" style="stroke:' +
            colorLine +
            '; fill:none;" />'
          // if (left != startLeft) pathAll = pathAll + strCircle;
          pathAll =
            pathAll +
            '<path d="M' +
            startLeft +
            ',' +
            topMiddle +
            ' L' +
            startLeft +
            ',' +
            top +
            '"style="stroke:' +
            colorLine +
            '; fill:none;"/>'
          lefPre = startLeft
          topPre = top
        }
      }
      // 新计算路径函数
      function calcLineNew() {
        // console.log(sizes, task)
        let moveto, lineto
        moveto = [
          sizes.left + sizes.width * task.progress,
          sizes.top + topPre + gantt.config.bar_height / 2
        ]
        lineto = [startLeft, sizes.top + topPre]
        pathAll += `<path d="M${moveto[0]},${moveto[1]} L${lineto[0]}, ${lineto[1]} Z" style="stroke:${colorLine}; fill:none;"/>`
        pathAll += `<path d="M${moveto[0]}, ${moveto[1]} L${
          lineto[0]
        }, ${lineto[1] +
          gantt.config.bar_height} Z" style="stroke:${colorLine}; fill:none;"/>`
        let circlePad = 0
        if (moveto[0] < startLeft) {
          circlePad = -3
        } else if (moveto[0] > startLeft) {
          circlePad = 3
        }
        pathAll += `<circle cx="${moveto[0] + circlePad}" cy="${
          moveto[1]
        }" r="3" style="stroke:${colorLine}; fill:none;" />`
      }
      // for(var i=0;i<$divtasks[0].childElementCount;i++){
      for (var i = 0; i < arr.length; i++) {
        // var $ele=$divtasks[0].children[i];
        var ele = arr[i]

        var taskid = ele.getAttribute('task_id')
        // 根据当前任务画前锋线
        var task = gantt.getTask(taskid)
        // 根据对比计划画前锋线
        console.log(task.start_date_plan)
        task.start_date_contrast = task.start_date_plan // 先改造个数据
        // 如果有对比计划显示，可以绘制前锋线
        if (task.start_date_contrast) {
          var planned_start = gantt.date.add(
            new Date(task.start_date_contrast),
            0,
            'day'
          )
          var planned_end = gantt.date.add(
            gantt.calculateEndDate(
              new Date(task.start_date_contrast),
              task.duration
            ),
            0,
            'day'
          )
          console.log(planned_start, planned_end)
          var sizes = gantt.getTaskPosition(task, planned_start, planned_end)
          // 计算路径
          // calcLineOrigin()
          calcLineNew()
        }
      }
      // console.log(pathAll)
      // gantt_data_area
      var divDataArea = document.querySelector('.gantt_task_bg')
      console.log(divDataArea)
      var svgWidth = divDataArea.offsetWidth
      var svgHeight = divDataArea.offsetHeight
      var QFline =
        '<svg style="width:' +
        svgWidth +
        'px;height:' +
        svgHeight +
        'px;">' +
        pathAll +
        '</svg>'
      var ele = document.getElementById(strID)
      if (ele) {
        divContainer.removeChild(ele)
      }
      var linechild = document.createElement('div')
      linechild.id = 'dujianguangtest'
      linechild.className = 'divLine'
      // 把前锋线插入到图层中
      linechild.innerHTML = QFline
      divContainer.appendChild(linechild)
      this.svgLayer = document.querySelector('#' + strID).querySelector('svg')
      console.log(divContainer)
    }
  }
}
</script>
